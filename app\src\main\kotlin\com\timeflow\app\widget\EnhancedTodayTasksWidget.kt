package com.timeflow.app.widget

import android.app.PendingIntent
import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.util.Log
import android.widget.RemoteViews
import com.timeflow.app.R
import com.timeflow.app.ui.MainActivity
import com.timeflow.app.widget.layout.WidgetSizeManager
import com.timeflow.app.widget.layout.WidgetLayoutAdapter
import com.timeflow.app.widget.theme.WidgetThemeManager
import kotlinx.coroutines.*
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import com.timeflow.app.widget.data.TodayTasksData
import com.timeflow.app.widget.data.DisplayTask

/**
 * 增强版今日待办小组件 - Material Design 3现代化设计
 * 支持交互式复选框、优先级颜色指示器、进度可视化
 * 支持多种尺寸和深色模式
 */
class EnhancedTodayTasksWidget : AppWidgetProvider() {

    companion object {
        private const val TAG = "EnhancedTodayTasksWidget"
        
        // 动作常量
        const val ACTION_TASK_TOGGLE = "com.timeflow.app.widget.ACTION_TASK_TOGGLE"
        const val ACTION_ADD_TASK = "com.timeflow.app.widget.ACTION_ADD_TASK"
        const val EXTRA_TASK_ID = "task_id"
        const val EXTRA_WIDGET_ID = "widget_id"

        // 性能优化
        private const val UPDATE_DEBOUNCE_DELAY = 300L
        private val updateJobs = ConcurrentHashMap<Int, Job>()
        private val widgetCache = ConcurrentHashMap<String, RemoteViews>()
        private var lastUpdateTime = 0L
        private val scope = CoroutineScope(Dispatchers.Main + SupervisorJob())
        
        // 日期格式化
        private val dateFormat = SimpleDateFormat("MM月dd日", Locale.getDefault())
        private val weekdayFormat = SimpleDateFormat("EEEE", Locale.getDefault())
    }

    override fun onUpdate(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetIds: IntArray
    ) {
        Log.d(TAG, "🔄 开始更新增强版今日待办小组件")

        for (appWidgetId in appWidgetIds) {
            updateAppWidgetWithDebounce(context, appWidgetManager, appWidgetId)
        }
    }

    override fun onAppWidgetOptionsChanged(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetId: Int,
        newOptions: Bundle?
    ) {
        Log.d(TAG, "📐 小组件尺寸变化: appWidgetId=$appWidgetId")
        updateAppWidgetWithDebounce(context, appWidgetManager, appWidgetId)
    }

    override fun onReceive(context: Context, intent: Intent) {
        super.onReceive(context, intent)
        
        when (intent.action) {
            ACTION_TASK_TOGGLE -> {
                handleTaskToggle(context, intent)
            }
            ACTION_ADD_TASK -> {
                handleAddTask(context, intent)
            }
        }
    }

    /**
     * 处理任务完成状态切换
     */
    private fun handleTaskToggle(context: Context, intent: Intent) {
        val taskId = intent.getStringExtra(EXTRA_TASK_ID)
        val widgetId = intent.getIntExtra(EXTRA_WIDGET_ID, AppWidgetManager.INVALID_APPWIDGET_ID)
        
        Log.d(TAG, "🔄 切换任务状态: taskId=$taskId, widgetId=$widgetId")
        
        if (taskId != null && widgetId != AppWidgetManager.INVALID_APPWIDGET_ID) {
            // 在后台线程中处理任务状态更新
            scope.launch {
                try {
                    // 这里应该调用实际的任务管理服务
                    // taskRepository.toggleTaskCompletion(taskId)
                    
                    // 更新小组件显示
                    val appWidgetManager = AppWidgetManager.getInstance(context)
                    updateAppWidget(context, appWidgetManager, widgetId)
                    
                    Log.d(TAG, "✅ 任务状态更新成功")
                } catch (e: Exception) {
                    Log.e(TAG, "❌ 任务状态更新失败", e)
                }
            }
        }
    }

    /**
     * 处理添加任务
     */
    private fun handleAddTask(context: Context, intent: Intent) {
        Log.d(TAG, "➕ 处理添加任务请求")
        
        // 跳转到主应用的任务添加页面
        val mainIntent = Intent(context, MainActivity::class.java).apply {
            putExtra("navigate_to", "add_task")
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        context.startActivity(mainIntent)
    }

    /**
     * 带防抖的小组件更新方法
     */
    private fun updateAppWidgetWithDebounce(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetId: Int
    ) {
        // 取消之前的更新任务
        updateJobs[appWidgetId]?.cancel()

        // 创建新的防抖更新任务
        updateJobs[appWidgetId] = scope.launch {
            delay(UPDATE_DEBOUNCE_DELAY)
            try {
                updateAppWidget(context, appWidgetManager, appWidgetId)
            } catch (e: Exception) {
                Log.e(TAG, "更新小组件失败: appWidgetId=$appWidgetId", e)
            } finally {
                updateJobs.remove(appWidgetId)
            }
        }
    }

    /**
     * 更新小组件
     */
    private fun updateAppWidget(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetId: Int
    ) {
        try {
            val currentTime = System.currentTimeMillis()

            // 性能优化：避免过于频繁的更新
            if (currentTime - lastUpdateTime < 100) {
                Log.d(TAG, "⏭️ 跳过过于频繁的更新")
                return
            }
            lastUpdateTime = currentTime

            Log.d(TAG, "🔄 开始更新增强版今日待办小组件")

            // 获取小组件尺寸
            val options = appWidgetManager.getAppWidgetOptions(appWidgetId)
            val minWidth = options.getInt(AppWidgetManager.OPTION_APPWIDGET_MIN_WIDTH, 250)
            val minHeight = options.getInt(AppWidgetManager.OPTION_APPWIDGET_MIN_HEIGHT, 150)

            // 确定小组件尺寸
            val sizeManager = WidgetSizeManager.getInstance(context)
            val widgetSize = sizeManager.determineWidgetSize(minWidth, minHeight)
            
            // 获取主题管理器
            val themeManager = WidgetThemeManager.getInstance(context)
            val isDarkMode = themeManager.isSystemInDarkMode()
            
            Log.d(TAG, "小组件尺寸: $widgetSize, 深色模式: $isDarkMode")

            // 生成缓存键
            val cacheKey = "${appWidgetId}_${widgetSize}_${isDarkMode}"

            // 检查缓存
            widgetCache[cacheKey]?.let { cachedViews ->
                Log.d(TAG, "📦 使用缓存的小组件视图")
                appWidgetManager.updateAppWidget(appWidgetId, cachedViews)
                return
            }

            // 选择合适的布局
            val layoutAdapter = WidgetLayoutAdapter(context)
            val layoutId = R.layout.widget_today_tasks_simplified

            val views = RemoteViews(context.packageName, layoutId)

            // 获取今日任务数据
            val tasksData = getTodayTasksData(context)

            // 设置日期信息
            setupDateInfo(views)

            // 设置任务列表
            setupTaskList(context, views, tasksData, widgetSize, themeManager)

            // 设置进度信息
            setupProgressInfo(views, tasksData, themeManager)

            // 设置交互事件
            setupInteractions(context, views, appWidgetId, tasksData)

            // 应用响应式布局
            layoutAdapter.adaptLayout(views, widgetSize, WidgetLayoutAdapter.LayoutType.TODAY_TASKS)

            // 缓存视图（限制缓存大小）
            if (widgetCache.size < 10) {
                widgetCache[cacheKey] = views
                Log.d(TAG, "💾 缓存小组件视图: $cacheKey")
            }

            appWidgetManager.updateAppWidget(appWidgetId, views)
            Log.d(TAG, "✅ 小组件更新完成")

        } catch (e: Exception) {
            Log.e(TAG, "小组件更新失败，使用备用方案", e)
            handleUpdateError(context, appWidgetManager, appWidgetId, e)
        }
    }

    /**
     * 设置日期信息
     */
    private fun setupDateInfo(views: RemoteViews) {
        try {
            val calendar = Calendar.getInstance()
            val dayOfWeek = getWeekdayText(calendar.get(Calendar.DAY_OF_WEEK))
            val dayOfMonth = calendar.get(Calendar.DAY_OF_MONTH).toString()

            safeSetTextViewText(views, R.id.widget_weekday, dayOfWeek)
            safeSetTextViewText(views, R.id.widget_date, dayOfMonth)
            
            Log.d(TAG, "📅 日期信息设置完成: $dayOfWeek $dayOfMonth")
        } catch (e: Exception) {
            Log.w(TAG, "设置日期信息失败", e)
        }
    }

    /**
     * 获取星期文本
     */
    private fun getWeekdayText(dayOfWeek: Int): String {
        return when (dayOfWeek) {
            Calendar.SUNDAY -> "周日"
            Calendar.MONDAY -> "周一"
            Calendar.TUESDAY -> "周二"
            Calendar.WEDNESDAY -> "周三"
            Calendar.THURSDAY -> "周四"
            Calendar.FRIDAY -> "周五"
            Calendar.SATURDAY -> "周六"
            else -> "周日"
        }
    }

    /**
     * 设置任务列表
     */
    private fun setupTaskList(
        context: Context,
        views: RemoteViews,
        tasksData: TodayTasksData,
        widgetSize: WidgetSizeManager.WidgetSize,
        themeManager: WidgetThemeManager
    ) {
        try {
            val sizeManager = WidgetSizeManager.getInstance(context)
            val config = sizeManager.getLayoutConfig(widgetSize)
            val displayTasks = tasksData.getDisplayTasks()
            val maxTasks = minOf(config.maxItems, displayTasks.size, 5)

            Log.d(TAG, "📝 设置任务列表: 显示 $maxTasks 个任务")

            // 设置任务项
            for (i in 1..5) {
                val task = if (i <= maxTasks) displayTasks.getOrNull(i - 1) else null
                setupTaskItem(context, views, i, task, themeManager)
            }

            // 设置心情emoji
            setupMoodEmoji(views, tasksData)

        } catch (e: Exception) {
            Log.e(TAG, "设置任务列表失败", e)
        }
    }

    /**
     * 设置单个任务项
     */
    private fun setupTaskItem(
        context: Context,
        views: RemoteViews,
        taskIndex: Int,
        task: DisplayTask?,
        themeManager: WidgetThemeManager
    ) {
        try {
            val containerId = getTaskContainerId(taskIndex)
            val taskId = getTaskTextId(taskIndex)
            val checkboxId = getTaskCheckboxId(taskIndex)
            val priorityBarId = getTaskPriorityBarId(taskIndex)

            if (task != null) {
                // 显示任务
                safeSetViewVisibility(views, containerId, android.view.View.VISIBLE)
                safeSetTextViewText(views, taskId, task.title)
                
                // 设置复选框状态
                val checkboxDrawable = if (task.isCompleted) {
                    R.drawable.widget_checkbox_checked
                } else {
                    R.drawable.widget_checkbox_unchecked
                }
                safeSetImageViewResource(views, checkboxId, checkboxDrawable)
                
                // 设置优先级颜色
                val priorityColor = themeManager.getPriorityColor(task.priority)
                safeSetBackgroundColor(views, priorityBarId, priorityColor)

                Log.d(TAG, "任务${taskIndex}设置成功: ${task.title}, 完成: ${task.isCompleted}")
            } else {
                // 隐藏任务容器
                safeSetViewVisibility(views, containerId, android.view.View.GONE)
            }
        } catch (e: Exception) {
            Log.e(TAG, "设置任务${taskIndex}失败", e)
        }
    }

    /**
     * 设置心情emoji
     */
    private fun setupMoodEmoji(views: RemoteViews, tasksData: TodayTasksData) {
        try {
            val completionPercentage = tasksData.getCompletionPercentage()
            val emoji = when (completionPercentage) {
                in 0..25 -> "😴"
                in 26..50 -> "🙂"
                in 51..75 -> "😊"
                in 76..100 -> "🎉"
                else -> "😊"
            }
            safeSetTextViewText(views, R.id.widget_mood_emoji, emoji)
            Log.d(TAG, "😊 心情emoji设置: $emoji (完成度: $completionPercentage%)")
        } catch (e: Exception) {
            Log.w(TAG, "设置心情emoji失败", e)
        }
    }

    /**
     * 设置进度信息
     */
    private fun setupProgressInfo(
        views: RemoteViews,
        tasksData: TodayTasksData,
        themeManager: WidgetThemeManager
    ) {
        try {
            // 设置完成计数
            safeSetTextViewText(views, R.id.widget_completed_count, tasksData.totalCompleted.toString())
            safeSetTextViewText(views, R.id.widget_total_count, tasksData.totalTasks.toString())
            
            // 设置进度条
            val progress = tasksData.getCompletionPercentage()
            safeSetProgressBar(views, R.id.widget_progress_bar, progress)
            
            // 设置进度文本
            val progressText = "${tasksData.totalCompleted} / ${tasksData.totalTasks} 已完成"
            safeSetTextViewText(views, R.id.widget_progress_text, progressText)

            Log.d(TAG, "📊 进度信息设置完成: $progressText ($progress%)")
        } catch (e: Exception) {
            Log.w(TAG, "设置进度信息失败", e)
        }
    }

    /**
     * 设置交互事件
     */
    private fun setupInteractions(
        context: Context,
        views: RemoteViews,
        appWidgetId: Int,
        tasksData: TodayTasksData
    ) {
        try {
            // 设置任务复选框点击事件
            val displayTasks = tasksData.getDisplayTasks()
            for (i in 1..minOf(displayTasks.size, 5)) {
                val task = displayTasks.getOrNull(i - 1)
                if (task != null) {
                    val checkboxId = getTaskCheckboxId(i)
                    val toggleIntent = createTaskToggleIntent(context, task.id, appWidgetId)
                    safeSetOnClickPendingIntent(views, checkboxId, toggleIntent)
                }
            }

            // 设置添加任务按钮点击事件
            val addTaskIntent = createAddTaskIntent(context, appWidgetId)
            safeSetOnClickPendingIntent(views, R.id.widget_add_task_button, addTaskIntent)

            // 设置整个小组件点击事件（跳转到任务页面）
            val mainIntent = createMainAppIntent(context, "tasks")
            safeSetOnClickPendingIntent(views, R.id.widget_container, mainIntent)

            Log.d(TAG, "🔗 交互事件设置完成")
        } catch (e: Exception) {
            Log.e(TAG, "设置交互事件失败", e)
        }
    }

    /**
     * 创建任务切换Intent
     */
    private fun createTaskToggleIntent(context: Context, taskId: String, appWidgetId: Int): PendingIntent {
        val intent = Intent(context, EnhancedTodayTasksWidget::class.java).apply {
            action = ACTION_TASK_TOGGLE
            putExtra(EXTRA_TASK_ID, taskId)
            putExtra(EXTRA_WIDGET_ID, appWidgetId)
        }
        return PendingIntent.getBroadcast(
            context,
            taskId.hashCode(),
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
    }

    /**
     * 创建添加任务Intent
     */
    private fun createAddTaskIntent(context: Context, appWidgetId: Int): PendingIntent {
        val intent = Intent(context, EnhancedTodayTasksWidget::class.java).apply {
            action = ACTION_ADD_TASK
            putExtra(EXTRA_WIDGET_ID, appWidgetId)
        }
        return PendingIntent.getBroadcast(
            context,
            appWidgetId,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
    }

    /**
     * 创建主应用Intent
     */
    private fun createMainAppIntent(context: Context, page: String): PendingIntent {
        val intent = Intent(context, MainActivity::class.java).apply {
            putExtra("navigate_to", page)
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        return PendingIntent.getActivity(
            context,
            page.hashCode(),
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
    }

    /**
     * 获取今日任务数据
     */
    private fun getTodayTasksData(context: Context): TodayTasksData {
        return try {
            // 这里应该从实际的数据源获取数据
            // val dataProvider = TodayTasksDataProvider(context)
            // dataProvider.getTodayTasksData()
            
            // 临时使用模拟数据
            createMockTasksData()
        } catch (e: Exception) {
            Log.w(TAG, "获取任务数据失败，使用默认数据", e)
            createDefaultTasksData()
        }
    }

    /**
     * 创建模拟任务数据
     */
    private fun createMockTasksData(): TodayTasksData {
        val tasks = listOf(
            DisplayTask("1", "完成项目报告", false, "HIGH"),
            DisplayTask("2", "回复重要邮件", false, "MEDIUM"),
            DisplayTask("3", "准备明天的会议", true, "LOW"),
            DisplayTask("4", "健身锻炼", false, "MEDIUM"),
            DisplayTask("5", "阅读技术文章", false, "LOW")
        )
        
        return TodayTasksData(
            tasks = tasks,
            totalTasks = tasks.size,
            totalCompleted = tasks.count { it.isCompleted },
            totalIncomplete = tasks.count { !it.isCompleted }
        )
    }

    /**
     * 创建默认任务数据
     */
    private fun createDefaultTasksData(): TodayTasksData {
        return TodayTasksData(
            tasks = emptyList(),
            totalTasks = 0,
            totalCompleted = 0,
            totalIncomplete = 0
        )
    }

    /**
     * 处理更新错误
     */
    private fun handleUpdateError(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetId: Int,
        error: Exception
    ) {
        try {
            Log.d(TAG, "🔧 使用错误恢复方案")
            
            val views = RemoteViews(context.packageName, R.layout.widget_today_tasks_medium)
            val defaultTasksData = createDefaultTasksData()
            
            setupDateInfo(views)
            safeSetTextViewText(views, R.id.widget_mood_emoji, "😊")
            safeSetTextViewText(views, R.id.widget_progress_text, "0 / 0 已完成")
            
            // 设置基本点击事件
            val mainIntent = createMainAppIntent(context, "tasks")
            safeSetOnClickPendingIntent(views, R.id.widget_container, mainIntent)
            
            appWidgetManager.updateAppWidget(appWidgetId, views)
            Log.d(TAG, "✅ 错误恢复方案应用成功")
        } catch (e2: Exception) {
            Log.e(TAG, "错误恢复方案也失败", e2)
        }
    }

    // 安全操作方法
    private fun safeSetTextViewText(views: RemoteViews, viewId: Int, text: String) {
        try {
            views.setTextViewText(viewId, text)
        } catch (e: Exception) {
            Log.w(TAG, "设置TextView文本失败: viewId=$viewId", e)
        }
    }

    private fun safeSetViewVisibility(views: RemoteViews, viewId: Int, visibility: Int) {
        try {
            views.setViewVisibility(viewId, visibility)
        } catch (e: Exception) {
            Log.w(TAG, "设置View可见性失败: viewId=$viewId", e)
        }
    }

    private fun safeSetImageViewResource(views: RemoteViews, viewId: Int, resId: Int) {
        try {
            views.setImageViewResource(viewId, resId)
        } catch (e: Exception) {
            Log.w(TAG, "设置ImageView资源失败: viewId=$viewId", e)
        }
    }

    private fun safeSetBackgroundColor(views: RemoteViews, viewId: Int, color: Int) {
        try {
            views.setInt(viewId, "setBackgroundColor", color)
        } catch (e: Exception) {
            Log.w(TAG, "设置背景颜色失败: viewId=$viewId", e)
        }
    }

    private fun safeSetProgressBar(views: RemoteViews, viewId: Int, progress: Int) {
        try {
            views.setProgressBar(viewId, 100, progress, false)
        } catch (e: Exception) {
            Log.w(TAG, "设置进度条失败: viewId=$viewId", e)
        }
    }

    private fun safeSetOnClickPendingIntent(views: RemoteViews, viewId: Int, pendingIntent: PendingIntent) {
        try {
            views.setOnClickPendingIntent(viewId, pendingIntent)
        } catch (e: Exception) {
            Log.w(TAG, "设置点击事件失败: viewId=$viewId", e)
        }
    }

    // ID获取方法
    private fun getTaskContainerId(index: Int): Int = when (index) {
        1 -> R.id.widget_task_container_1
        2 -> R.id.widget_task_container_2
        3 -> R.id.widget_task_container_3
        4 -> R.id.widget_task_container_4
        5 -> R.id.widget_task_container_5
        else -> R.id.widget_task_container_1
    }

    private fun getTaskTextId(index: Int): Int = when (index) {
        1 -> R.id.widget_task_1
        2 -> R.id.widget_task_2
        3 -> R.id.widget_task_3
        4 -> R.id.widget_task_4
        5 -> R.id.widget_task_5
        else -> R.id.widget_task_1
    }

    private fun getTaskCheckboxId(index: Int): Int = when (index) {
        1 -> R.id.widget_checkbox_1
        2 -> R.id.widget_checkbox_2
        3 -> R.id.widget_checkbox_3
        4 -> R.id.widget_checkbox_4
        5 -> R.id.widget_checkbox_5
        else -> R.id.widget_checkbox_1
    }

    private fun getTaskPriorityBarId(index: Int): Int = when (index) {
        1 -> R.id.widget_priority_bar_1
        2 -> R.id.widget_priority_bar_2
        3 -> R.id.widget_priority_bar_3
        4 -> R.id.widget_priority_bar_4
        5 -> R.id.widget_priority_bar_5
        else -> R.id.widget_priority_bar_1
    }

    override fun onDeleted(context: Context, appWidgetIds: IntArray) {
        super.onDeleted(context, appWidgetIds)
        // 清理缓存和取消任务
        appWidgetIds.forEach { appWidgetId ->
            updateJobs[appWidgetId]?.cancel()
            updateJobs.remove(appWidgetId)
            widgetCache.keys.removeAll { key -> key.startsWith("${appWidgetId}_") }
        }
        Log.d(TAG, "🗑️ 清理小组件缓存: ${appWidgetIds.contentToString()}")
    }

    override fun onDisabled(context: Context) {
        super.onDisabled(context)
        // 清理所有缓存和任务
        updateJobs.values.forEach { it.cancel() }
        updateJobs.clear()
        widgetCache.clear()
        Log.d(TAG, "🗑️ 清理所有小组件缓存")
    }
}

