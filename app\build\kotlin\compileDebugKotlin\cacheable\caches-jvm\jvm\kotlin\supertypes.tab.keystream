com.timeflow.app.MainActivity$com.timeflow.app.TimeFlowApplication0com.timeflow.app.TimeFlowApplication.ReleaseTree,com.timeflow.app.data.ai.model.MessageSender*com.timeflow.app.data.ai.model.MessageType-com.timeflow.app.data.ai.model.AttachmentType)com.timeflow.app.data.ai.model.ActionType,com.timeflow.app.data.ai.model.MessageIntent1com.timeflow.app.data.ai.model.ConversationStatus-com.timeflow.app.data.ai.model.TaskDifficulty(com.timeflow.app.data.ai.model.TimeOfDay1com.timeflow.app.data.ai.model.RecommendationType7com.timeflow.app.data.ai.model.RecommendationImportance.com.timeflow.app.data.ai.model.InsightCategory)com.timeflow.app.data.ai.model.SentStatusIcom.timeflow.app.data.algorithm.PeriodPredictionAlgorithm.CycleRegularity.com.timeflow.app.data.analytics.TrendDirection,com.timeflow.app.data.analytics.HealthStatus$com.timeflow.app.data.db.AppDatabase-com.timeflow.app.data.entity.Goal.$serializer4com.timeflow.app.data.entity.GoalSubTask.$serializer.com.timeflow.app.data.entity.Habit.$serializer4com.timeflow.app.data.entity.HabitRecord.$serializer%com.timeflow.app.data.entity.Priority9com.timeflow.app.data.entity.ReflectionEntity.$serializer-com.timeflow.app.data.entity.Task.$serializer)com.timeflow.app.data.model.AiConfigModel(com.timeflow.app.data.model.GoalPriority+com.timeflow.app.data.model.ReviewFrequency)com.timeflow.app.data.model.GoalTimeFrame(com.timeflow.app.data.model.CategoryType,com.timeflow.app.data.model.TemplateCategory$com.timeflow.app.data.model.GoalType&com.timeflow.app.data.model.Difficulty)com.timeflow.app.data.model.HabitCategory.com.timeflow.app.data.model.HabitFrequencyType+com.timeflow.app.data.model.HabitDifficulty-com.timeflow.app.data.model.HistoryDataSource*com.timeflow.app.data.model.DataConfidence6com.timeflow.app.data.model.ConflictResolutionStrategy(com.timeflow.app.data.model.ConflictType0com.timeflow.app.data.model.CycleRegularityLevel$com.timeflow.app.data.model.MoodType0com.timeflow.app.data.model.NotificationItemType)com.timeflow.app.data.model.PaymentMethod)com.timeflow.app.data.model.PaymentStatus*com.timeflow.app.data.model.MembershipPlan4com.timeflow.app.data.model.PaymentOrder.$serializer,com.timeflow.app.data.model.PaymentException9com.timeflow.app.data.model.PaymentException.NetworkError?com.timeflow.app.data.model.PaymentException.ConfigurationError;com.timeflow.app.data.model.PaymentException.SignatureError9com.timeflow.app.data.model.PaymentException.OrderExpired;com.timeflow.app.data.model.PaymentException.AmountMismatch:com.timeflow.app.data.model.PaymentException.UserCancelled4com.timeflow.app.data.model.PaymentException.Unknown$com.timeflow.app.data.model.Priority*com.timeflow.app.data.model.RecurrenceType:com.timeflow.app.data.model.RecurrenceSettings.$serializer+com.timeflow.app.data.model.RecurringPeriod'com.timeflow.app.data.model.MonthlyType'com.timeflow.app.data.model.WeekOfMonth.com.timeflow.app.data.model.EndCondition.Never/com.timeflow.app.data.model.EndCondition.ByDate0com.timeflow.app.data.model.EndCondition.ByCount&com.timeflow.app.data.model.TimeFilter(com.timeflow.app.data.model.ReminderType1com.timeflow.app.data.model.ProgressThresholdType$com.timeflow.app.data.model.ViewMode+com.timeflow.app.data.model.TaskType.NORMAL'com.timeflow.app.data.model.TaskType.AI.com.timeflow.app.data.model.TaskType.RECURRING-com.timeflow.app.data.model.TaskType.FLOATING)com.timeflow.app.data.model.TaskGroupType,com.timeflow.app.data.model.TimeConflictType(com.timeflow.app.data.model.WishCategory&com.timeflow.app.data.model.WishStatus*<EMAIL>=com.timeflow.app.data.notification.GoalDeadlineReminderWorker5com.timeflow.app.data.repository.AiTaskRepositoryImpl<com.timeflow.app.data.repository.EmotionRecordRepositoryImpl4com.timeflow.app.data.repository.EventRepositoryImpl3com.timeflow.app.data.repository.GoalRepositoryImpl;com.timeflow.app.data.repository.GoalTemplateRepositoryImpl4com.timeflow.app.data.repository.HabitRepositoryImpl:com.timeflow.app.data.repository.KanbanBoardRepositoryImpl;com.timeflow.app.data.repository.KanbanColumnRepositoryImpl9com.timeflow.app.data.repository.MedicationRepositoryImpl<com.timeflow.app.data.repository.PendingDeletion.$serializer3com.timeflow.app.data.repository.TaskRepositoryImpl<com.timeflow.app.data.repository.TimeAnalyticsRepositoryImpl=com.timeflow.app.data.repository.UserPreferenceRepositoryImpl3com.timeflow.app.data.repository.WishRepositoryImpl)com.timeflow.app.di.LocalAnalyticsTracker&com.timeflow.app.di.LocalCrashReporter2com.timeflow.app.di.DataStoreModule.ThemeDataStore7com.timeflow.app.di.DataStoreModule.MedicationDataStore9com.timeflow.app.di.DataStoreModule.NotificationDataStore com.timeflow.app.di.ViewModelKey*com.timeflow.app.di.WorkManagerInitializer5com.timeflow.app.initializer.RecurringTaskInitializer/com.timeflow.app.receiver.BootCompletedReceiver2com.timeflow.app.receiver.DailyReviewAlarmReceiver2com.timeflow.app.receiver.FocusTimerActionReceiver,com.timeflow.app.receiver.HabitAlarmReceiver+com.timeflow.app.receiver.TaskAlarmReceiverBcom.timeflow.app.receiver.TaskPersistentNotificationActionReceiver*com.timeflow.app.service.AutoBackupService*com.timeflow.app.service.FocusTimerService3com.timeflow.app.service.MedicationReminderReceiver1com.timeflow.app.service.MedicationActionReceiver0com.timeflow.app.service.NotificationTestService:com.timeflow.app.service.TaskPersistentNotificationServiceFcom.timeflow.app.service.TaskPersistentNotificationService.LocalBinder,com.timeflow.app.service.TimeTrackingService8com.timeflow.app.service.TimeTrackingService.LocalBinder com.timeflow.app.ui.MainActivity&com.timeflow.app.ui.base.SafeViewModel'com.timeflow.app.ui.base.ViewState.Idle*com.timeflow.app.ui.base.ViewState.Loading*com.timeflow.app.ui.base.ViewState.Success(com.timeflow.app.ui.base.ViewState.Error,com.timeflow.app.ui.component.goal.ChartType>com.timeflow.app.ui.language.LanguageManager.SupportedLanguage6com.timeflow.app.ui.language.LanguageSettingsViewModel=com.timeflow.app.ui.navigation.AnimationConfigurator.PageTypeHcom.timeflow.app.ui.navigation.AnimationConfigurator.NavigationDirectionBcom.timeflow.app.ui.navigation.AnimationOptimizer.PerformanceLevelKcom.timeflow.app.ui.navigation.BottomNavAnimationManager.TabSwitchDirectionPcom.timeflow.app.ui.navigation.BottomNavAnimationManager.BottomNavAnimationStyle*com.timeflow.app.ui.navigation.Screen.Home.com.timeflow.app.ui.navigation.Screen.TaskList0com.timeflow.app.ui.navigation.Screen.TaskDetail-com.timeflow.app.ui.navigation.Screen.AddTask.com.timeflow.app.ui.navigation.Screen.Calendar.com.timeflow.app.ui.navigation.Screen.Settings-com.timeflow.app.ui.navigation.Screen.Profile/com.timeflow.app.ui.navigation.Screen.Analytics.com.timeflow.app.ui.navigation.Screen.Discover2com.timeflow.app.ui.navigation.Screen.TimeTracking4com.timeflow.app.ui.navigation.TimeFlowNavigatorImpl)com.timeflow.app.ui.screen.ai.InsightType+com.timeflow.app.ui.screen.ai.InsightImpact0com.timeflow.app.ui.screen.ai.RecommendationType4com.timeflow.app.ui.screen.ai.RecommendationPriority,com.timeflow.app.ui.screen.ai.TrendDirection*com.timeflow.app.ui.screen.ai.WorkingStyle/com.timeflow.app.ui.screen.ai.AIReviewViewModel7com.timeflow.app.ui.screen.analytics.AnalyticsViewModelAcom.timeflow.app.ui.screen.analytics.AnalyticsViewModel.TimeRange4com.timeflow.app.ui.screen.calendar.CalendarViewType=com.timeflow.app.ui.screen.calendar.NoRippleInteractionSource5com.timeflow.app.ui.screen.calendar.CalendarViewModelKcom.timeflow.app.ui.screen.calendar.CalendarViewModel.CalendarUiEvent.ErrorScom.timeflow.app.ui.screen.calendar.CalendarViewModel.CalendarUiEvent.EventsUpdated5com.timeflow.app.ui.screen.discover.DiscoverViewModel1com.timeflow.app.ui.screen.goal.MockGoalViewModel2com.timeflow.app.ui.screen.goal.MockGoalRepository4com.timeflow.app.ui.screen.goal.TemplateUiState.Idle7com.timeflow.app.ui.screen.goal.TemplateUiState.Loading7com.timeflow.app.ui.screen.goal.TemplateUiState.Success5com.timeflow.app.ui.screen.goal.TemplateUiState.Error1com.timeflow.app.ui.screen.goal.TemplateSortOrder5com.timeflow.app.ui.screen.goal.GoalTemplateViewModel-com.timeflow.app.ui.screen.goal.GoalViewModel0com.timeflow.app.ui.screen.goal.GoalUiState.Idle3com.timeflow.app.ui.screen.goal.GoalUiState.Loading3com.timeflow.app.ui.screen.goal.GoalUiState.Success1com.timeflow.app.ui.screen.goal.GoalUiState.Error3com.timeflow.app.ui.screen.goal.BreakdownState.Idle9com.timeflow.app.ui.screen.goal.BreakdownState.Processing<com.timeflow.app.ui.screen.goal.BreakdownState.ShowingResult8com.timeflow.app.ui.screen.goal.BreakdownState.Completed4com.timeflow.app.ui.screen.goal.BreakdownState.Error1com.timeflow.app.ui.screen.goal.GoalState.Loading1com.timeflow.app.ui.screen.goal.GoalState.Success/com.timeflow.app.ui.screen.goal.GoalState.Error3com.timeflow.app.ui.screen.health.AddHabitViewModel/com.timeflow.app.ui.screen.health.FrequencyType1com.timeflow.app.ui.screen.health.HabitActionTypeAcom.timeflow.app.ui.screen.health.ProfessionalMedicationViewModel/com.timeflow.app.ui.screen.health.AlertSeverity+com.timeflow.app.ui.screen.health.AlertType1com.timeflow.app.ui.screen.health.InteractionType5com.timeflow.app.ui.screen.health.InteractionSeverity.com.timeflow.app.ui.screen.health.ResourceType-com.timeflow.app.ui.screen.health.ContactType.com.timeflow.app.ui.screen.health.ReminderType2com.timeflow.app.ui.screen.health.ReminderPriority0com.timeflow.app.ui.screen.health.TrendDirection4com.timeflow.app.ui.screen.health.AdherenceRiskLevel2com.timeflow.app.ui.screen.health.MissedDoseReason0com.timeflow.app.ui.screen.health.SuggestionType4com.timeflow.app.ui.screen.health.SuggestionPriority/com.timeflow.app.ui.screen.health.HeatmapPeriod6com.timeflow.app.ui.screen.milestone.MilestoneCategory2com.timeflow.app.ui.screen.milestone.MilestoneType7com.timeflow.app.ui.screen.milestone.MilestoneViewModel/com.timeflow.app.ui.screen.milestone.TextFormat-com.timeflow.app.ui.screen.milestone.ViewMode+com.timeflow.app.ui.screen.profile.ViewMode0com.timeflow.app.ui.screen.profile.EmotionFilter,com.timeflow.app.ui.screen.profile.TimeRange=com.timeflow.app.ui.screen.profile.EmotionStatisticsViewModel.com.timeflow.app.ui.screen.profile.EmotionType0com.timeflow.app.ui.screen.profile.EmotionRecord3com.timeflow.app.ui.screen.profile.ProfileViewModel4com.timeflow.app.ui.screen.reflection.ReflectionType0com.timeflow.app.ui.screen.reflection.TimeFilter4com.timeflow.app.ui.screen.reflection.AdvancedFilter/com.timeflow.app.ui.screen.reflection.SortOrder9com.timeflow.app.ui.screen.reflection.ReflectionViewModel?com.timeflow.app.ui.screen.reflection.ReflectionDetailViewModelCcom.timeflow.app.ui.screen.reflection.data.ReflectionRepositoryImplFcom.timeflow.app.ui.screen.reflection.data.SearchSuggestionServiceImpl9com.timeflow.app.ui.screen.reflection.model.TimeDimension7com.timeflow.app.ui.screen.settings.AutoBackupFrequency5com.timeflow.app.ui.screen.settings.DataManagementTabAcom.timeflow.app.ui.screen.settings.NotificationSettingsViewModel2com.timeflow.app.ui.screen.settings.MembershipType5com.timeflow.app.ui.screen.settings.SettingsViewModel1com.timeflow.app.ui.screen.settings.CloudProviderBcom.timeflow.app.ui.screen.settings.CloudStorageConfig.$serializer8com.timeflow.app.ui.screen.settings.S3Config.$<EMAIL>.$serializer6com.timeflow.app.ui.screen.settings.AwsS3StorageClient;com.timeflow.app.ui.screen.settings.QiniuCloudStorageClient=com.timeflow.app.ui.screen.settings.TencentCloudStorageClient:com.timeflow.app.ui.screen.settings.AliyunOssStorageClient6com.timeflow.app.ui.screen.settings.SyncRepositoryImpl9com.timeflow.app.ui.screen.settings.SyncSettingsViewModelFcom.timeflow.app.ui.screen.settings.CloudStorageConfigData.$serializer.com.timeflow.app.ui.screen.settings.SyncStatus0com.timeflow.app.ui.screen.settings.ResourceType.com.timeflow.app.ui.screen.task.PostponeOption3com.timeflow.app.ui.screen.task.TaskDetailViewModel9com.timeflow.app.ui.screen.task.TaskDetailUiState.Loading9com.timeflow.app.ui.screen.task.TaskDetailUiState.Success7com.timeflow.app.ui.screen.task.TaskDetailUiState.Error9com.timeflow.app.ui.screen.task.TaskDetailUiState.Deleted9com.timeflow.app.ui.screen.task.NoRippleInteractionSource4com.timeflow.app.ui.screen.task.CompletionFilterMode,com.timeflow.app.ui.screen.task.TaskSortMode6com.timeflow.app.ui.screen.task.ExperimentalComposeApi:com.timeflow.app.ui.screen.task.ExperimentalTypeConversion+com.timeflow.app.ui.screen.task.DrawerValue+com.timeflow.app.ui.screen.task.TaskUrgency-com.timeflow.app.ui.screen.task.TaskViewModelAcom.timeflow.app.ui.screen.task.TaskViewModel.TaskUiState.LoadingAcom.timeflow.app.ui.screen.task.TaskViewModel.TaskUiState.Success?com.timeflow.app.ui.screen.task.TaskViewModel.TaskUiState.Error1com.timeflow.app.ui.screen.task.model.TaskUrgency&com.timeflow.app.ui.settings.ColorType1com.timeflow.app.ui.settings.PresetThemeViewModel&<EMAIL>(com.timeflow.app.ui.task.KanbanViewModel<com.timeflow.app.ui.task.TaskListOptimizer.OptimizationLevel=com.timeflow.app.ui.task.TaskListOptimizer.DeviceCapabilities*com.timeflow.app.ui.task.TaskListViewModel1com.timeflow.app.ui.task.components.TaskCardStyle:<EMAIL>)com.timeflow.app.ui.task.model.SortOption'com.timeflow.app.ui.task.model.ViewMode#com.timeflow.app.ui.theme.ThemeMode<com.timeflow.app.ui.timetracking.TaskTimeStatisticsViewModel6com.timeflow.app.ui.timetracking.TimeTrackingViewModel+com.timeflow.app.ui.timetracking.TimerState*com.timeflow.app.ui.timetracking.TimerType6com.timeflow.app.ui.timetracking.components.TimerState2com.timeflow.app.ui.viewmodel.AiAssistantViewModel/com.timeflow.app.ui.viewmodel.AiConfigViewModel1com.timeflow.app.ui.viewmodel.AiSettingsViewModel2com.timeflow.app.ui.viewmodel.GlobalTimerViewModel3com.timeflow.app.ui.viewmodel.GoalCreationViewModelNcom.timeflow.app.ui.viewmodel.GoalCreationViewModel.GoalCreationUiState.Normal^com.timeflow.app.ui.viewmodel.GoalCreationViewModel.GoalCreationUiState.TemplateRecommendationPcom.timeflow.app.ui.viewmodel.GoalCreationViewModel.GoalCreationUiState.CreatingOcom.timeflow.app.ui.viewmodel.GoalCreationViewModel.GoalCreationUiState.SuccessMcom.timeflow.app.ui.viewmodel.GoalCreationViewModel.GoalCreationUiState.Error+com.timeflow.app.ui.viewmodel.GoalViewModel.com.timeflow.app.ui.viewmodel.GoalUiState.Idle1com.timeflow.app.ui.viewmodel.GoalUiState.Loading1com.timeflow.app.ui.viewmodel.GoalUiState.Success/com.timeflow.app.ui.viewmodel.GoalUiState.Error,com.timeflow.app.ui.viewmodel.HabitViewModel'com.timeflow.app.ui.viewmodel.CycleType5com.timeflow.app.ui.viewmodel.MenstrualCycleViewModel4com.timeflow.app.ui.viewmodel.ValidationResult.Valid6com.timeflow.app.ui.viewmodel.ValidationResult.Warning6com.timeflow.app.ui.viewmodel.ValidationResult.Invalid<<EMAIL>>com.timeflow.app.ui.viewmodel.OperationResult.ConflictResolved/com.timeflow.app.ui.viewmodel.TaskTimeViewModel/com.timeflow.app.ui.viewmodel.TimeFlowViewModel/com.timeflow.app.ui.viewmodel.WishListViewModel5com.timeflow.app.ui.viewmodel.WishStatisticsViewModel%com.timeflow.app.util.HabitChangeType*com.timeflow.app.utils.AppExceptionHandler3com.timeflow.app.utils.HwcLutsErrorFixer.ErrorState,com.timeflow.app.utils.LogConfig.ReleaseTree!com.timeflow.app.utils.RenderMode.com.timeflow.app.utils.RenderOptimizationLevelcom.timeflow.app.utils.Priority1com.timeflow.app.utils.NotificationActionReceiver1com.timeflow.app.viewmodel.BackupRestoreViewModel2com.timeflow.app.viewmodel.BackupSettingsViewModel2com.timeflow.app.viewmodel.DataManagementViewModelIcom.timeflow.app.viewmodel.DataManagementViewModel.ExportData.$serializer(com.timeflow.app.widget.FocusTimerWidget*com.timeflow.app.widget.GoalProgressWidget(com.timeflow.app.widget.QuickTimerWidget)com.timeflow.app.widget.TimeInsightWidget(com.timeflow.app.widget.TodayTasksWidget)com.timeflow.app.widget.WeeklyStatsWidget*com.timeflow.app.worker.AiSuggestionWorker(com.timeflow.app.worker.AutoBackupWorker)com.timeflow.app.worker.DailyReviewWorker+com.timeflow.app.worker.HabitReminderWorker.com.timeflow.app.worker.OverdueTaskCheckWorker+com.timeflow.app.worker.RecurringTaskWorker*com.timeflow.app.worker.TaskReminderWorker                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               