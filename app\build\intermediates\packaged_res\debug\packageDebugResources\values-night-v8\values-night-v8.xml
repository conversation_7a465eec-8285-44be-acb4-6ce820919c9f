<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <color name="widget_accent_blue">#0A84FF</color>
    <color name="widget_accent_green">#30D158</color>
    <color name="widget_accent_orange">#FF9F0A</color>
    <color name="widget_accent_red">#FF453A</color>
    <color name="widget_background_overlay">#2C2C2E</color>
    <color name="widget_background_white">#1C1C1E</color>
    <color name="widget_disabled">@color/widget_disabled_dark</color>
    <color name="widget_focus">@color/widget_focus_dark</color>
    <color name="widget_focus_container">@color/widget_focus_container_dark</color>
    <color name="widget_focus_primary">@color/widget_focus_primary_dark</color>
    <color name="widget_goal_container">@color/widget_goal_container_dark</color>
    <color name="widget_goal_primary">@color/widget_goal_primary_dark</color>
    <color name="widget_gradient_end">#2C2C2E</color>
    <color name="widget_gradient_start">#1C1C1E</color>
    <color name="widget_hover">@color/widget_hover_dark</color>
    <color name="widget_md3_background">@color/widget_md3_background_dark</color>
    <color name="widget_md3_divider">@color/widget_md3_divider_dark</color>
    <color name="widget_md3_error">@color/widget_md3_error_dark</color>
    <color name="widget_md3_error_container">@color/widget_md3_error_container_dark</color>
    <color name="widget_md3_inverse_on_surface">@color/widget_md3_inverse_on_surface_dark</color>
    <color name="widget_md3_inverse_primary">@color/widget_md3_inverse_primary_dark</color>
    <color name="widget_md3_inverse_surface">@color/widget_md3_inverse_surface_dark</color>
    <color name="widget_md3_on_background">@color/widget_md3_on_background_dark</color>
    <color name="widget_md3_on_error">@color/widget_md3_on_error_dark</color>
    <color name="widget_md3_on_error_container">@color/widget_md3_on_error_container_dark</color>
    <color name="widget_md3_on_primary">@color/widget_md3_on_primary_dark</color>
    <color name="widget_md3_on_primary_container">@color/widget_md3_on_primary_container_dark</color>
    <color name="widget_md3_on_secondary">@color/widget_md3_on_secondary_dark</color>
    <color name="widget_md3_on_secondary_container">@color/widget_md3_on_secondary_container_dark</color>
    <color name="widget_md3_on_surface">@color/widget_md3_on_surface_dark</color>
    <color name="widget_md3_on_surface_variant">@color/widget_md3_on_surface_variant_dark</color>
    <color name="widget_md3_on_tertiary">@color/widget_md3_on_tertiary_dark</color>
    <color name="widget_md3_on_tertiary_container">@color/widget_md3_on_tertiary_container_dark</color>
    <color name="widget_md3_outline">@color/widget_md3_outline_dark</color>
    <color name="widget_md3_outline_variant">@color/widget_md3_outline_variant_dark</color>
    <color name="widget_md3_overlay">@color/widget_md3_overlay_dark</color>
    <color name="widget_md3_primary">@color/widget_md3_primary_dark</color>
    <color name="widget_md3_primary_container">@color/widget_md3_primary_container_dark</color>
    <color name="widget_md3_scrim">@color/widget_md3_scrim_dark</color>
    <color name="widget_md3_secondary">@color/widget_md3_secondary_dark</color>
    <color name="widget_md3_secondary_container">@color/widget_md3_secondary_container_dark</color>
    <color name="widget_md3_shadow">@color/widget_md3_shadow_dark</color>
    <color name="widget_md3_surface">@color/widget_md3_surface_dark</color>
    <color name="widget_md3_surface_variant">@color/widget_md3_surface_variant_dark</color>
    <color name="widget_md3_tertiary">@color/widget_md3_tertiary_dark</color>
    <color name="widget_md3_tertiary_container">@color/widget_md3_tertiary_container_dark</color>
    <color name="widget_pressed">@color/widget_pressed_dark</color>
    <color name="widget_selected">@color/widget_selected_dark</color>
    <color name="widget_stats_container">@color/widget_stats_container_dark</color>
    <color name="widget_stats_primary">@color/widget_stats_primary_dark</color>
    <color name="widget_status_error">@color/widget_status_error_dark</color>
    <color name="widget_status_info">@color/widget_status_info_dark</color>
    <color name="widget_status_success">@color/widget_status_success_dark</color>
    <color name="widget_status_warning">@color/widget_status_warning_dark</color>
    <color name="widget_surface_tint">@color/widget_surface_tint_dark</color>
    <color name="widget_task_completed">#30D158</color>
    <color name="widget_task_container">@color/widget_task_container_dark</color>
    <color name="widget_task_overdue">#FF453A</color>
    <color name="widget_task_pending">#FF9F0A</color>
    <color name="widget_task_primary">@color/widget_task_primary_dark</color>
    <color name="widget_text_primary">#FFFFFF</color>
    <color name="widget_text_secondary">#AEAEB2</color>
    <color name="widget_text_tertiary">#6C6C70</color>
    <color name="widget_today_accent_light">#0A84FF</color>
    <color name="widget_today_background_light">#1C1C1E</color>
    <color name="widget_today_border_light">#38383A</color>
    <color name="widget_today_shadow_light">#20000000</color>
    <color name="widget_today_surface_light">#2C2C2E</color>
    <color name="widget_today_text_primary_light">#FFFFFF</color>
    <color name="widget_today_text_secondary_light">#AEAEB2</color>
    <style name="Theme.TimeFlow" parent="Theme.Material3.DayNight.NoActionBar">
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar" ns1:targetApi="27">false</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
    </style>
    <style name="Theme.TimeFlow.StaticColors">
        
        <item name="colorPrimary">@color/primary_light</item>
        <item name="colorPrimaryContainer">@color/primary_variant</item>
        <item name="colorOnPrimary">@color/black</item>
        
        <item name="colorSecondary">@color/secondary_light</item>
        <item name="colorSecondaryContainer">@color/secondary_variant</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="colorTertiary">@color/primary_light</item>
        <item name="colorOnTertiary">@color/black</item>
        
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar" ns1:targetApi="27">false</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
    </style>
</resources>