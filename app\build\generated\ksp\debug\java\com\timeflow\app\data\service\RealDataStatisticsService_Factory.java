package com.timeflow.app.data.service;

import com.timeflow.app.data.repository.GoalRepository;
import com.timeflow.app.data.repository.HabitRepository;
import com.timeflow.app.data.repository.TaskRepository;
import com.timeflow.app.data.repository.TimeSessionRepository;
import com.timeflow.app.ui.screen.reflection.ReflectionRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RealDataStatisticsService_Factory implements Factory<RealDataStatisticsService> {
  private final Provider<TaskRepository> taskRepositoryProvider;

  private final Provider<TimeSessionRepository> timeSessionRepositoryProvider;

  private final Provider<GoalRepository> goalRepositoryProvider;

  private final Provider<HabitRepository> habitRepositoryProvider;

  private final Provider<ReflectionRepository> reflectionRepositoryProvider;

  public RealDataStatisticsService_Factory(Provider<TaskRepository> taskRepositoryProvider,
      Provider<TimeSessionRepository> timeSessionRepositoryProvider,
      Provider<GoalRepository> goalRepositoryProvider,
      Provider<HabitRepository> habitRepositoryProvider,
      Provider<ReflectionRepository> reflectionRepositoryProvider) {
    this.taskRepositoryProvider = taskRepositoryProvider;
    this.timeSessionRepositoryProvider = timeSessionRepositoryProvider;
    this.goalRepositoryProvider = goalRepositoryProvider;
    this.habitRepositoryProvider = habitRepositoryProvider;
    this.reflectionRepositoryProvider = reflectionRepositoryProvider;
  }

  @Override
  public RealDataStatisticsService get() {
    return newInstance(taskRepositoryProvider.get(), timeSessionRepositoryProvider.get(), goalRepositoryProvider.get(), habitRepositoryProvider.get(), reflectionRepositoryProvider.get());
  }

  public static RealDataStatisticsService_Factory create(
      Provider<TaskRepository> taskRepositoryProvider,
      Provider<TimeSessionRepository> timeSessionRepositoryProvider,
      Provider<GoalRepository> goalRepositoryProvider,
      Provider<HabitRepository> habitRepositoryProvider,
      Provider<ReflectionRepository> reflectionRepositoryProvider) {
    return new RealDataStatisticsService_Factory(taskRepositoryProvider, timeSessionRepositoryProvider, goalRepositoryProvider, habitRepositoryProvider, reflectionRepositoryProvider);
  }

  public static RealDataStatisticsService newInstance(TaskRepository taskRepository,
      TimeSessionRepository timeSessionRepository, GoalRepository goalRepository,
      HabitRepository habitRepository, ReflectionRepository reflectionRepository) {
    return new RealDataStatisticsService(taskRepository, timeSessionRepository, goalRepository, habitRepository, reflectionRepository);
  }
}
