<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <color name="widget_accent_blue">#0A84FF</color>
    <color name="widget_accent_green">#30D158</color>
    <color name="widget_accent_orange">#FF9F0A</color>
    <color name="widget_accent_red">#FF453A</color>
    <color name="widget_background_overlay">#2C2C2E</color>
    <color name="widget_background_white">#1C1C1E</color>
    <color name="widget_gradient_end">#2C2C2E</color>
    <color name="widget_gradient_start">#1C1C1E</color>
    <color name="widget_task_completed">#30D158</color>
    <color name="widget_task_overdue">#FF453A</color>
    <color name="widget_task_pending">#FF9F0A</color>
    <color name="widget_text_primary">#FFFFFF</color>
    <color name="widget_text_secondary">#AEAEB2</color>
    <color name="widget_text_tertiary">#6C6C70</color>
    <color name="widget_today_accent_light">#0A84FF</color>
    <color name="widget_today_background_light">#1C1C1E</color>
    <color name="widget_today_border_light">#38383A</color>
    <color name="widget_today_shadow_light">#20000000</color>
    <color name="widget_today_surface_light">#2C2C2E</color>
    <color name="widget_today_text_primary_light">#FFFFFF</color>
    <color name="widget_today_text_secondary_light">#AEAEB2</color>
    <style name="Theme.TimeFlow" parent="Theme.Material3.DayNight.NoActionBar">
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar" ns1:targetApi="27">false</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
    </style>
    <style name="Theme.TimeFlow.StaticColors">
        
        <item name="colorPrimary">@color/primary_light</item>
        <item name="colorPrimaryContainer">@color/primary_variant</item>
        <item name="colorOnPrimary">@color/black</item>
        
        <item name="colorSecondary">@color/secondary_light</item>
        <item name="colorSecondaryContainer">@color/secondary_variant</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="colorTertiary">@color/primary_light</item>
        <item name="colorOnTertiary">@color/black</item>
        
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar" ns1:targetApi="27">false</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
    </style>
</resources>