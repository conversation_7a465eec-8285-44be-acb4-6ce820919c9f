package com.timeflow.app.ui.statistics;

import com.timeflow.app.data.service.RealDataStatisticsService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RealTimeStatisticsViewModel_Factory implements Factory<RealTimeStatisticsViewModel> {
  private final Provider<RealDataStatisticsService> realDataStatisticsServiceProvider;

  public RealTimeStatisticsViewModel_Factory(
      Provider<RealDataStatisticsService> realDataStatisticsServiceProvider) {
    this.realDataStatisticsServiceProvider = realDataStatisticsServiceProvider;
  }

  @Override
  public RealTimeStatisticsViewModel get() {
    return newInstance(realDataStatisticsServiceProvider.get());
  }

  public static RealTimeStatisticsViewModel_Factory create(
      Provider<RealDataStatisticsService> realDataStatisticsServiceProvider) {
    return new RealTimeStatisticsViewModel_Factory(realDataStatisticsServiceProvider);
  }

  public static RealTimeStatisticsViewModel newInstance(
      RealDataStatisticsService realDataStatisticsService) {
    return new RealTimeStatisticsViewModel(realDataStatisticsService);
  }
}
