package com.timeflow.app.data.model

import java.time.LocalDate
import java.time.YearMonth

// ========================================================================================
// 统计数据模型
// ========================================================================================

/**
 * 月度统计数据
 */
data class MonthlyStatisticsData(
    val yearMonth: YearMonth,
    val taskStatistics: TaskStatistics,
    val timeStatistics: TimeStatistics,
    val goalStatistics: GoalStatistics,
    val habitStatistics: HabitStatistics,
    val emotionStatistics: EmotionStatistics,
    val productivityScore: Int
) {
    companion object {
        fun empty(yearMonth: YearMonth) = MonthlyStatisticsData(
            yearMonth = yearMonth,
            taskStatistics = TaskStatistics.empty(),
            timeStatistics = TimeStatistics.empty(),
            goalStatistics = GoalStatistics.empty(),
            habitStatistics = HabitStatistics.empty(),
            emotionStatistics = EmotionStatistics.empty(),
            productivityScore = 0
        )
    }
}

/**
 * 年度统计数据
 */
data class YearlyStatisticsData(
    val year: Int,
    val taskStatistics: TaskStatistics,
    val timeStatistics: TimeStatistics,
    val goalStatistics: GoalStatistics,
    val habitStatistics: HabitStatistics,
    val productivityScore: Int,
    val monthlyTrends: List<MonthlyTrendData>,
    val achievements: List<YearlyAchievement>,
    val insights: List<String>
) {
    companion object {
        fun empty(year: Int) = YearlyStatisticsData(
            year = year,
            taskStatistics = TaskStatistics.empty(),
            timeStatistics = TimeStatistics.empty(),
            goalStatistics = GoalStatistics.empty(),
            habitStatistics = HabitStatistics.empty(),
            productivityScore = 0,
            monthlyTrends = emptyList(),
            achievements = emptyList(),
            insights = emptyList()
        )
    }
}

/**
 * 具象视图数据
 */
data class ConcreteViewData(
    val dailyBreakdown: List<DailyBreakdown>,
    val detailedTasks: List<Task>,
    val tagAnalysis: TagAnalysis,
    val hourlyHeatmap: HourlyHeatmap
) {
    companion object {
        fun empty() = ConcreteViewData(
            dailyBreakdown = emptyList(),
            detailedTasks = emptyList(),
            tagAnalysis = TagAnalysis.empty(),
            hourlyHeatmap = HourlyHeatmap.empty()
        )
    }
}

/**
 * 宏观视图数据
 */
data class MacroViewData(
    val productivityTrend: List<TrendPoint>,
    val categoryDistribution: Map<String, Float>,
    val efficiencyPattern: EfficiencyPattern,
    val goalProgressOverview: GoalProgressOverview,
    val habitStreakOverview: HabitStreakOverview,
    val overallInsights: List<String>
) {
    companion object {
        fun empty() = MacroViewData(
            productivityTrend = emptyList(),
            categoryDistribution = emptyMap(),
            efficiencyPattern = EfficiencyPattern.empty(),
            goalProgressOverview = GoalProgressOverview.empty(),
            habitStreakOverview = HabitStreakOverview.empty(),
            overallInsights = emptyList()
        )
    }
}

// ========================================================================================
// 基础统计类型
// ========================================================================================

/**
 * 任务统计
 */
data class TaskStatistics(
    val totalTasks: Int,
    val completedTasks: Int,
    val completionRate: Float,
    val averagePriority: Float,
    val categoryDistribution: Map<String, Int>
) {
    companion object {
        fun empty() = TaskStatistics(0, 0, 0f, 0f, emptyMap())
    }
}

/**
 * 时间统计
 */
data class TimeStatistics(
    val totalMinutes: Long,
    val focusSessions: Int,
    val averageSessionLength: Float,
    val timeDistribution: Map<String, Float>,
    val dailyPattern: List<DailyTimeData>
) {
    companion object {
        fun empty() = TimeStatistics(0L, 0, 0f, emptyMap(), emptyList())
    }
}

/**
 * 目标统计
 */
data class GoalStatistics(
    val totalGoals: Int,
    val completedGoals: Int,
    val completionRate: Float,
    val averageProgress: Float
) {
    companion object {
        fun empty() = GoalStatistics(0, 0, 0f, 0f)
    }
}

/**
 * 习惯统计
 */
data class HabitStatistics(
    val totalHabits: Int,
    val activeHabits: Int,
    val averageStreak: Float,
    val completionRate: Float
) {
    companion object {
        fun empty() = HabitStatistics(0, 0, 0f, 0f)
    }
}

/**
 * 情绪统计
 */
data class EmotionStatistics(
    val totalReflections: Int,
    val moodDistribution: Map<String, Int>,
    val averageMoodScore: Float,
    val emotionalTrend: List<EmotionTrendPoint>
) {
    companion object {
        fun empty() = EmotionStatistics(0, emptyMap(), 0f, emptyList())
    }
}

// ========================================================================================
// 辅助数据类型
// ========================================================================================

data class MonthlyTrendData(
    val month: Int,
    val monthName: String,
    val productivityScore: Int,
    val taskCompletionRate: Float,
    val totalFocusHours: Float,
    val goalProgress: Float
)

data class YearlyAchievement(
    val title: String,
    val description: String,
    val achievedDate: LocalDate,
    val category: String,
    val value: String
)

data class DailyBreakdown(
    val date: LocalDate,
    val totalTasks: Int,
    val completedTasks: Int,
    val focusMinutes: Long,
    val productivityScore: Int,
    val topCategory: String
)

data class TagAnalysis(
    val topTags: List<TagFrequency>,
    val tagTimeDistribution: Map<String, Long>,
    val tagProductivity: Map<String, Float>
) {
    companion object {
        fun empty() = TagAnalysis(emptyList(), emptyMap(), emptyMap())
    }
}

data class TagFrequency(
    val tag: String,
    val frequency: Int,
    val totalTime: Long
)

data class HourlyHeatmap(
    val data: Array<Array<Int>>,
    val maxValue: Int
) {
    companion object {
        fun empty() = HourlyHeatmap(Array(7) { Array(24) { 0 } }, 0)
    }
}

data class TrendPoint(
    val date: LocalDate,
    val value: Float,
    val label: String
)

data class EfficiencyPattern(
    val peakHours: List<Int>,
    val lowHours: List<Int>,
    val weekdayPattern: Map<Int, Float>,
    val averageEfficiency: Float
) {
    companion object {
        fun empty() = EfficiencyPattern(emptyList(), emptyList(), emptyMap(), 0f)
    }
}

data class GoalProgressOverview(
    val totalGoals: Int,
    val onTrackGoals: Int,
    val completedGoals: Int,
    val delayedGoals: Int,
    val categoryProgress: Map<String, Float>
) {
    companion object {
        fun empty() = GoalProgressOverview(0, 0, 0, 0, emptyMap())
    }
}

data class HabitStreakOverview(
    val totalHabits: Int,
    val activeStreaks: Int,
    val longestStreak: Int,
    val averageStreak: Float,
    val habitPerformance: Map<String, Int>
) {
    companion object {
        fun empty() = HabitStreakOverview(0, 0, 0, 0f, emptyMap())
    }
}

data class DailyTimeData(
    val date: LocalDate,
    val totalMinutes: Long,
    val categoryBreakdown: Map<String, Long>
)

data class EmotionTrendPoint(
    val date: LocalDate,
    val averageScore: Float,
    val dominantEmotion: String
)

// ========================================================================================
// 视图模式枚举
// ========================================================================================

enum class ViewMode {
    CONCRETE, // 具象视图
    MACRO     // 宏观视图
}
