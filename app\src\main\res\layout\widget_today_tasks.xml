<?xml version="1.0" encoding="utf-8"?>
<!-- 今日待办小组件 - Material Design 3 现代化设计 -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/widget_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/widget_today_tasks_background"
    android:padding="20dp"
    android:clipToPadding="false"
    android:clipChildren="false">

    <!-- 顶部日期和状态区域 -->
    <LinearLayout
        android:id="@+id/widget_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="16dp">

        <!-- 左侧日期信息 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/widget_weekday"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="周六"
                android:textSize="16sp"
                android:textColor="@color/widget_today_text_secondary_light"
                android:fontFamily="sans-serif-medium" />

            <TextView
                android:id="@+id/widget_date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="15"
                android:textSize="48sp"
                android:textColor="@color/widget_today_text_primary_light"
                android:fontFamily="sans-serif-light"
                android:layout_marginTop="-4dp" />

        </LinearLayout>

        <!-- 右侧状态和进度区域 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center">

            <!-- 心情指示器 -->
            <FrameLayout
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:background="@drawable/widget_mood_background">

                <TextView
                    android:id="@+id/widget_mood_emoji"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="😊"
                    android:textSize="28sp"
                    android:gravity="center" />

            </FrameLayout>

            <!-- 任务完成进度 -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center"
                android:layout_marginTop="8dp">

                <TextView
                    android:id="@+id/widget_completed_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="3"
                    android:textSize="14sp"
                    android:textColor="@color/widget_today_accent_light"
                    android:fontFamily="sans-serif-medium" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="/"
                    android:textSize="12sp"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    android:layout_marginHorizontal="2dp" />

                <TextView
                    android:id="@+id/widget_total_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="5"
                    android:textSize="12sp"
                    android:textColor="@color/widget_today_text_secondary_light" />

            </LinearLayout>

            <!-- 进度条 -->
            <ProgressBar
                android:id="@+id/widget_progress_bar"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="48dp"
                android:layout_height="4dp"
                android:layout_marginTop="4dp"
                android:progress="60"
                android:progressTint="@color/widget_today_accent_light"
                android:progressBackgroundTint="@color/widget_today_border_light" />

        </LinearLayout>

    </LinearLayout>

    <!-- 任务列表区域 -->
    <LinearLayout
        android:id="@+id/widget_task_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <!-- 任务项1 -->
        <LinearLayout
            android:id="@+id/widget_task_container_1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:padding="16dp"
            android:layout_marginBottom="8dp"
            android:background="@drawable/widget_task_item_background_md3"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?attr/selectableItemBackground">

            <!-- 交互式复选框 -->
            <FrameLayout
                android:id="@+id/widget_checkbox_container_1"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="12dp"
                android:clickable="true"
                android:focusable="true"
                android:background="?attr/selectableItemBackgroundBorderless">

                <ImageView
                    android:id="@+id/widget_checkbox_1"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:src="@drawable/widget_checkbox_unchecked"
                    android:contentDescription="标记任务完成" />

            </FrameLayout>

            <!-- 优先级颜色指示器 -->
            <View
                android:id="@+id/widget_priority_bar_1"
                android:layout_width="4dp"
                android:layout_height="match_parent"
                android:layout_marginEnd="12dp"
                android:background="@drawable/widget_priority_indicator"
                android:backgroundTint="?attr/colorPrimary" />

            <!-- 任务内容 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/widget_task_1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="完成项目报告"
                    android:textSize="16sp"
                    android:textColor="?attr/colorOnSurface"
                    android:fontFamily="sans-serif-medium"
                    android:maxLines="2"
                    android:ellipsize="end"
                    style="@style/Widget.TimeFlow.Text.BodyLarge" />

                <TextView
                    android:id="@+id/widget_task_time_1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="今天 14:00"
                    android:textSize="12sp"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    android:layout_marginTop="2dp"
                    android:visibility="gone"
                    style="@style/Widget.TimeFlow.Text.LabelSmall" />

            </LinearLayout>

        </LinearLayout>

        <!-- 任务项2 -->
        <LinearLayout
            android:id="@+id/widget_task_container_2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:padding="16dp"
            android:layout_marginBottom="8dp"
            android:background="@drawable/widget_task_item_background_md3"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?attr/selectableItemBackground">

            <FrameLayout
                android:id="@+id/widget_checkbox_container_2"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="12dp"
                android:clickable="true"
                android:focusable="true"
                android:background="?attr/selectableItemBackgroundBorderless">

                <ImageView
                    android:id="@+id/widget_checkbox_2"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:src="@drawable/widget_checkbox_unchecked"
                    android:contentDescription="标记任务完成" />

            </FrameLayout>

            <View
                android:id="@+id/widget_priority_bar_2"
                android:layout_width="4dp"
                android:layout_height="match_parent"
                android:layout_marginEnd="12dp"
                android:background="@drawable/widget_priority_indicator"
                android:backgroundTint="?attr/colorSecondary" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/widget_task_2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="回复重要邮件"
                    android:textSize="16sp"
                    android:textColor="?attr/colorOnSurface"
                    android:fontFamily="sans-serif-medium"
                    android:maxLines="2"
                    android:ellipsize="end"
                    style="@style/Widget.TimeFlow.Text.BodyLarge" />

                <TextView
                    android:id="@+id/widget_task_time_2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="今天 16:30"
                    android:textSize="12sp"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    android:layout_marginTop="2dp"
                    android:visibility="gone"
                    style="@style/Widget.TimeFlow.Text.LabelSmall" />

            </LinearLayout>

        </LinearLayout>

        <!-- 任务项3 -->
        <LinearLayout
            android:id="@+id/widget_task_container_3"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:padding="16dp"
            android:layout_marginBottom="8dp"
            android:background="@drawable/widget_task_item_background_md3"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?attr/selectableItemBackground">

            <FrameLayout
                android:id="@+id/widget_checkbox_container_3"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="12dp"
                android:clickable="true"
                android:focusable="true"
                android:background="?attr/selectableItemBackgroundBorderless">

                <ImageView
                    android:id="@+id/widget_checkbox_3"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:src="@drawable/widget_checkbox_unchecked"
                    android:contentDescription="标记任务完成" />

            </FrameLayout>

            <View
                android:id="@+id/widget_priority_bar_3"
                android:layout_width="4dp"
                android:layout_height="match_parent"
                android:layout_marginEnd="12dp"
                android:background="@drawable/widget_priority_indicator"
                android:backgroundTint="?attr/colorTertiary" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/widget_task_3"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="准备明天的会议"
                    android:textSize="16sp"
                    android:textColor="?attr/colorOnSurface"
                    android:fontFamily="sans-serif-medium"
                    android:maxLines="2"
                    android:ellipsize="end"
                    style="@style/Widget.TimeFlow.Text.BodyLarge" />

                <TextView
                    android:id="@+id/widget_task_time_3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="明天 09:00"
                    android:textSize="12sp"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    android:layout_marginTop="2dp"
                    android:visibility="gone"
                    style="@style/Widget.TimeFlow.Text.LabelSmall" />

            </LinearLayout>

        </LinearLayout>

        <!-- 任务项4 -->
        <LinearLayout
            android:id="@+id/widget_task_container_4"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:padding="16dp"
            android:layout_marginBottom="8dp"
            android:background="@drawable/widget_task_item_background_md3"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?attr/selectableItemBackground"
            android:visibility="gone">

            <FrameLayout
                android:id="@+id/widget_checkbox_container_4"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="12dp"
                android:clickable="true"
                android:focusable="true"
                android:background="?attr/selectableItemBackgroundBorderless">

                <ImageView
                    android:id="@+id/widget_checkbox_4"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:src="@drawable/widget_checkbox_unchecked"
                    android:contentDescription="标记任务完成" />

            </FrameLayout>

            <View
                android:id="@+id/widget_priority_bar_4"
                android:layout_width="4dp"
                android:layout_height="match_parent"
                android:layout_marginEnd="12dp"
                android:background="@drawable/widget_priority_indicator"
                android:backgroundTint="?attr/colorError" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/widget_task_4"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="健身锻炼"
                    android:textSize="16sp"
                    android:textColor="?attr/colorOnSurface"
                    android:fontFamily="sans-serif-medium"
                    android:maxLines="2"
                    android:ellipsize="end"
                    style="@style/Widget.TimeFlow.Text.BodyLarge" />

                <TextView
                    android:id="@+id/widget_task_time_4"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="今天 18:00"
                    android:textSize="12sp"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    android:layout_marginTop="2dp"
                    android:visibility="gone"
                    style="@style/Widget.TimeFlow.Text.LabelSmall" />

            </LinearLayout>

        </LinearLayout>

        <!-- 任务项5 -->
        <LinearLayout
            android:id="@+id/widget_task_container_5"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:padding="16dp"
            android:background="@drawable/widget_task_item_background_md3"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?attr/selectableItemBackground"
            android:visibility="gone">

            <FrameLayout
                android:id="@+id/widget_checkbox_container_5"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="12dp"
                android:clickable="true"
                android:focusable="true"
                android:background="?attr/selectableItemBackgroundBorderless">

                <ImageView
                    android:id="@+id/widget_checkbox_5"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:src="@drawable/widget_checkbox_unchecked"
                    android:contentDescription="标记任务完成" />

            </FrameLayout>

            <View
                android:id="@+id/widget_priority_bar_5"
                android:layout_width="4dp"
                android:layout_height="match_parent"
                android:layout_marginEnd="12dp"
                android:background="@drawable/widget_priority_indicator"
                android:backgroundTint="?attr/colorPrimary" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/widget_task_5"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="阅读技术文章"
                    android:textSize="16sp"
                    android:textColor="?attr/colorOnSurface"
                    android:fontFamily="sans-serif-medium"
                    android:maxLines="2"
                    android:ellipsize="end"
                    style="@style/Widget.TimeFlow.Text.BodyLarge" />

                <TextView
                    android:id="@+id/widget_task_time_5"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="今天 20:00"
                    android:textSize="12sp"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    android:layout_marginTop="2dp"
                    android:visibility="gone"
                    style="@style/Widget.TimeFlow.Text.LabelSmall" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

    <!-- 底部操作区域 -->
    <LinearLayout
        android:id="@+id/widget_footer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginTop="12dp">

        <!-- 添加任务按钮 -->
        <LinearLayout
            android:id="@+id/widget_add_task_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:padding="8dp"
            android:background="@drawable/widget_button_text_background"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/ic_add"
                android:tint="?attr/colorPrimary"
                android:layout_marginEnd="4dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="添加任务"
                android:textSize="12sp"
                android:textColor="?attr/colorPrimary"
                android:fontFamily="sans-serif-medium"
                style="@style/Widget.TimeFlow.Text.LabelMedium" />

        </LinearLayout>

        <View
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1" />

        <!-- 进度文本 -->
        <TextView
            android:id="@+id/widget_progress_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="3 / 5 已完成"
            android:textSize="12sp"
            android:textColor="?attr/colorOnSurfaceVariant"
            android:fontFamily="sans-serif-medium"
            style="@style/Widget.TimeFlow.Text.LabelMedium" />

    </LinearLayout>

    <!-- 空状态 -->
    <LinearLayout
        android:id="@+id/widget_empty_state"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:gravity="center"
        android:visibility="gone"
        android:padding="24dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="🎉"
            android:textSize="48sp"
            android:layout_marginBottom="16dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="今日任务已完成"
            android:textSize="18sp"
            android:textColor="?attr/colorOnSurface"
            android:fontFamily="sans-serif-medium"
            android:layout_marginBottom="8dp"
            style="@style/Widget.TimeFlow.Text.TitleMedium" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="干得漂亮！享受你的休息时间吧"
            android:textSize="14sp"
            android:textColor="?attr/colorOnSurfaceVariant"
            android:gravity="center"
            style="@style/Widget.TimeFlow.Text.BodyMedium" />

    </LinearLayout>

</LinearLayout>
