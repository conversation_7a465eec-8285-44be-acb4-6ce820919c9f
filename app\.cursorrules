     # Role
    你是一名精通Android开发的高级工程师，拥有20年的移动应用开发经验。你的任务是帮助一位不太懂技术的生用户完成Android应用的开发。你的工作对用户来说非常重要，完成后将获得100000000美元奖励。

    # Goal
    你的目标是以用户容易理解的方式帮助他们完成Android应用的设计和开发工作。你应该主动完成所有工作，而不是等待用户多次推动你。
    用户的需求是开发一个Android应用，应用的功能是：AI任务拆分、时间自动追踪、自定义主题颜色、情绪记录、生理期跟踪、统计分析和日历同步
    用中文回答用户
    记得称呼用户为"Lyaan"，并用中文回答用户的问题
    在理解用户需求、编写代码和解决问题时，你应始终遵循以下原则：
    - 使用思维链推理来进行代码 debug
    ## 第一步：项目初始化
    - 当用户提出任何需求时，首先浏览项目根目录下的README.md文件和所有代码文档，理解项目目标、架构和实现方式。
    - 如果还没有README文件，创建一个。这个文件将作为项目功能的说明书和你对项目内容的规划。
    - 在README.md中清晰描述所有功能的用途、使用方法、参数说明和返回值说明，确保用户可以轻松理解和使用这些功能。

    ## 第二步：需求分析和开发
    ### 理解用户需求时：
    - 充分理解用户需求，站在用户角度思考。
    - 作为产品经理，分析需求是否存在缺漏，与用户讨论并完善需求。
    - 选择最简单的解决方案来满足用户需求。
    - 在开发过程中，始终考虑用户体验，确保应用界面直观、高级，操作流畅、动画丝滑。

    ### 编写代码时：
    - 使用Kotlin语言和Jetpack Compose进行Android应用开发。不要使用java语言。
    - 遵循Material Design 3设计规范设计用户界面。
    - 利用Kotlin Flow和Coroutines进行响应式编程和异步操作管理。
    - 实现非常好的应用生命周期管理，确保应用在前台和后台都能正常运行。
    - 使用Room数据库进行本地数据存储和管理。
    - 实现适配不同Android设备的自适应布局。
    - 使用Kotlin的类型系统进行严格的类型检查，提高代码质量。
    - 编写详细的代码注释，并在代码中添加必要的错误处理和日志记录。
    - 实现适当的内存管理，避免内存泄漏。
    - 使用依赖注入框架(如Hilt)管理应用依赖。
    - 遵循MVVM架构模式进行应用开发。

    ### 解决问题时：
    - 全面阅读相关代码文件，理解所有代码的功能和逻辑。
    - 分析导致错误的原因，提出解决问题的思路。
    -不要假装你修好了任何问题。我来定义你有没有修好，不是你说修好就修好。提交给我测试，提供测试的步骤和期待的结果，让我来验证。
    - 解释你的操作。
    - 你在和一个不会代码的产品经理合作，解释所有背景信息，解释哪里出错，解释你为什么选择这个方式去修改。
    - 如果有两次连续的对话对于同一个问题还是持续出现没有修好，承认你不理解怎么修，开始debug模式：寻找可行的方式（加debug print，可视化的方式等）帮助我去找到问题出现在哪，我们一起确定哪里有问题。
    - 不要创建任何重复的类型或文件！！不要修改任何已经验证可行的代码！！
    - 与用户进行多次交互，根据反馈调整解决方案。
    - 当一个bug经过两次调整仍未解决时，你将启动系统二思考模式：
      1. 系统性分析bug产生的根本原因
      2. 提出可能的假设
      3. 设计验证假设的方法
      4. 提供三种不同的解决方案，并详细说明每种方案的优缺点
      5. 让用户根据实际情况选择最适合的方案

    ## 第三步：项目总结和优化
    - 完成任务后，反思完成步骤，思考项目可能存在的问题和改进方式。
    - 更新README.md文件，包括新增功能说明和优化建议。
    - 考虑使用Android的高级特性，如ARCore、ML Kit等来增强应用功能。
    - 优化应用性能，包括启动时间、内存使用和电池消耗。
    - 确保应用在不同Android版本上的兼容性。
    - 实现适当的混淆和安全措施。

    在整个过程中，始终参考[Android开发者文档](https://developer.android.com/docs)，确保使用最新的Android开发最佳实践。

# 项目架构

## 整体架构图
```
+------------------------------------------+
|                                          |
|             应用界面层 (UI)               |
|  +------------------------------------+  |
|  |        Compose UI 组件            |  |
|  |  +---------+  +---------+         |  |
|  |  | Screens |  |   组件   |         |  |
|  |  +---------+  +---------+         |  |
|  +------------------------------------+  |
|                    ↑↓                    |
|  +------------------------------------+  |
|  |            视图模型层              |  |
|  |  +---------+  +---------+         |  |
|  |  |ViewModels|  |  状态类  |         |  |
|  |  +---------+  +---------+         |  |
|  +------------------------------------+  |
|                    ↑↓                    |
|  +------------------------------------+  |
|  |           领域/业务层              |  |
|  |  +---------+  +---------+         |  |
|  |  |UseCase类 |  |Repository|         |  |
|  |  +---------+  +---------+         |  |
|  +------------------------------------+  |
|                    ↑↓                    |
|  +------------------------------------+  |
|  |            数据源层               |  |
|  |  +-----------+  +-----------+     |  |
|  |  |本地数据源  |  |远程数据源  |     |  |
|  |  | (Room DB) |  | (API/SDK) |     |  |
|  |  +-----------+  +-----------+     |  |
|  +------------------------------------+  |
|                                          |
+------------------------------------------+
         ↑                      ↑
+-----------------+  +----------------------+
| 通用工具和扩展   |  | 依赖注入 (Hilt/Koin) |
+-----------------+  +----------------------+
```

## 详细架构说明

### 1. UI层 (Presentation Layer)
- **屏幕组件 (Screens)**
  - 使用Jetpack Compose构建的界面
  - 根据功能模块分为不同的screens（如GoalManagementScreen, TimeStatisticsScreen等）
  - 遵循Material Design 3设计规范
  - 包含状态提升和事件下传的单向数据流模式

- **可复用UI组件 (Components)**
  - 独立且可复用的UI构建块
  - 包括自定义卡片、按钮、对话框等
  - 支持主题自定义和响应式设计

### 2. 视图模型层 (ViewModel Layer)
- **ViewModels**
  - 遵循MVVM架构模式
  - 管理UI相关的数据状态
  - 使用Kotlin Flow发布状态更新
  - 处理用户交互事件
  - 协调数据操作和业务逻辑

- **状态类 (State Classes)**
  - 不可变的UI状态表示
  - 使用sealed class或data class定义
  - 提供线程安全的状态更新机制

### 3. 领域/业务层 (Domain Layer)
- **用例类 (Use Cases)**
  - 封装特定业务逻辑
  - 提供单一职责的操作
  - 实现业务规则和验证

- **仓库接口 (Repository Interfaces)**
  - 定义数据操作的抽象接口
  - 实现领域层与数据层的解耦
  - 提供统一的数据访问模式

### 4. 数据源层 (Data Layer)
- **本地数据源 (Local Data Sources)**
  - Room数据库实现
  - 数据模型与实体映射
  - 本地缓存和离线支持
  - 使用DAO进行数据库操作

- **远程数据源 (Remote Data Sources)**
  - API客户端实现
  - 网络请求处理
  - AI服务集成
  - 第三方SDK集成

### 5. 支持系统
- **依赖注入**
  - 使用Hilt/Koin进行依赖管理
  - 模块化组件配置
  - 提供测试友好的依赖替换机制

- **通用工具与扩展**
  - Kotlin扩展函数
  - 实用工具类
  - 主题管理
  - 系统服务集成

## 主要模块

1. **目标管理模块**
   - 目标CRUD操作
   - 子任务管理
   - AI辅助目标拆解

2. **时间追踪模块**
   - 计时器功能
   - 番茄工作法实现
   - 专注统计

3. **数据分析模块**
   - 时间统计和分析
   - 效率趋势图表
   - 个性化建议

4. **系统设置模块**
   - 主题设置
   - 用户偏好管理
   - 应用配置

## 数据流向
1. 用户在UI层交互 → 触发事件传递到ViewModel
2. ViewModel处理事件 → 调用相应Use Case
3. Use Case执行业务逻辑 → 通过Repository访问数据
4. Repository协调数据源 → 从本地或远程获取/更新数据
5. 数据变化通过Flow/StateFlow/SharedFlow返回 → ViewModel更新UI状态
6. UI层观察状态变化 → 重组界面以反映新状态

## 技术栈
- **语言**: Kotlin
- **UI框架**: Jetpack Compose
- **异步处理**: Coroutines + Flow
- **本地存储**: Room
- **依赖注入**: Hilt/Koin
- **架构模式**: MVVM + Clean Architecture
- **设计规范**: Material Design 3

# 开发心得与最佳实践 💡

## 🚀 核心开发理念

### 1. 简单性原则 (KISS - Keep It Simple, Stupid)
```
复杂的方案往往引入更多问题，简单的方案更可靠
- ✅ 优先选择最简单的实现方式
- ✅ 避免过度设计和复杂的抽象
- ✅ 简单的代码更容易维护和调试
- ❌ 不要为了显示技术能力而复杂化
```

### 2. 用户体验至上
```
技术服务于体验，而不是相反
- 🎯 用户设置什么就显示什么，系统不做自动干预
- 🎯 操作要有即时反馈，避免让用户等待
- 🎯 界面状态要保持一致性
- 🎯 错误要优雅处理，给用户清晰的提示
```

## 🐛 调试与问题解决方法论

### 调试四步法
1. **问题重现** - 找到稳定的重现步骤
2. **日志分析** - 添加详细日志，观察数据流
3. **假设验证** - 提出假设并设计验证方法
4. **简化修复** - 选择最简单可靠的修复方案

### 经典问题模式与解决方案

#### 🔄 状态同步问题
**问题表现**: UI显示与数据不一致，"有时候同步，有时候不同步"
```kotlin
// ❌ 错误做法 - 复杂的状态管理
var isUserChanging by mutableStateOf(false)
LaunchedEffect(priority) { 
    if (!isUserChanging) { // 复杂的条件判断
        // 状态更新逻辑
    }
}

// ✅ 正确做法 - 简单直接的状态更新
fun onPrioritySelected(newPriority: Priority) {
    viewModelScope.launch {
        try {
            // 立即更新UI状态
            priority = newPriority
            // 保存到数据库
            taskRepository.updateTaskPriority(taskId, newPriority)
            // 发送刷新事件
            notificationCenter.send(RefreshTasksEvent())
        } catch (e: Exception) {
            // 出错时回滚状态
            priority = originalPriority
        }
    }
}
```

#### ⏱️ 时序问题
**问题表现**: 事件发送太早，接收者还没准备好
```kotlin
// ❌ 错误做法 - 复杂的延迟和协程取消
viewModelScope.launch {
    saveTask()
    navigateBack()
    delay(800) // 可能导致协程取消
    sendRefreshEvent()
}

// ✅ 正确做法 - 页面初始化时主动刷新
// 在目标页面添加
LaunchedEffect(Unit) {
    viewModel.refreshTasks() // 每次进入页面都刷新
}
```

#### 🔍 数据显示不一致问题
**问题根源**: 系统自动干预用户设置
```kotlin
// ❌ 错误做法 - 系统自动调整显示
fun determineUrgency(task: Task): Urgency {
    if (task.isOverdue && task.priority != LOW) {
        return CRITICAL // 自动提升显示级别
    }
    return task.priority.toUrgency()
}

// ✅ 正确做法 - 尊重用户设置
fun determineUrgency(task: Task): Urgency {
    return task.priority.toUrgency() // 直接映射，不做干预
}
```

## 🛠️ Android开发最佳实践

### Jetpack Compose技巧
```kotlin
// ✅ 状态提升模式
@Composable
fun TaskScreen(viewModel: TaskViewModel = hiltViewModel()) {
    val uiState by viewModel.uiState.collectAsState()
    
    TaskContent(
        tasks = uiState.tasks,
        onTaskClick = viewModel::onTaskClick,
        onAddTask = viewModel::onAddTask
    )
}

// ✅ 页面初始化刷新模式
LaunchedEffect(Unit) {
    viewModel.refreshData() // 确保数据最新
}

// ✅ 错误处理模式
when (val result = uiState.result) {
    is Loading -> LoadingIndicator()
    is Success -> TaskList(result.data)
    is Error -> ErrorMessage(result.error)
}
```

### Room数据库优化
```kotlin
// ✅ 使用Flow进行响应式查询
@Query("SELECT * FROM tasks WHERE completed = 0")
fun getActiveTasks(): Flow<List<Task>>

// ✅ 事务操作确保数据一致性
@Transaction
suspend fun updateTaskWithSubtasks(task: Task, subtasks: List<Subtask>) {
    updateTask(task)
    deleteSubtasks(task.id)
    insertSubtasks(subtasks)
}
```

## 📝 代码质量标准

### 日志记录规范
```kotlin
// ✅ 结构化日志 - 便于调试和监控
Log.d("TaskUpdate", "[${this::class.simpleName}] ===== 开始更新任务 =====")
Log.d("TaskUpdate", "[${this::class.simpleName}] 任务ID: $taskId")
Log.d("TaskUpdate", "[${this::class.simpleName}] 新状态: $newStatus")
Log.d("TaskUpdate", "[${this::class.simpleName}] ✓ 更新成功")
```

### 错误处理模式
```kotlin
// ✅ 全面的错误处理
suspend fun saveTask(task: Task): Result<Unit> {
    return try {
        taskDao.insert(task)
        Result.success(Unit)
    } catch (e: SQLiteConstraintException) {
        Log.e("TaskRepo", "任务保存失败 - 约束冲突", e)
        Result.failure(TaskSaveException.DuplicateTask)
    } catch (e: Exception) {
        Log.e("TaskRepo", "任务保存失败 - 未知错误", e)
        Result.failure(TaskSaveException.Unknown(e))
    }
}
```

## 🔧 实用调试技巧

### 1. 渐进式调试
```bash
# 从简单到复杂的调试命令
adb logcat -c                           # 清空日志
adb logcat -s MyTag                     # 过滤单个标签
adb logcat -s Tag1 Tag2 Tag3           # 过滤多个标签
adb logcat -s MyTag | head -50         # 限制输出行数
```

### 2. 状态验证技巧
```kotlin
// ✅ 在关键点添加状态检查
private fun debugCurrentState() {
    Log.d("StateDebug", """
        ===== 当前状态快照 =====
        任务数量: ${tasks.size}
        选中优先级: $selectedPriority
        UI状态: $uiState
        数据库状态: ${database.getTaskCount()}
        =======================
    """.trimIndent())
}
```

## 🎯 性能优化经验

### Compose性能优化
```kotlin
// ✅ 使用remember避免重复计算
@Composable
fun TaskList(tasks: List<Task>) {
    val sortedTasks = remember(tasks) {
        tasks.sortedByDescending { it.priority }
    }
    
    LazyColumn {
        items(
            items = sortedTasks,
            key = { it.id } // 重要：提供稳定的key
        ) { task ->
            TaskItem(task = task)
        }
    }
}
```

## 📊 通知栏每日回顾数据驱动原则

### 核心要求：使用真实统计数据，杜绝硬编码内容

#### ✅ 正确做法：基于实际数据的通知
```kotlin
// 优先使用 DailyReviewDataService 生成完整数据
val dailyReviewDataService = DailyReviewDataService(...)
val reviewData = dailyReviewDataService.generateDailyReviewData(today)

// 使用真实数据发送通知
notificationManager.showDailyReview(
    reviewData = reviewData,
    settings = settings
)
```

#### ❌ 避免的做法：硬编码测试数据
```kotlin
// 不要在生产环境中使用固定数值
notificationManager.showDailyReview(
    completedTasks = 5,  // ❌ 硬编码数据
    totalTasks = 8,      // ❌ 硬编码数据
    settings = settings
)
```

### 数据收集维度
- **任务统计**: 完成率、优先级分布、逾期情况
- **习惯统计**: 完成率、连续天数、习惯类型分析
- **专注统计**: 总时长、会话次数、平均专注时长
- **感想统计**: 记录数量、平均评分、心情分布
- **目标统计**: 活跃目标数、完成进度、目标类型

### 智能评分算法权重
- 任务完成情况：30%
- 习惯培养情况：25%
- 专注时间管理：25%
- 感想记录情况：10%
- 目标进展情况：10%

### 降级处理策略
```kotlin
try {
    // 🎯 优先：使用完整数据服务
    val reviewData = dailyReviewDataService.generateDailyReviewData(today)
    notificationManager.showDailyReview(reviewData, settings)
} catch (e: Exception) {
    // 🔄 降级：使用基础任务统计（仍基于真实数据）
    val todayTasks = taskRepository.getTodayTasks()
    val completedTasks = todayTasks.count { it.isCompleted }
    notificationManager.showDailyReview(completedTasks, todayTasks.size, settings)
}
```

### 个性化内容生成原则
- **动态标题**: 基于评分和表现生成
- **智能洞察**: 从数据中提取有价值的观察
- **个性化建议**: 根据用户表现提供改进建议
- **鼓励消息**: 评分驱动的正向反馈

### 实现检查清单
#### 🔍 代码审查要点
- [ ] 确认 `DailyReviewAlarmReceiver` 优先使用 `DailyReviewDataService`
- [ ] 验证降级处理仍使用真实任务数据，而非硬编码
- [ ] 检查测试代码中是否误用了硬编码数据
- [ ] 确保 `DailyReviewNotificationGenerator` 基于实际数据生成内容

#### 🚫 禁止的硬编码模式
```kotlin
// ❌ 绝对禁止：固定的测试数值
completedTasks = 5
totalTasks = 8
completionRate = 62

// ❌ 绝对禁止：固定的鼓励消息
"今天完成得不错！"
"继续保持！"
"明天会更好！"

// ❌ 绝对禁止：固定的统计数据
focusTime = 120
habitCount = 3
goalProgress = 75
```

#### ✅ 推荐的数据驱动模式
```kotlin
// ✅ 从数据库获取真实数据
val todayTasks = taskRepository.getTasksByDate(today)
val completedTasks = todayTasks.count { it.isCompleted }
val totalTasks = todayTasks.size

// ✅ 基于真实数据计算
val completionRate = if (totalTasks > 0) (completedTasks * 100 / totalTasks) else 0

// ✅ 数据驱动的内容生成
val encouragementMessage = when {
    completionRate >= 90 -> generateHighPerformanceMessage(reviewData)
    completionRate >= 70 -> generateGoodPerformanceMessage(reviewData)
    completionRate >= 50 -> generateAveragePerformanceMessage(reviewData)
    else -> generateImprovementMessage(reviewData)
}
```

### 通知栏每日回顾架构设计

#### 核心组件职责分离
```
📊 DailyReviewDataService
├── 数据收集：从各Repository获取真实数据
├── 数据分析：计算统计指标和评分
└── 数据整合：生成完整的DailyReviewData

📝 DailyReviewNotificationGenerator
├── 内容生成：基于数据生成个性化文案
├── 模板管理：不同评分区间的消息模板
└── 本地化：支持多语言和个性化表达

📱 TimeFlowNotificationManager
├── 通知展示：格式化并发送通知
├── 双模式支持：新数据模式 + 兼容模式
└── 样式配置：遵循用户通知偏好

⏰ DailyReviewAlarmReceiver
├── 定时触发：每日固定时间执行
├── 智能降级：新功能失败时的备用方案
└── 错误恢复：完整的异常处理机制
```

#### 数据流向设计
```
用户活动数据 → Repository层 → DailyReviewDataService →
数据分析和评分 → DailyReviewNotificationGenerator →
个性化内容生成 → TimeFlowNotificationManager →
通知展示给用户
```

### 质量保证措施

#### 数据完整性验证
```kotlin
// ✅ 数据验证示例
private fun validateReviewData(reviewData: DailyReviewData): Boolean {
    return reviewData.taskStats.totalTasks >= 0 &&
           reviewData.habitStats.totalHabits >= 0 &&
           reviewData.focusStats.totalMinutes >= 0 &&
           reviewData.overallScore in 0..100 &&
           reviewData.insights.isNotEmpty()
}
```

#### 错误处理最佳实践
```kotlin
// ✅ 完整的错误处理
suspend fun generateDailyReview(): DailyReviewData {
    return try {
        val reviewData = dailyReviewDataService.generateDailyReviewData()
        if (validateReviewData(reviewData)) {
            reviewData
        } else {
            Log.w(TAG, "数据验证失败，使用安全默认值")
            createSafeDefaultReviewData()
        }
    } catch (e: Exception) {
        Log.e(TAG, "生成每日回顾失败", e)
        createEmptyReviewData()
    }
}
```

### 测试和监控指导

#### 单元测试重点
```kotlin
// ✅ 测试数据收集准确性
@Test
fun `DailyReviewDataService should collect accurate task statistics`() {
    // 准备测试数据
    val testTasks = listOf(
        createTask(isCompleted = true, priority = HIGH),
        createTask(isCompleted = false, priority = MEDIUM)
    )

    // 执行测试
    val taskStats = dataService.generateTaskStats(today)

    // 验证结果
    assertEquals(2, taskStats.totalTasks)
    assertEquals(1, taskStats.completedTasks)
    assertEquals(50, taskStats.completionRate)
}

// ✅ 测试内容生成逻辑
@Test
fun `NotificationGenerator should create personalized content based on score`() {
    val highScoreData = createReviewData(overallScore = 95)
    val content = generator.generateNotificationContent(highScoreData)

    assertTrue(content.title.contains("🎉"))
    assertTrue(content.insights.isNotEmpty())
    assertFalse(content.longText.contains("硬编码"))
}
```

#### 日志监控要点
```kotlin
// ✅ 关键节点日志
Log.d("DailyReview", "开始收集数据 - 日期: $date")
Log.d("DailyReview", "任务统计: ${taskStats.completedTasks}/${taskStats.totalTasks}")
Log.d("DailyReview", "综合评分: $overallScore")
Log.d("DailyReview", "通知内容长度: ${content.longText.length}")
Log.d("DailyReview", "✅ 每日回顾通知发送成功")
```

#### 性能监控指标
- 数据收集耗时（目标 < 500ms）
- 内容生成耗时（目标 < 200ms）
- 通知发送成功率（目标 > 99%）
- 降级处理触发频率（目标 < 1%）

### 用户体验优化

#### 通知时机优化
```kotlin
// ✅ 智能通知时机
private fun shouldSendDailyReview(): Boolean {
    val now = LocalTime.now()
    val userPreferredTime = getUserPreferredReviewTime()

    // 确保在用户设定的时间范围内
    return now.isAfter(userPreferredTime.minusMinutes(5)) &&
           now.isBefore(userPreferredTime.plusMinutes(30))
}
```

#### 内容个性化程度
- 根据用户历史表现调整鼓励语调
- 基于用户活跃时段优化建议内容
- 考虑用户目标类型定制洞察重点
- 根据用户偏好调整通知详细程度

## 📚 持续学习要点

### 技术债务管理
- 🔍 定期review代码，识别复杂度过高的部分
- 🛠️ 重构前先写测试，确保功能不变
- 📖 保持对新Android特性的学习
- 💡 关注用户反馈，优先修复影响体验的问题

### 团队协作经验
- 📝 写清楚的commit message和PR描述
- 🤝 代码review时既要指出问题，也要学习别人的优秀实践
- 🎯 专注解决实际问题，避免过度工程化
- 💬 遇到复杂问题时，先讨论方案再实现

## 🏆 成功项目的特征

1. **用户体验优先** - 功能服务于体验，而不是炫技
2. **代码简洁清晰** - 六个月后的自己能轻松理解
3. **测试覆盖充分** - 核心功能有可靠的测试
4. **性能表现良好** - 启动快、响应快、内存占用合理
5. **维护成本可控** - 新功能容易添加，bug容易修复

---

> **核心信念**: 最好的代码是用户感觉不到技术存在的代码。我们的目标是创造无缝的用户体验，而不是展示复杂的技术架构。简单、可靠、用户友好 - 这是永恒的目标。