package com.timeflow.app.ui.screen.settings

import com.amazonaws.auth.AWSCredentials
import com.amazonaws.auth.AWSCredentialsProvider
import com.amazonaws.auth.BasicAWSCredentials
import com.amazonaws.auth.CognitoCachingCredentialsProvider
import com.amazonaws.regions.Region
import com.amazonaws.regions.Regions
import com.amazonaws.services.s3.AmazonS3Client
import com.amazonaws.services.s3.model.*
// 七牛云HTTP API实现 (不依赖SDK)
import java.security.MessageDigest
import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec
import java.util.Base64
import java.net.HttpURLConnection

import java.io.OutputStreamWriter
// import com.tencent.cos.xml.CosXmlService
// import com.tencent.cos.xml.CosXmlServiceConfig
// import com.tencent.cos.xml.exception.CosXmlClientException
// import com.tencent.cos.xml.exception.CosXmlServiceException
// import com.tencent.cos.xml.listener.CosXmlResultListener
// import com.tencent.cos.xml.model.CosXmlRequest
// import com.tencent.cos.xml.model.CosXmlResult
// import com.tencent.cos.xml.model.`object`.*
// import com.tencent.cos.xml.model.bucket.GetBucketRequest
// import com.tencent.qcloud.core.auth.QCloudCredentialProvider
// import com.tencent.qcloud.core.auth.BasicQCloudCredentials
// import com.tencent.qcloud.core.auth.StaticCredentialProvider
import com.timeflow.app.data.model.Task
import com.timeflow.app.data.model.Priority
import com.timeflow.app.data.model.TaskType
import kotlinx.coroutines.suspendCancellableCoroutine
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import kotlinx.coroutines.withContext
import kotlinx.coroutines.Dispatchers
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString
import android.util.Log
import java.io.ByteArrayInputStream
import java.text.SimpleDateFormat
import java.util.*
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

// import okhttp3.OkHttpClient
// import okhttp3.Request

/**
 * 云服务提供商枚举
 */
enum class CloudProvider(
    val displayName: String,
    val description: String,
    val endpointExample: String
) {
    AWS_S3("Amazon S3", "亚马逊云存储服务", "s3.amazonaws.com"),
    QINIU_CLOUD("七牛云", "七牛云对象存储", "s3-cn-east-1.qiniucs.com"),
    TENCENT_CLOUD("腾讯云", "腾讯云对象存储COS", "cos.ap-beijing.myqcloud.com"),
    ALIYUN_OSS("阿里云OSS", "阿里云对象存储服务", "oss-cn-hangzhou.aliyuncs.com")
}

/**
 * 云存储配置数据类
 */
@Serializable
data class CloudStorageConfig(
    val provider: String = CloudProvider.AWS_S3.name,
    val accessKeyId: String = "",
    val secretAccessKey: String = "",
    val bucketName: String = "",
    val region: String = "",
    val endpoint: String = "",
    val customDomain: String = "" // 自定义域名，用于七牛云等
)

/**
 * S3配置数据类 (保持向后兼容)
 */
@Serializable
data class S3Config(
    val accessKeyId: String,
    val secretAccessKey: String,
    val bucketName: String,
    val region: String,
    val endpoint: String = "",
    val provider: String = CloudProvider.AWS_S3.name
) {
    /**
     * 转换为CloudStorageConfig
     */
    fun toCloudStorageConfig(): CloudStorageConfig {
        return CloudStorageConfig(
            provider = provider,
            accessKeyId = accessKeyId,
            secretAccessKey = secretAccessKey,
            bucketName = bucketName,
            region = region,
            endpoint = endpoint
        )
    }

    companion object {
        /**
         * 从CloudStorageConfig创建S3Config
         */
        fun fromCloudStorageConfig(config: CloudStorageConfig): S3Config {
            return S3Config(
                provider = config.provider,
                accessKeyId = config.accessKeyId,
                secretAccessKey = config.secretAccessKey,
                bucketName = config.bucketName,
                region = config.region,
                endpoint = config.endpoint
            )
        }
    }
}

/**
 * 简化的任务序列化模型
 */
@Serializable
data class SerializableTask(
    val id: String,
    val title: String,
    val description: String = "",
    val dueDate: String? = null,
    val startDate: String? = null,
    val createdAt: String,
    val updatedAt: String,
    val completedAt: String? = null,
    val isCompleted: Boolean = false,
    val priority: String? = null,
    val type: String = "NORMAL",
    val parentTaskId: String? = null,
    val hasSubtasks: Boolean = false,
    val childTasksCount: Int? = null,
    val completedChildTasksCount: Int? = null,
    val depth: Int = 0,
    val orderIndex: Int = 0,
    val groupId: String? = null,
    val groupType: String? = null,
    val estimatedTimeMinutes: Int = 0,
    val actualTimeMinutes: Int = 0,
    val progress: Float = 0f,
    val aiGenerated: Boolean = false,
    val emotionState: String? = null,
    val status: String = "待办",
    val displayInTaskList: Boolean = true,
    val reminderTime: String? = null,
    val goalId: String? = null,
    val dateManuallyModified: Boolean = false,
    val isFloatingTask: Boolean = false,
    val floatingWeekStart: String? = null,
    val floatingWeekEnd: String? = null,
    val scheduledDate: String? = null,
    val floatingTaskOrder: Int = 0
)

/**
 * 任务模型转换器
 */
object TaskSerializer {
    private val dateFormatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME
    
    fun toSerializable(task: Task): SerializableTask {
        return SerializableTask(
            id = task.id,
            title = task.title,
            description = task.description,
            dueDate = task.dueDate?.format(dateFormatter),
            startDate = task.startDate?.format(dateFormatter),
            createdAt = task.createdAt.format(dateFormatter),
            updatedAt = task.updatedAt.format(dateFormatter),
            completedAt = task.completedAt?.format(dateFormatter),
            isCompleted = task.isCompleted,
            priority = task.priority?.name,
            type = task.type.getName(),
            parentTaskId = task.parentTaskId,
            hasSubtasks = task.hasSubtasks,
            childTasksCount = task.childTasksCount,
            completedChildTasksCount = task.completedChildTasksCount,
            depth = task.depth,
            orderIndex = task.orderIndex,
            groupId = task.groupId,
            groupType = task.groupType,
            estimatedTimeMinutes = task.estimatedTimeMinutes,
            actualTimeMinutes = task.actualTimeMinutes,
            progress = task.progress,
            aiGenerated = task.aiGenerated,
            emotionState = task.emotionState,
            status = task.status,
            displayInTaskList = task.displayInTaskList,
            reminderTime = task.reminderTime?.format(dateFormatter),
            goalId = task.goalId,
            dateManuallyModified = task.dateManuallyModified,
            isFloatingTask = task.isFloatingTask,
            floatingWeekStart = task.floatingWeekStart?.format(dateFormatter),
            floatingWeekEnd = task.floatingWeekEnd?.format(dateFormatter),
            scheduledDate = task.scheduledDate?.format(dateFormatter),
            floatingTaskOrder = task.floatingTaskOrder
        )
    }
    
    fun fromSerializable(serializable: SerializableTask): Task {
        return Task(
            id = serializable.id,
            title = serializable.title,
            description = serializable.description,
            dueDate = serializable.dueDate?.let { LocalDateTime.parse(it, dateFormatter) },
            startDate = serializable.startDate?.let { LocalDateTime.parse(it, dateFormatter) },
            createdAt = LocalDateTime.parse(serializable.createdAt, dateFormatter),
            updatedAt = LocalDateTime.parse(serializable.updatedAt, dateFormatter),
            completedAt = serializable.completedAt?.let { LocalDateTime.parse(it, dateFormatter) },
            isCompleted = serializable.isCompleted,
            priority = serializable.priority?.let { Priority.valueOf(it) },
            type = TaskType.valueOf(serializable.type),
            parentTaskId = serializable.parentTaskId,
            hasSubtasks = serializable.hasSubtasks,
            childTasksCount = serializable.childTasksCount,
            completedChildTasksCount = serializable.completedChildTasksCount,
            depth = serializable.depth,
            orderIndex = serializable.orderIndex,
            groupId = serializable.groupId,
            groupType = serializable.groupType,
            estimatedTimeMinutes = serializable.estimatedTimeMinutes,
            actualTimeMinutes = serializable.actualTimeMinutes,
            progress = serializable.progress,
            aiGenerated = serializable.aiGenerated,
            emotionState = serializable.emotionState,
            status = serializable.status,
            displayInTaskList = serializable.displayInTaskList,
            reminderTime = serializable.reminderTime?.let { LocalDateTime.parse(it, dateFormatter) },
            goalId = serializable.goalId,
            dateManuallyModified = serializable.dateManuallyModified,
            isFloatingTask = serializable.isFloatingTask,
            floatingWeekStart = serializable.floatingWeekStart?.let { LocalDateTime.parse(it, dateFormatter) },
            floatingWeekEnd = serializable.floatingWeekEnd?.let { LocalDateTime.parse(it, dateFormatter) },
            scheduledDate = serializable.scheduledDate?.let { LocalDateTime.parse(it, dateFormatter) },
            floatingTaskOrder = serializable.floatingTaskOrder
        )
    }
}

/**
 * 云存储客户端抽象接口
 */
interface CloudStorageClient {
    suspend fun testConnection(): Result<String>
    suspend fun uploadData(data: ByteArray, fileName: String, metadata: Map<String, String> = emptyMap()): Result<String>
    suspend fun downloadData(fileName: String? = null): Result<ByteArray>
    suspend fun listFiles(prefix: String = ""): Result<List<String>>
}

/**
 * 同步仓库接口
 */
interface SyncRepository {
    suspend fun testConnection(config: CloudStorageConfig): Result<String>
    suspend fun uploadData(config: CloudStorageConfig, data: List<Task>): Result<String>
    suspend fun downloadData(config: CloudStorageConfig): Result<List<Task>>

    // 新增资源管理方法
    suspend fun uploadResource(config: CloudStorageConfig, resource: com.timeflow.app.ui.screen.settings.LocalResource): Result<String>
    suspend fun listCloudResources(config: CloudStorageConfig): Result<Set<String>>

    // 保持向后兼容的S3方法
    suspend fun testS3Connection(config: S3Config): Result<String>
    suspend fun uploadDataToS3(config: S3Config, data: List<Task>): Result<String>
    suspend fun downloadDataFromS3(config: S3Config): Result<List<Task>>
}

/**
 * 云存储客户端工厂
 */
object CloudStorageClientFactory {
    fun createClient(config: CloudStorageConfig): CloudStorageClient {
        return when (CloudProvider.valueOf(config.provider)) {
            CloudProvider.AWS_S3 -> AwsS3StorageClient(config)
            CloudProvider.QINIU_CLOUD -> QiniuCloudStorageClient(config)
            CloudProvider.TENCENT_CLOUD -> TencentCloudStorageClient(config)
            CloudProvider.ALIYUN_OSS -> AliyunOssStorageClient(config)
        }
    }
}

/**
 * AWS S3存储客户端实现
 */
class AwsS3StorageClient(private val config: CloudStorageConfig) : CloudStorageClient {

    private fun createS3Client(): AmazonS3Client {
        val credentials = BasicAWSCredentials(config.accessKeyId, config.secretAccessKey)
        val credentialsProvider = object : AWSCredentialsProvider {
            override fun getCredentials(): AWSCredentials = credentials
            override fun refresh() {}
        }

        val region = try {
            Region.getRegion(Regions.fromName(config.region))
        } catch (e: Exception) {
            Log.w("AwsS3StorageClient", "无法解析区域 ${config.region}，使用默认区域 us-east-1")
            Region.getRegion(Regions.US_EAST_1)
        }

        return AmazonS3Client(credentialsProvider, region)
    }

    override suspend fun testConnection(): Result<String> = suspendCancellableCoroutine { continuation ->
        try {
            val s3Client = createS3Client()

            // 测试连接：尝试列出存储桶中的对象
            val listObjectsRequest = ListObjectsRequest()
                .withBucketName(config.bucketName)
                .withMaxKeys(1)

            s3Client.listObjects(listObjectsRequest)

            Log.d("AwsS3StorageClient", "AWS S3连接测试成功")
            continuation.resume(Result.success("连接成功"))

        } catch (e: Exception) {
            Log.e("AwsS3StorageClient", "连接测试失败", e)
            continuation.resume(Result.failure(e))
        }
    }

    override suspend fun uploadData(data: ByteArray, fileName: String, metadata: Map<String, String>): Result<String> =
        suspendCancellableCoroutine { continuation ->
            try {
                val s3Client = createS3Client()
                val putObjectRequest = PutObjectRequest(
                    config.bucketName,
                    fileName,
                    ByteArrayInputStream(data),
                    ObjectMetadata().apply {
                        contentLength = data.size.toLong()
                        contentType = "application/json"
                        metadata.forEach { (key, value) ->
                            addUserMetadata(key, value)
                        }
                    }
                )

                s3Client.putObject(putObjectRequest)
                Log.d("AwsS3StorageClient", "数据上传成功: $fileName")
                continuation.resume(Result.success("上传成功: $fileName"))
            } catch (e: Exception) {
                Log.e("AwsS3StorageClient", "数据上传失败", e)
                continuation.resume(Result.failure(e))
            }
        }

    override suspend fun downloadData(fileName: String?): Result<ByteArray> =
        suspendCancellableCoroutine { continuation ->
            try {
                val s3Client = createS3Client()

                val targetFileName = fileName ?: run {
                    // 找到最新的备份文件
                    val listObjectsRequest = ListObjectsRequest()
                        .withBucketName(config.bucketName)
                        .withPrefix("timeflow_backup_")

                    val objectListing = s3Client.listObjects(listObjectsRequest)
                    val latestBackup = objectListing.objectSummaries
                        .filter { it.key.startsWith("timeflow_backup_") && it.key.endsWith(".json") }
                        .maxByOrNull { it.lastModified }

                    latestBackup?.key ?: throw Exception("未找到备份文件")
                }

                val getObjectRequest = GetObjectRequest(config.bucketName, targetFileName)
                val s3Object = s3Client.getObject(getObjectRequest)
                val data = s3Object.objectContent.readBytes()

                Log.d("AwsS3StorageClient", "数据下载成功: $targetFileName")
                continuation.resume(Result.success(data))
            } catch (e: Exception) {
                Log.e("AwsS3StorageClient", "数据下载失败", e)
                continuation.resume(Result.failure(e))
            }
        }

    override suspend fun listFiles(prefix: String): Result<List<String>> =
        suspendCancellableCoroutine { continuation ->
            try {
                val s3Client = createS3Client()
                val listObjectsRequest = ListObjectsRequest()
                    .withBucketName(config.bucketName)
                    .withPrefix(prefix)

                val objectListing = s3Client.listObjects(listObjectsRequest)
                val fileNames = objectListing.objectSummaries.map { it.key }

                continuation.resume(Result.success(fileNames))
            } catch (e: Exception) {
                Log.e("AwsS3StorageClient", "文件列表获取失败", e)
                continuation.resume(Result.failure(e))
            }
        }
}

/**
 * 七牛云存储客户端实现（基于HTTP API的真实实现）
 */
class QiniuCloudStorageClient(private val config: CloudStorageConfig) : CloudStorageClient {

    companion object {
        private const val QINIU_API_BASE = "https://api.qiniu.com"
        // 尝试使用华东区域的上传端点
        private const val QINIU_UPLOAD_BASE = "https://up-z0.qiniup.com"
        // 备用端点
        private const val QINIU_UPLOAD_BASE_BACKUP = "https://upload.qiniup.com"
    }

    /**
     * 生成七牛云管理API认证签名 (QBox认证)
     */
    private fun generateQiniuAuth(method: String, path: String, body: String = ""): String {
        val signingStr = "$method $path\nHost: api.qiniu.com\n\n$body"
        val mac = Mac.getInstance("HmacSHA1")
        val secretKeySpec = SecretKeySpec(config.secretAccessKey.toByteArray(), "HmacSHA1")
        mac.init(secretKeySpec)
        val signature = Base64.getUrlEncoder().withoutPadding().encodeToString(mac.doFinal(signingStr.toByteArray()))
        return "QBox ${config.accessKeyId}:$signature"
    }

    /**
     * 生成上传Token (七牛云基础格式，先测试最简单的策略)
     */
    private fun generateUploadToken(bucket: String, key: String? = null): String {
        val deadline = System.currentTimeMillis() / 1000 + 3600 // 1小时有效期

        // 构建最基础的上传策略（先不使用returnBody）
        val policy = if (key != null) {
            // 指定文件名的策略
            """{"scope":"$bucket:$key","deadline":$deadline}"""
        } else {
            // 不指定文件名的策略
            """{"scope":"$bucket","deadline":$deadline}"""
        }

        Log.d("QiniuCloudStorageClient", "生成上传策略: $policy")

        // 第一步：对策略进行Base64编码
        val encodedPolicy = Base64.getUrlEncoder().withoutPadding().encodeToString(policy.toByteArray(Charsets.UTF_8))
        Log.d("QiniuCloudStorageClient", "编码后的策略: $encodedPolicy")

        // 第二步：对编码后的策略进行HMAC-SHA1签名
        val mac = Mac.getInstance("HmacSHA1")
        val secretKeySpec = SecretKeySpec(config.secretAccessKey.toByteArray(Charsets.UTF_8), "HmacSHA1")
        mac.init(secretKeySpec)
        val signature = Base64.getUrlEncoder().withoutPadding().encodeToString(mac.doFinal(encodedPolicy.toByteArray(Charsets.UTF_8)))
        Log.d("QiniuCloudStorageClient", "生成的签名: $signature")

        // 第三步：拼接最终的上传Token
        val uploadToken = "${config.accessKeyId}:$signature:$encodedPolicy"
        Log.d("QiniuCloudStorageClient", "最终上传Token: ${uploadToken.take(50)}...")

        return uploadToken
    }

    override suspend fun testConnection(): Result<String> = withContext(Dispatchers.IO) {
        try {
            // 验证配置参数
            if (config.accessKeyId.isBlank()) {
                return@withContext Result.failure(Exception("AccessKey ID 不能为空"))
            }
            if (config.secretAccessKey.isBlank()) {
                return@withContext Result.failure(Exception("SecretAccessKey 不能为空"))
            }
            if (config.bucketName.isBlank()) {
                return@withContext Result.failure(Exception("Bucket名称不能为空"))
            }

            Log.d("QiniuCloudStorageClient", "开始测试七牛云连接...")
            Log.d("QiniuCloudStorageClient", "Bucket: ${config.bucketName}")
            Log.d("QiniuCloudStorageClient", "AccessKey: ${config.accessKeyId.take(8)}...")

            // 通过生成上传token来验证AccessKey和SecretKey的有效性
            try {
                val testToken = generateUploadToken(config.bucketName, "test_connection_file.txt")
                Log.d("QiniuCloudStorageClient", "✅ 上传Token生成成功，长度: ${testToken.length}")

                // 验证token格式是否正确 (应该包含三个部分，用冒号分隔)
                val tokenParts = testToken.split(":")
                if (tokenParts.size == 3) {
                    Log.d("QiniuCloudStorageClient", "✅ Token格式验证通过")

                    // 尝试简单的网络连接测试到七牛云上传服务器
                    try {
                        val testUrl = java.net.URL(QINIU_UPLOAD_BASE)
                        val testConnection = testUrl.openConnection() as HttpURLConnection
                        testConnection.requestMethod = "HEAD"
                        testConnection.connectTimeout = 10000
                        testConnection.readTimeout = 10000

                        val responseCode = testConnection.responseCode
                        Log.d("QiniuCloudStorageClient", "上传服务器连通性测试: $responseCode")

                        if (responseCode in 200..499) { // 2xx, 3xx, 4xx都表示服务器可达
                            Log.d("QiniuCloudStorageClient", "✅ 七牛云连接测试成功")
                            Result.success("✅ 七牛云连接成功！\n" +
                                    "Bucket: ${config.bucketName}\n" +
                                    "AccessKey: ${config.accessKeyId.take(8)}...\n" +
                                    "状态: 认证信息有效，服务器可达")
                        } else {
                            Log.w("QiniuCloudStorageClient", "服务器响应异常: $responseCode")
                            Result.success("⚠️ 七牛云连接部分成功\n" +
                                    "Bucket: ${config.bucketName}\n" +
                                    "AccessKey: ${config.accessKeyId.take(8)}...\n" +
                                    "状态: 认证信息有效，但服务器连接异常")
                        }
                    } catch (networkException: Exception) {
                        Log.w("QiniuCloudStorageClient", "网络连接测试失败", networkException)
                        Result.success("⚠️ 七牛云认证成功\n" +
                                "Bucket: ${config.bucketName}\n" +
                                "AccessKey: ${config.accessKeyId.take(8)}...\n" +
                                "状态: 认证信息有效，网络连接可能有问题")
                    }
                } else {
                    Log.e("QiniuCloudStorageClient", "Token格式错误: $testToken")
                    Result.failure(Exception("生成的Token格式不正确，请检查AccessKey和SecretKey"))
                }
            } catch (tokenException: Exception) {
                Log.e("QiniuCloudStorageClient", "Token生成失败", tokenException)
                Result.failure(Exception("无法生成上传Token，请检查AccessKey和SecretKey是否正确"))
            }
        } catch (e: Exception) {
            Log.e("QiniuCloudStorageClient", "七牛云连接测试异常", e)
            Result.failure(e)
        }
    }

    override suspend fun uploadData(data: ByteArray, fileName: String, metadata: Map<String, String>): Result<String> = withContext(Dispatchers.IO) {
        try {
            // 验证配置
            if (config.accessKeyId.isBlank() || config.secretAccessKey.isBlank()) {
                return@withContext Result.failure(Exception("七牛云认证信息不完整"))
            }

            Log.d("QiniuCloudStorageClient", "开始上传到七牛云: $fileName (${data.size} bytes)")
            Log.d("QiniuCloudStorageClient", "Bucket: ${config.bucketName}")
            Log.d("QiniuCloudStorageClient", "AccessKey: ${config.accessKeyId.take(8)}...")
            Log.d("QiniuCloudStorageClient", "SecretKey: ${config.secretAccessKey.take(8)}...")

            // 验证凭据格式
            if (config.accessKeyId.length < 20) {
                Log.w("QiniuCloudStorageClient", "AccessKey长度可能不正确: ${config.accessKeyId.length}")
            }
            if (config.secretAccessKey.length < 20) {
                Log.w("QiniuCloudStorageClient", "SecretKey长度可能不正确: ${config.secretAccessKey.length}")
            }

            // 先尝试不指定key的简单策略
            val uploadToken = generateUploadToken(config.bucketName, null)
            Log.d("QiniuCloudStorageClient", "上传Token已生成，长度: ${uploadToken.length}")
            Log.d("QiniuCloudStorageClient", "使用简单策略（不指定key）")

            // 构建multipart/form-data请求
            val boundary = "----WebKitFormBoundary" + System.currentTimeMillis()
            val url = java.net.URL(QINIU_UPLOAD_BASE)
            val connection = url.openConnection() as HttpURLConnection

            connection.requestMethod = "POST"
            connection.setRequestProperty("Content-Type", "multipart/form-data; boundary=$boundary")
            connection.connectTimeout = 60000
            connection.readTimeout = 120000
            connection.doOutput = true

            val outputStream = connection.outputStream
            val writer = OutputStreamWriter(outputStream, "UTF-8")

            // 添加token字段
            writer.write("--$boundary\r\n")
            writer.write("Content-Disposition: form-data; name=\"token\"\r\n\r\n")
            writer.write("$uploadToken\r\n")

            // 添加key字段（必须与Token中的key完全一致）
            writer.write("--$boundary\r\n")
            writer.write("Content-Disposition: form-data; name=\"key\"\r\n\r\n")
            writer.write("$fileName\r\n")
            Log.d("QiniuCloudStorageClient", "表单中使用的key: $fileName")

            // 添加文件字段
            writer.write("--$boundary\r\n")
            writer.write("Content-Disposition: form-data; name=\"file\"; filename=\"$fileName\"\r\n")
            writer.write("Content-Type: application/octet-stream\r\n\r\n")
            writer.flush()

            // 写入文件数据
            outputStream.write(data)
            outputStream.flush()

            writer.write("\r\n--$boundary--\r\n")
            writer.flush()
            writer.close()

            val responseCode = connection.responseCode
            Log.d("QiniuCloudStorageClient", "上传响应状态码: $responseCode")

            if (responseCode == 200) {
                val response = connection.inputStream.bufferedReader().readText()
                Log.d("QiniuCloudStorageClient", "✅ 七牛云上传成功: $fileName")
                Log.d("QiniuCloudStorageClient", "响应: $response")
                Result.success("✅ 上传成功: $fileName")
            } else {
                val errorResponse = connection.errorStream?.bufferedReader()?.readText() ?: "未知错误"
                Log.e("QiniuCloudStorageClient", "上传失败: $errorResponse")
                Log.e("QiniuCloudStorageClient", "请求详情:")
                Log.e("QiniuCloudStorageClient", "  - URL: ${connection.url}")
                Log.e("QiniuCloudStorageClient", "  - Method: ${connection.requestMethod}")
                Log.e("QiniuCloudStorageClient", "  - Content-Type: ${connection.getRequestProperty("Content-Type")}")
                Log.e("QiniuCloudStorageClient", "  - Token长度: ${uploadToken.length}")
                Log.e("QiniuCloudStorageClient", "  - 文件名: $fileName")
                Log.e("QiniuCloudStorageClient", "  - 数据大小: ${data.size} bytes")
                Result.failure(Exception("七牛云上传失败 (状态码: $responseCode): $errorResponse"))
            }
        } catch (e: Exception) {
            Log.e("QiniuCloudStorageClient", "七牛云上传异常", e)
            Result.failure(e)
        }
    }

    override suspend fun downloadData(fileName: String?): Result<ByteArray> = withContext(Dispatchers.IO) {
        try {
            // 验证配置
            if (config.accessKeyId.isBlank() || config.secretAccessKey.isBlank()) {
                return@withContext Result.failure(Exception("七牛云认证信息不完整"))
            }

            val targetFileName = fileName ?: "timeflow_backup_latest.json"

            Log.d("QiniuCloudStorageClient", "开始从七牛云下载: $targetFileName")

            // 构建下载URL
            val baseUrl = if (config.customDomain.isNotBlank()) {
                "https://${config.customDomain}/$targetFileName"
            } else {
                // 使用七牛云默认域名格式 (需要先获取bucket的域名)
                "https://${config.bucketName}.bkt.clouddn.com/$targetFileName"
            }

            Log.d("QiniuCloudStorageClient", "下载URL: $baseUrl")

            // 对于私有空间，需要生成下载凭证
            val downloadUrl = if (config.customDomain.isNotBlank() || isPrivateBucket()) {
                generatePrivateDownloadUrl(baseUrl)
            } else {
                baseUrl
            }

            // 使用HTTP客户端下载数据
            val connection = java.net.URL(downloadUrl).openConnection() as HttpURLConnection
            connection.connectTimeout = 30000 // 30秒连接超时
            connection.readTimeout = 60000 // 60秒读取超时

            val responseCode = connection.responseCode
            Log.d("QiniuCloudStorageClient", "下载响应状态码: $responseCode")

            if (responseCode == 200) {
                val inputStream = connection.inputStream
                val data = inputStream.readBytes()
                inputStream.close()

                Log.d("QiniuCloudStorageClient", "✅ 七牛云下载成功，数据大小: ${data.size} bytes")
                Result.success(data)
            } else {
                val errorResponse = connection.errorStream?.bufferedReader()?.readText() ?: "文件不存在或无权限访问"
                Log.e("QiniuCloudStorageClient", "下载失败: $errorResponse")
                Result.failure(Exception("七牛云下载失败 (状态码: $responseCode): $errorResponse"))
            }

        } catch (e: Exception) {
            Log.e("QiniuCloudStorageClient", "七牛云下载失败", e)
            Result.failure(e)
        }
    }

    /**
     * 生成私有空间的下载链接
     */
    private fun generatePrivateDownloadUrl(baseUrl: String): String {
        val deadline = System.currentTimeMillis() / 1000 + 3600 // 1小时有效期
        val urlWithDeadline = "$baseUrl?e=$deadline"

        val mac = Mac.getInstance("HmacSHA1")
        val secretKeySpec = SecretKeySpec(config.secretAccessKey.toByteArray(), "HmacSHA1")
        mac.init(secretKeySpec)
        val signature = Base64.getUrlEncoder().withoutPadding().encodeToString(mac.doFinal(urlWithDeadline.toByteArray()))

        return "$urlWithDeadline&token=${config.accessKeyId}:$signature"
    }

    /**
     * 判断是否为私有空间（简单实现，实际应该通过API查询）
     */
    private fun isPrivateBucket(): Boolean {
        // 简单实现：假设如果配置了自定义域名就是私有空间
        return config.customDomain.isNotBlank()
    }

    override suspend fun listFiles(prefix: String): Result<List<String>> = withContext(Dispatchers.IO) {
        try {
            // 验证配置
            if (config.accessKeyId.isBlank() || config.secretAccessKey.isBlank()) {
                return@withContext Result.failure(Exception("七牛云认证信息不完整"))
            }

            Log.d("QiniuCloudStorageClient", "开始获取七牛云文件列表，前缀: $prefix")

            // 构建列举文件的API请求
            val url = java.net.URL("$QINIU_API_BASE/list?bucket=${config.bucketName}&prefix=$prefix&limit=1000")
            val connection = url.openConnection() as HttpURLConnection

            connection.requestMethod = "POST"
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded")
            connection.setRequestProperty("Authorization", generateQiniuAuth("POST", "/list?bucket=${config.bucketName}&prefix=$prefix&limit=1000"))
            connection.connectTimeout = 30000
            connection.readTimeout = 30000

            val responseCode = connection.responseCode
            Log.d("QiniuCloudStorageClient", "文件列表API响应状态码: $responseCode")

            if (responseCode == 200) {
                val response = connection.inputStream.bufferedReader().readText()
                Log.d("QiniuCloudStorageClient", "API响应: $response")

                // 解析JSON响应（简单实现）
                val fileNames = parseFileListResponse(response)

                Log.d("QiniuCloudStorageClient", "✅ 七牛云文件列表获取成功，文件数量: ${fileNames.size}")
                fileNames.forEach { fileName ->
                    Log.d("QiniuCloudStorageClient", "  - $fileName")
                }

                Result.success(fileNames)
            } else {
                val errorResponse = connection.errorStream?.bufferedReader()?.readText() ?: "未知错误"
                Log.e("QiniuCloudStorageClient", "文件列表获取失败: $errorResponse")
                Result.failure(Exception("七牛云文件列表获取失败 (状态码: $responseCode): $errorResponse"))
            }
        } catch (e: Exception) {
            Log.e("QiniuCloudStorageClient", "七牛云文件列表获取异常", e)
            Result.failure(e)
        }
    }

    /**
     * 解析文件列表API响应（简单JSON解析）
     */
    private fun parseFileListResponse(response: String): List<String> {
        return try {
            // 简单的JSON解析，提取文件名
            val fileNames = mutableListOf<String>()

            // 查找items数组
            val itemsStart = response.indexOf("\"items\":")
            if (itemsStart != -1) {
                val itemsContent = response.substring(itemsStart)

                // 简单提取key字段
                val keyPattern = "\"key\":\"([^\"]+)\"".toRegex()
                keyPattern.findAll(itemsContent).forEach { match ->
                    fileNames.add(match.groupValues[1])
                }
            }

            fileNames
        } catch (e: Exception) {
            Log.e("QiniuCloudStorageClient", "解析文件列表响应失败", e)
            emptyList()
        }
    }
}

/**
 * 腾讯云存储客户端实现（暂时禁用，等待SDK依赖解决）
 */
class TencentCloudStorageClient(private val config: CloudStorageConfig) : CloudStorageClient {

    override suspend fun testConnection(): Result<String> {
        return Result.failure(Exception("腾讯云SDK暂未集成，请先配置正确的依赖"))
    }

    override suspend fun uploadData(data: ByteArray, fileName: String, metadata: Map<String, String>): Result<String> {
        return Result.failure(Exception("腾讯云SDK暂未集成，请先配置正确的依赖"))
    }

    override suspend fun downloadData(fileName: String?): Result<ByteArray> {
        return Result.failure(Exception("腾讯云SDK暂未集成，请先配置正确的依赖"))
    }

    override suspend fun listFiles(prefix: String): Result<List<String>> {
        return Result.failure(Exception("腾讯云SDK暂未集成，请先配置正确的依赖"))
    }
}

/**
 * 阿里云OSS存储客户端实现（暂时禁用，等待SDK依赖解决）
 */
class AliyunOssStorageClient(private val config: CloudStorageConfig) : CloudStorageClient {
    override suspend fun testConnection(): Result<String> {
        return Result.failure(Exception("阿里云OSS SDK暂未集成，请先配置正确的依赖"))
    }

    override suspend fun uploadData(data: ByteArray, fileName: String, metadata: Map<String, String>): Result<String> {
        return Result.failure(Exception("阿里云OSS SDK暂未集成，请先配置正确的依赖"))
    }

    override suspend fun downloadData(fileName: String?): Result<ByteArray> {
        return Result.failure(Exception("阿里云OSS SDK暂未集成，请先配置正确的依赖"))
    }

    override suspend fun listFiles(prefix: String): Result<List<String>> {
        return Result.failure(Exception("阿里云OSS SDK暂未集成，请先配置正确的依赖"))
    }
}

/**
 * 同步仓库实现类
 */
@Singleton
class SyncRepositoryImpl @Inject constructor() : SyncRepository {

    // 新的云存储方法实现
    override suspend fun testConnection(config: CloudStorageConfig): Result<String> {
        return try {
            val client = CloudStorageClientFactory.createClient(config)
            client.testConnection()
        } catch (e: Exception) {
            Log.e("SyncRepository", "连接测试失败", e)
            Result.failure(e)
        }
    }

    override suspend fun uploadData(config: CloudStorageConfig, data: List<Task>): Result<String> {
        return try {
            val client = CloudStorageClientFactory.createClient(config)

            // 转换为可序列化的模型
            val serializableTasks = data.map { TaskSerializer.toSerializable(it) }

            // 序列化数据为JSON
            val json = Json.encodeToString(serializableTasks)
            val jsonBytes = json.toByteArray(Charsets.UTF_8)

            // 生成带时间戳的文件名
            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val fileName = "timeflow_backup_$timestamp.json"

            // 创建元数据
            val metadata = mapOf(
                "app" to "TimeFlow",
                "version" to "1.0",
                "timestamp" to timestamp,
                "provider" to config.provider
            )

            client.uploadData(jsonBytes, fileName, metadata)
        } catch (e: Exception) {
            Log.e("SyncRepository", "数据上传失败", e)
            Result.failure(e)
        }
    }

    override suspend fun downloadData(config: CloudStorageConfig): Result<List<Task>> {
        return try {
            val client = CloudStorageClientFactory.createClient(config)

            val dataResult = client.downloadData()
            dataResult.fold(
                onSuccess = { jsonBytes ->
                    val jsonContent = String(jsonBytes, Charsets.UTF_8)
                    val serializableTasks = Json.decodeFromString<List<SerializableTask>>(jsonContent)
                    val tasks = serializableTasks.map { TaskSerializer.fromSerializable(it) }

                    Log.d("SyncRepository", "数据下载成功，任务数量: ${tasks.size}")
                    Result.success(tasks)
                },
                onFailure = { exception ->
                    Log.e("SyncRepository", "数据下载失败", exception)
                    Result.failure(exception)
                }
            )
        } catch (e: Exception) {
            Log.e("SyncRepository", "数据下载失败", e)
            Result.failure(e)
        }
    }

    // 保持向后兼容的S3方法
    private fun createS3Client(config: S3Config): AmazonS3Client {
        // 创建AWS凭证
        val credentials = BasicAWSCredentials(config.accessKeyId, config.secretAccessKey)

        // 创建凭证提供者
        val credentialsProvider = object : AWSCredentialsProvider {
            override fun getCredentials(): AWSCredentials = credentials
            override fun refresh() {
                // 不需要刷新静态凭证
            }
        }

        // 创建区域对象
        val region = try {
            Region.getRegion(Regions.fromName(config.region))
        } catch (e: Exception) {
            Log.w("SyncRepository", "无法解析区域 ${config.region}，使用默认区域 us-east-1")
            Region.getRegion(Regions.US_EAST_1)
        }

        // 创建S3客户端
        val s3Client = AmazonS3Client(credentialsProvider, region)

        return s3Client
    }

    override suspend fun uploadResource(config: CloudStorageConfig, resource: com.timeflow.app.ui.screen.settings.LocalResource): Result<String> {
        return try {
            val client = CloudStorageClientFactory.createClient(config)

            // 读取文件内容
            val fileData = java.io.File(resource.filePath).readBytes()

            // 创建元数据
            val metadata = mapOf(
                "app" to "TimeFlow",
                "resource_type" to resource.type.name,
                "file_size" to resource.fileSize.toString(),
                "last_modified" to resource.lastModified.toString(),
                "upload_time" to System.currentTimeMillis().toString()
            )

            // 上传文件
            client.uploadData(fileData, "resources/${resource.fileName}", metadata)

        } catch (e: Exception) {
            Log.e("SyncRepository", "资源上传失败: ${resource.fileName}", e)
            Result.failure(e)
        }
    }

    override suspend fun listCloudResources(config: CloudStorageConfig): Result<Set<String>> {
        return try {
            val client = CloudStorageClientFactory.createClient(config)
            val result = client.listFiles("resources/")

            result.fold(
                onSuccess = { fileList ->
                    val resourceNames = fileList.map { fileName ->
                        fileName.removePrefix("resources/")
                    }.toSet()
                    Result.success(resourceNames)
                },
                onFailure = { exception ->
                    Result.failure(exception)
                }
            )
        } catch (e: Exception) {
            Log.e("SyncRepository", "获取云端资源列表失败", e)
            Result.failure(e)
        }
    }

    override suspend fun testS3Connection(config: S3Config): Result<String> {
        // 使用新的架构实现向后兼容
        return testConnection(config.toCloudStorageConfig())
    }

    override suspend fun uploadDataToS3(config: S3Config, data: List<Task>): Result<String> {
        // 使用新的架构实现向后兼容
        return uploadData(config.toCloudStorageConfig(), data)
    }

    override suspend fun downloadDataFromS3(config: S3Config): Result<List<Task>> {
        // 使用新的架构实现向后兼容
        return downloadData(config.toCloudStorageConfig())
    }
} 