{"logs": [{"outputFile": "com.timeflow.app-mergeDebugResources-85:/values-uz/values-uz.xml", "map": [{"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\5619827a7b7a666ff443af1afbf85287\\transformed\\preference-1.2.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,180,273,353,498,667,754", "endColumns": "74,92,79,144,168,86,78", "endOffsets": "175,268,348,493,662,749,828"}, "to": {"startLines": "50,55,174,178,184,190,191", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4651,5097,16292,16590,17158,17807,17894", "endColumns": "74,92,79,144,168,86,78", "endOffsets": "4721,5185,16367,16730,17322,17889,17968"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\e8414bc356727da52467a60536d06b50\\transformed\\material3-release\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,287,401,515,612,710,825,958,1067,1209,1293,1397,1491,1589,1703,1824,1933,2058,2181,2311,2479,2604,2725,2849,2970,3065,3163,3280,3406,3510,3620,3727,3850,3978,4091,4195,4279,4375,4469,4557,4643,4744,4824,4908,5008,5112,5208,5307,5395,5503,5603,5706,5845,5925,6041", "endColumns": "116,114,113,113,96,97,114,132,108,141,83,103,93,97,113,120,108,124,122,129,167,124,120,123,120,94,97,116,125,103,109,106,122,127,112,103,83,95,93,87,85,100,79,83,99,103,95,98,87,107,99,102,138,79,115,102", "endOffsets": "167,282,396,510,607,705,820,953,1062,1204,1288,1392,1486,1584,1698,1819,1928,2053,2176,2306,2474,2599,2720,2844,2965,3060,3158,3275,3401,3505,3615,3722,3845,3973,4086,4190,4274,4370,4464,4552,4638,4739,4819,4903,5003,5107,5203,5302,5390,5498,5598,5701,5840,5920,6036,6139"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5603,5720,5835,5949,6063,6160,6258,6373,6506,6615,6757,6841,6945,7039,7137,7251,7372,7481,7606,7729,7859,8027,8152,8273,8397,8518,8613,8711,8828,8954,9058,9168,9275,9398,9526,9639,9743,9827,9923,10017,10105,10191,10292,10372,10456,10556,10660,10756,10855,10943,11051,11151,11254,11393,11473,11589", "endColumns": "116,114,113,113,96,97,114,132,108,141,83,103,93,97,113,120,108,124,122,129,167,124,120,123,120,94,97,116,125,103,109,106,122,127,112,103,83,95,93,87,85,100,79,83,99,103,95,98,87,107,99,102,138,79,115,102", "endOffsets": "5715,5830,5944,6058,6155,6253,6368,6501,6610,6752,6836,6940,7034,7132,7246,7367,7476,7601,7724,7854,8022,8147,8268,8392,8513,8608,8706,8823,8949,9053,9163,9270,9393,9521,9634,9738,9822,9918,10012,10100,10186,10287,10367,10451,10551,10655,10751,10850,10938,11046,11146,11249,11388,11468,11584,11687"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\8502c7d6e294f2825540546f57fbfcc0\\transformed\\material-1.11.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,360,440,523,617,704,799,926,1010,1074,1177,1247,1314,1423,1486,1553,1612,1686,1749,1803,1918,1976,2038,2092,2167,2296,2386,2475,2616,2698,2780,2919,3005,3089,3149,3200,3266,3339,3417,3503,3584,3656,3733,3808,3879,3980,4074,4153,4249,4343,4417,4493,4579,4632,4719,4785,4870,4961,5023,5087,5150,5219,5321,5422,5518,5619,5683,5738", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,76,79,82,93,86,94,126,83,63,102,69,66,108,62,66,58,73,62,53,114,57,61,53,74,128,89,88,140,81,81,138,85,83,59,50,65,72,77,85,80,71,76,74,70,100,93,78,95,93,73,75,85,52,86,65,84,90,61,63,62,68,101,100,95,100,63,54,82", "endOffsets": "278,355,435,518,612,699,794,921,1005,1069,1172,1242,1309,1418,1481,1548,1607,1681,1744,1798,1913,1971,2033,2087,2162,2291,2381,2470,2611,2693,2775,2914,3000,3084,3144,3195,3261,3334,3412,3498,3579,3651,3728,3803,3874,3975,4069,4148,4244,4338,4412,4488,4574,4627,4714,4780,4865,4956,5018,5082,5145,5214,5316,5417,5513,5614,5678,5733,5816"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,54,56,57,60,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,177", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3004,3081,3161,3244,3338,4161,4256,4383,5033,5190,5293,5536,11692,11801,11864,11931,11990,12064,12127,12181,12296,12354,12416,12470,12545,12674,12764,12853,12994,13076,13158,13297,13383,13467,13527,13578,13644,13717,13795,13881,13962,14034,14111,14186,14257,14358,14452,14531,14627,14721,14795,14871,14957,15010,15097,15163,15248,15339,15401,15465,15528,15597,15699,15800,15896,15997,16061,16507", "endLines": "5,33,34,35,36,37,45,46,47,54,56,57,60,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,177", "endColumns": "12,76,79,82,93,86,94,126,83,63,102,69,66,108,62,66,58,73,62,53,114,57,61,53,74,128,89,88,140,81,81,138,85,83,59,50,65,72,77,85,80,71,76,74,70,100,93,78,95,93,73,75,85,52,86,65,84,90,61,63,62,68,101,100,95,100,63,54,82", "endOffsets": "328,3076,3156,3239,3333,3420,4251,4378,4462,5092,5288,5358,5598,11796,11859,11926,11985,12059,12122,12176,12291,12349,12411,12465,12540,12669,12759,12848,12989,13071,13153,13292,13378,13462,13522,13573,13639,13712,13790,13876,13957,14029,14106,14181,14252,14353,14447,14526,14622,14716,14790,14866,14952,15005,15092,15158,15243,15334,15396,15460,15523,15592,15694,15795,15891,15992,16056,16111,16585"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\2e72a71af7cea86f1e691bad46c56138\\transformed\\appcompat-1.6.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,405,487,587,704,789,867,958,1051,1146,1240,1334,1427,1522,1617,1708,1800,1884,1994,2100,2200,2308,2414,2516,2677,2776", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "205,300,400,482,582,699,784,862,953,1046,1141,1235,1329,1422,1517,1612,1703,1795,1879,1989,2095,2195,2303,2409,2511,2672,2771,2855"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,181", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,438,533,633,715,815,932,1017,1095,1186,1279,1374,1468,1562,1655,1750,1845,1936,2028,2112,2222,2328,2428,2536,2642,2744,2905,16899", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "433,528,628,710,810,927,1012,1090,1181,1274,1369,1463,1557,1650,1745,1840,1931,2023,2107,2217,2323,2423,2531,2637,2739,2900,2999,16978"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\6e1bec7b61f7c5e14c57eb6146ea2199\\transformed\\ui-release\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,289,393,500,596,679,769,862,945,1013,1080,1161,1244,1318,1401,1469", "endColumns": "98,84,103,106,95,82,89,92,82,67,66,80,82,73,82,67,116", "endOffsets": "199,284,388,495,591,674,764,857,940,1008,1075,1156,1239,1313,1396,1464,1581"}, "to": {"startLines": "48,49,51,52,53,58,59,172,173,175,176,179,180,182,185,186,187", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4467,4566,4726,4830,4937,5363,5446,16116,16209,16372,16440,16735,16816,16983,17327,17410,17478", "endColumns": "98,84,103,106,95,82,89,92,82,67,66,80,82,73,82,67,116", "endOffsets": "4561,4646,4825,4932,5028,5441,5531,16204,16287,16435,16502,16811,16894,17052,17405,17473,17590"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\443a6cfc0de72abe8b7a8ef4c7b7a3e3\\transformed\\core-1.12.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,360,460,568,672,791", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "152,254,355,455,563,667,786,887"}, "to": {"startLines": "38,39,40,41,42,43,44,183", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3425,3527,3629,3730,3830,3938,4042,17057", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "3522,3624,3725,3825,3933,4037,4156,17153"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\71256635af31e1aec955d3a558d276eb\\transformed\\foundation-release\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,100", "endOffsets": "161,262"}, "to": {"startLines": "188,189", "startColumns": "4,4", "startOffsets": "17595,17706", "endColumns": "110,100", "endOffsets": "17701,17802"}}]}]}