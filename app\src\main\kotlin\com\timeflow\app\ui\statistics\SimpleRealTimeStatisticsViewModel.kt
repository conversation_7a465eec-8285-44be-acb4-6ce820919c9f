package com.timeflow.app.ui.statistics

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.timeflow.app.data.service.SimpleRealDataStatisticsService
import com.timeflow.app.data.model.*
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.time.LocalDate
import java.time.YearMonth
import javax.inject.Inject

/**
 * 简化的真实时间统计ViewModel
 */
@HiltViewModel
class SimpleRealTimeStatisticsViewModel @Inject constructor(
    private val statisticsService: SimpleRealDataStatisticsService
) : ViewModel() {

    // 时间范围选择
    private val _selectedTimeRange = MutableStateFlow("本月")
    val selectedTimeRange: StateFlow<String> = _selectedTimeRange.asStateFlow()

    // 视图模式选择
    private val _selectedViewMode = MutableStateFlow(ViewMode.CONCRETE)
    val selectedViewMode: StateFlow<ViewMode> = _selectedViewMode.asStateFlow()

    // 加载状态
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    // 错误状态
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    // 月度数据
    private val _monthlyData = MutableStateFlow<MonthlyStatisticsData?>(null)
    val monthlyData: StateFlow<MonthlyStatisticsData?> = _monthlyData.asStateFlow()

    // 年度数据
    private val _yearlyData = MutableStateFlow<YearlyStatisticsData?>(null)
    val yearlyData: StateFlow<YearlyStatisticsData?> = _yearlyData.asStateFlow()

    // 具象视图数据
    private val _concreteData = MutableStateFlow<ConcreteViewData?>(null)
    val concreteData: StateFlow<ConcreteViewData?> = _concreteData.asStateFlow()

    // 宏观视图数据
    private val _macroData = MutableStateFlow<MacroViewData?>(null)
    val macroData: StateFlow<MacroViewData?> = _macroData.asStateFlow()

    init {
        loadInitialData()
    }

    /**
     * 设置时间范围
     */
    fun setTimeRange(range: String) {
        _selectedTimeRange.value = range
        loadDataForTimeRange(range)
    }

    /**
     * 设置视图模式
     */
    fun setViewMode(mode: ViewMode) {
        _selectedViewMode.value = mode
        loadDataForViewMode(mode)
    }

    /**
     * 加载初始数据
     */
    private fun loadInitialData() {
        loadDataForTimeRange(_selectedTimeRange.value)
        loadDataForViewMode(_selectedViewMode.value)
    }

    /**
     * 根据时间范围加载数据
     */
    private fun loadDataForTimeRange(range: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null

                when (range) {
                    "本月", "上月" -> {
                        val yearMonth = if (range == "本月") {
                            YearMonth.now()
                        } else {
                            YearMonth.now().minusMonths(1)
                        }
                        
                        statisticsService.getMonthlyStatistics(yearMonth)
                            .catch { e ->
                                _error.value = "加载月度数据失败: ${e.message}"
                            }
                            .collect { data ->
                                _monthlyData.value = data
                            }
                    }
                    
                    "本年", "去年" -> {
                        val year = if (range == "本年") {
                            LocalDate.now().year
                        } else {
                            LocalDate.now().year - 1
                        }
                        
                        statisticsService.getYearlyStatistics(year)
                            .catch { e ->
                                _error.value = "加载年度数据失败: ${e.message}"
                            }
                            .collect { data ->
                                _yearlyData.value = data
                            }
                    }
                }
            } catch (e: Exception) {
                _error.value = "加载数据失败: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * 根据视图模式加载数据
     */
    private fun loadDataForViewMode(mode: ViewMode) {
        viewModelScope.launch {
            try {
                val startDate = getStartDateForCurrentRange()
                val endDate = getEndDateForCurrentRange()

                when (mode) {
                    ViewMode.CONCRETE -> {
                        statisticsService.getConcreteViewData(startDate, endDate)
                            .catch { e ->
                                _error.value = "加载具象视图数据失败: ${e.message}"
                            }
                            .collect { data ->
                                _concreteData.value = data
                            }
                    }
                    
                    ViewMode.MACRO -> {
                        statisticsService.getMacroViewData(startDate, endDate)
                            .catch { e ->
                                _error.value = "加载宏观视图数据失败: ${e.message}"
                            }
                            .collect { data ->
                                _macroData.value = data
                            }
                    }
                }
            } catch (e: Exception) {
                _error.value = "加载视图数据失败: ${e.message}"
            }
        }
    }

    /**
     * 获取当前范围的开始日期
     */
    private fun getStartDateForCurrentRange(): LocalDate {
        return when (_selectedTimeRange.value) {
            "本月" -> YearMonth.now().atDay(1)
            "上月" -> YearMonth.now().minusMonths(1).atDay(1)
            "本年" -> LocalDate.of(LocalDate.now().year, 1, 1)
            "去年" -> LocalDate.of(LocalDate.now().year - 1, 1, 1)
            else -> YearMonth.now().atDay(1)
        }
    }

    /**
     * 获取当前范围的结束日期
     */
    private fun getEndDateForCurrentRange(): LocalDate {
        return when (_selectedTimeRange.value) {
            "本月" -> YearMonth.now().atEndOfMonth()
            "上月" -> YearMonth.now().minusMonths(1).atEndOfMonth()
            "本年" -> LocalDate.of(LocalDate.now().year, 12, 31)
            "去年" -> LocalDate.of(LocalDate.now().year - 1, 12, 31)
            else -> YearMonth.now().atEndOfMonth()
        }
    }

    /**
     * 刷新数据
     */
    fun refreshData() {
        loadInitialData()
    }

    /**
     * 清除错误状态
     */
    fun clearError() {
        _error.value = null
    }
}
