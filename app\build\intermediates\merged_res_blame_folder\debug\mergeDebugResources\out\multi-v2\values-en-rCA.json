{"logs": [{"outputFile": "com.timeflow.app-mergeDebugResources-85:/values-en-rCA/values-en-rCA.xml", "map": [{"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\443a6cfc0de72abe8b7a8ef4c7b7a3e3\\transformed\\core-1.12.0\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,657,773", "endColumns": "95,101,98,98,103,101,115,100", "endOffsets": "146,248,347,446,550,652,768,869"}, "to": {"startLines": "29,30,31,32,33,34,35,111", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2762,2858,2960,3059,3158,3262,3364,11086", "endColumns": "95,101,98,98,103,101,115,100", "endOffsets": "2853,2955,3054,3153,3257,3359,3475,11182"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\5619827a7b7a666ff443af1afbf85287\\transformed\\preference-1.2.1\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,173,260,334,468,637,717", "endColumns": "67,86,73,133,168,79,75", "endOffsets": "168,255,329,463,632,712,788"}, "to": {"startLines": "38,42,103,106,112,118,119", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3654,4001,10433,10636,11187,11790,11870", "endColumns": "67,86,73,133,168,79,75", "endOffsets": "3717,4083,10502,10765,11351,11865,11941"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\6e1bec7b61f7c5e14c57eb6146ea2199\\transformed\\ui-release\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,279,373,472,558,640,730,819,903,968,1032,1110,1192,1265,1342,1408", "endColumns": "91,81,93,98,85,81,89,88,83,64,63,77,81,72,76,65,119", "endOffsets": "192,274,368,467,553,635,725,814,898,963,1027,1105,1187,1260,1337,1403,1523"}, "to": {"startLines": "36,37,39,40,41,43,44,101,102,104,105,107,108,110,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3480,3572,3722,3816,3915,4088,4170,10260,10349,10507,10572,10770,10848,11013,11356,11433,11499", "endColumns": "91,81,93,98,85,81,89,88,83,64,63,77,81,72,76,65,119", "endOffsets": "3567,3649,3811,3910,3996,4165,4255,10344,10428,10567,10631,10843,10925,11081,11428,11494,11614"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\2e72a71af7cea86f1e691bad46c56138\\transformed\\appcompat-1.6.1\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,10930", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,11008"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\71256635af31e1aec955d3a558d276eb\\transformed\\foundation-release\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,84", "endOffsets": "136,221"}, "to": {"startLines": "116,117", "startColumns": "4,4", "startOffsets": "11619,11705", "endColumns": "85,84", "endOffsets": "11700,11785"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\e8414bc356727da52467a60536d06b50\\transformed\\material3-release\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,289,400,514,613,708,820,956,1072,1208,1292,1391,1482,1579,1698,1823,1927,2054,2177,2305,2467,2588,2704,2827,2952,3044,3142,3259,3383,3480,3582,3684,3814,3953,4059,4158,4234,4330,4424,4511,4598,4700,4780,4864,4965,5066,5166,5265,5353,5459,5560,5664,5780,5860,5960", "endColumns": "117,115,110,113,98,94,111,135,115,135,83,98,90,96,118,124,103,126,122,127,161,120,115,122,124,91,97,116,123,96,101,101,129,138,105,98,75,95,93,86,86,101,79,83,100,100,99,98,87,105,100,103,115,79,99,94", "endOffsets": "168,284,395,509,608,703,815,951,1067,1203,1287,1386,1477,1574,1693,1818,1922,2049,2172,2300,2462,2583,2699,2822,2947,3039,3137,3254,3378,3475,3577,3679,3809,3948,4054,4153,4229,4325,4419,4506,4593,4695,4775,4859,4960,5061,5161,5260,5348,5454,5555,5659,5775,5855,5955,6050"}, "to": {"startLines": "45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4260,4378,4494,4605,4719,4818,4913,5025,5161,5277,5413,5497,5596,5687,5784,5903,6028,6132,6259,6382,6510,6672,6793,6909,7032,7157,7249,7347,7464,7588,7685,7787,7889,8019,8158,8264,8363,8439,8535,8629,8716,8803,8905,8985,9069,9170,9271,9371,9470,9558,9664,9765,9869,9985,10065,10165", "endColumns": "117,115,110,113,98,94,111,135,115,135,83,98,90,96,118,124,103,126,122,127,161,120,115,122,124,91,97,116,123,96,101,101,129,138,105,98,75,95,93,86,86,101,79,83,100,100,99,98,87,105,100,103,115,79,99,94", "endOffsets": "4373,4489,4600,4714,4813,4908,5020,5156,5272,5408,5492,5591,5682,5779,5898,6023,6127,6254,6377,6505,6667,6788,6904,7027,7152,7244,7342,7459,7583,7680,7782,7884,8014,8153,8259,8358,8434,8530,8624,8711,8798,8900,8980,9064,9165,9266,9366,9465,9553,9659,9760,9864,9980,10060,10160,10255"}}]}]}