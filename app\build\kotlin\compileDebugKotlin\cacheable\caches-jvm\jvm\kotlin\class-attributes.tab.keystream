com.timeflow.app.MainActivity'com.timeflow.app.MainActivity.Companion$com.timeflow.app.TimeFlowApplication.com.timeflow.app.TimeFlowApplication.Companion0com.timeflow.app.TimeFlowApplication.ReleaseTree*com.timeflow.app.ai.GoalCategoryClassifierAcom.timeflow.app.ai.GoalCategoryClassifier.CategoryRecommendation'com.timeflow.app.data.ai.model.AiConfig-com.timeflow.app.data.ai.model.AiConversation2com.timeflow.app.data.ai.model.ConversationMessage0com.timeflow.app.data.ai.model.MessageAttachment,com.timeflow.app.data.ai.model.MessageAction2com.timeflow.app.data.ai.model.ConversationContext,com.timeflow.app.data.ai.model.MessageSender*com.timeflow.app.data.ai.model.MessageType-com.timeflow.app.data.ai.model.AttachmentType)com.timeflow.app.data.ai.model.ActionType,com.timeflow.app.data.ai.model.MessageIntent1com.timeflow.app.data.ai.model.ConversationStatus)com.timeflow.app.data.ai.model.AiSettings2com.timeflow.app.data.ai.model.AiTaskDecomposition(com.timeflow.app.data.ai.model.AiSubTask-com.timeflow.app.data.ai.model.TaskDifficulty(com.timeflow.app.data.ai.model.TimeOfDay,com.timeflow.app.data.ai.model.AiTaskInsight3com.timeflow.app.data.ai.model.ProgressTrackingData0com.timeflow.app.data.ai.model.ProgressDataPoint1com.timeflow.app.data.ai.model.TaskRecommendation5com.timeflow.app.data.ai.model.TimeSlotRecommendation2com.timeflow.app.data.ai.model.ProductivityInsight1com.timeflow.app.data.ai.model.RecommendationType7com.timeflow.app.data.ai.model.RecommendationImportance.com.timeflow.app.data.ai.model.InsightCategory/com.timeflow.app.data.ai.model.AiTimeEstimation3com.timeflow.app.data.ai.model.SimilarTaskReference/com.timeflow.app.data.ai.model.AdjustmentFactor1com.timeflow.app.data.ai.model.UserEfficiencyData3com.timeflow.app.data.ai.model.EfficiencyTrendPoint)com.timeflow.app.data.ai.model.SentStatus9com.timeflow.app.data.algorithm.PeriodPredictionAlgorithmCcom.timeflow.app.data.algorithm.PeriodPredictionAlgorithm.CompanionJcom.timeflow.app.data.algorithm.PeriodPredictionAlgorithm.PredictionResultIcom.timeflow.app.data.algorithm.PeriodPredictionAlgorithm.CycleRegularityHcom.timeflow.app.data.algorithm.PeriodPredictionAlgorithm.BaseStatisticsIcom.timeflow.app.data.algorithm.PeriodPredictionAlgorithm.FinalPrediction1com.timeflow.app.data.analytics.AdvancedAnalytics.com.timeflow.app.data.analytics.TrendDirection,com.timeflow.app.data.analytics.HealthStatus/com.timeflow.app.data.analytics.HealthIndicator+com.timeflow.app.data.analytics.MonthlyData*com.timeflow.app.data.converter.Converters*com.timeflow.app.data.converter.TaskTagDto1com.timeflow.app.data.converter.DateTimeConverter3com.timeflow.app.data.converter.ListStringConverter2com.timeflow.app.data.converter.LocalDateConverter3com.timeflow.app.data.converter.StringListConverter-com.timeflow.app.data.converter.TaskConverter%com.timeflow.app.data.dao.AppUsageDao"com.timeflow.app.data.dao.CycleDao*com.timeflow.app.data.dao.EmotionRecordDao!com.timeflow.app.data.dao.GoalDao)com.timeflow.app.data.dao.GoalTemplateDao"com.timeflow.app.data.dao.HabitDao-com.timeflow.app.data.dao.HabitCompletionStat(com.timeflow.app.data.dao.KanbanBoardDao)com.timeflow.app.data.dao.KanbanColumnDao-com.timeflow.app.data.dao.MedicationRecordDao'com.timeflow.app.data.dao.ReflectionDao!com.timeflow.app.data.dao.TaskDao(com.timeflow.app.data.dao.TimeSessionDao&com.timeflow.app.data.dao.SessionStats'com.timeflow.app.data.dao.TaskTimeStats/com.timeflow.app.data.dao.TaskTimeStatsWithTags!com.timeflow.app.data.dao.WishDao$com.timeflow.app.data.db.AppDatabase.com.timeflow.app.data.db.AppDatabase.Companion+com.timeflow.app.data.entity.AppUsageEntity(com.timeflow.app.data.entity.CycleRecord0com.timeflow.app.data.entity.EmotionRecordEntity!com.timeflow.app.data.entity.Goal+com.timeflow.app.data.entity.Goal.Companion-com.timeflow.app.data.entity.Goal.$serializer(com.timeflow.app.data.entity.GoalSubTask2com.timeflow.app.data.entity.GoalSubTask.Companion4com.timeflow.app.data.entity.GoalSubTask.$serializer)com.timeflow.app.data.entity.GoalTemplate0com.timeflow.app.data.entity.GoalSubTaskTemplate"com.timeflow.app.data.entity.Habit,com.timeflow.app.data.entity.Habit.Companion.com.timeflow.app.data.entity.Habit.$serializer(com.timeflow.app.data.entity.HabitRecord2com.timeflow.app.data.entity.HabitRecord.Companion4com.timeflow.app.data.entity.HabitRecord.$serializer*com.timeflow.app.data.entity.HabitReminder(com.timeflow.app.data.entity.KanbanBoard)com.timeflow.app.data.entity.KanbanColumn-com.timeflow.app.data.entity.MedicationRecord%com.timeflow.app.data.entity.Priority-com.timeflow.app.data.entity.ReflectionEntity7com.timeflow.app.data.entity.ReflectionEntity.Companion9com.timeflow.app.data.entity.ReflectionEntity.$serializer*com.timeflow.app.data.entity.SymptomRecord!com.timeflow.app.data.entity.Task+com.timeflow.app.data.entity.Task.Companion-com.timeflow.app.data.entity.Task.$serializer(com.timeflow.app.data.entity.TaskClosure$com.timeflow.app.data.entity.TaskTag!com.timeflow.app.data.entity.Wish-com.timeflow.app.data.mapper.ReflectionMapper'com.timeflow.app.data.model.AiApiResult$com.timeflow.app.data.model.AiConfig)com.timeflow.app.data.model.AiConfigModel(com.timeflow.app.data.model.AppUsageData2com.timeflow.app.data.model.AppUsageData.Companion,com.timeflow.app.data.model.DataLoadingState6com.timeflow.app.data.model.DataLoadingState.Companion!com.timeflow.app.data.model.Event(com.timeflow.app.data.model.GoalPriority+com.timeflow.app.data.model.ReviewFrequency com.timeflow.app.data.model.Goal(com.timeflow.app.data.model.GoalCategory)com.timeflow.app.data.model.GoalTimeFrame(com.timeflow.app.data.model.CategoryType'com.timeflow.app.data.model.GoalSubTask(com.timeflow.app.data.model.GoalTemplate/com.timeflow.app.data.model.GoalSubTaskTemplate,com.timeflow.app.data.model.TemplateCategory$com.timeflow.app.data.model.GoalType*com.timeflow.app.data.model.WizardTemplate%com.timeflow.app.data.model.TimeFrame&com.timeflow.app.data.model.Difficulty/com.timeflow.app.data.model.CustomHabitCategory9com.timeflow.app.data.model.CustomHabitCategory.Companion)com.timeflow.app.data.model.HabitCategory.com.timeflow.app.data.model.HabitFrequencyType+com.timeflow.app.data.model.HabitDifficulty&com.timeflow.app.data.model.HabitModel,com.timeflow.app.data.model.HabitRecordModel.com.timeflow.app.data.model.HabitReminderModel+com.timeflow.app.data.model.HabitStatsModel/com.timeflow.app.data.model.HabitCompletionStat*com.timeflow.app.data.model.HabitFormState.com.timeflow.app.data.model.HistoryPeriodModel8com.timeflow.app.data.model.HistoryPeriodModel.Companion-com.timeflow.app.data.model.HistoryDataSource*com.timeflow.app.data.model.DataConfidence,com.timeflow.app.data.model.ValidationResult-com.timeflow.app.data.model.BatchImportResult6com.timeflow.app.data.model.ConflictResolutionStrategy(com.timeflow.app.data.model.DataConflict(com.timeflow.app.data.model.ConflictType1com.timeflow.app.data.model.HistoryAnalysisResult0com.timeflow.app.data.model.CycleRegularityLevel(com.timeflow.app.data.model.KanbanColumn&com.timeflow.app.data.model.Medication$com.timeflow.app.data.model.MoodType.com.timeflow.app.data.model.NotificationConfig0com.timeflow.app.data.model.NotificationCategory,com.timeflow.app.data.model.NotificationItem0com.timeflow.app.data.model.NotificationItemType.com.timeflow.app.data.model.NotificationOption.com.timeflow.app.data.model.ReminderTimeOption)com.timeflow.app.data.model.PaymentMethod)com.timeflow.app.data.model.PaymentStatus*com.timeflow.app.data.model.MembershipPlan(com.timeflow.app.data.model.PaymentOrder2com.timeflow.app.data.model.PaymentOrder.Companion4com.timeflow.app.data.model.PaymentOrder.$serializer*com.timeflow.app.data.model.PaymentRequest)com.timeflow.app.data.model.PaymentResult)com.timeflow.app.data.model.PaymentConfig,com.timeflow.app.data.model.PaymentException9com.timeflow.app.data.model.PaymentException.NetworkError?com.timeflow.app.data.model.PaymentException.ConfigurationError;com.timeflow.app.data.model.PaymentException.SignatureError9com.timeflow.app.data.model.PaymentException.OrderExpired;com.timeflow.app.data.model.PaymentException.AmountMismatch:com.timeflow.app.data.model.PaymentException.UserCancelled4com.timeflow.app.data.model.PaymentException.Unknown$com.timeflow.app.data.model.Priority*com.timeflow.app.data.model.RecurrenceType.com.timeflow.app.data.model.RecurrenceSettings8com.timeflow.app.data.model.RecurrenceSettings.Companion:com.timeflow.app.data.model.RecurrenceSettings.$serializer+com.timeflow.app.data.model.RecurringPeriod-com.timeflow.app.data.model.RecurringSettings'com.timeflow.app.data.model.MonthlyType'com.timeflow.app.data.model.WeekOfMonth(com.timeflow.app.data.model.EndCondition.com.timeflow.app.data.model.EndCondition.Never/com.timeflow.app.data.model.EndCondition.ByDate0com.timeflow.app.data.model.EndCondition.ByCount&com.timeflow.app.data.model.Reflection&com.timeflow.app.data.model.TimeFilter0com.timeflow.app.data.model.ReflectionAIAnalysis+com.timeflow.app.data.model.ReflectionStats(com.timeflow.app.data.model.ReminderType1com.timeflow.app.data.model.ProgressThresholdType+com.timeflow.app.data.model.ReminderSetting5com.timeflow.app.data.model.ReminderSetting.Companion1com.timeflow.app.data.model.MonthlyStatisticsData;com.timeflow.app.data.model.MonthlyStatisticsData.Companion0com.timeflow.app.data.model.YearlyStatisticsData:com.timeflow.app.data.model.YearlyStatisticsData.Companion,com.timeflow.app.data.model.ConcreteViewData6com.timeflow.app.data.model.ConcreteViewData.Companion)com.timeflow.app.data.model.MacroViewData3com.timeflow.app.data.model.MacroViewData.Companion*com.timeflow.app.data.model.TaskStatistics4com.timeflow.app.data.model.TaskStatistics.Companion*com.timeflow.app.data.model.TimeStatistics4com.timeflow.app.data.model.TimeStatistics.Companion*com.timeflow.app.data.model.GoalStatistics4com.timeflow.app.data.model.GoalStatistics.Companion+com.timeflow.app.data.model.HabitStatistics5com.timeflow.app.data.model.HabitStatistics.Companion-com.timeflow.app.data.model.EmotionStatistics7com.timeflow.app.data.model.EmotionStatistics.Companion,com.timeflow.app.data.model.MonthlyTrendData-com.timeflow.app.data.model.YearlyAchievement*com.timeflow.app.data.model.DailyBreakdown'com.timeflow.app.data.model.TagAnalysis1com.timeflow.app.data.model.TagAnalysis.Companion(com.timeflow.app.data.model.TagFrequency)com.timeflow.app.data.model.HourlyHeatmap3com.timeflow.app.data.model.HourlyHeatmap.Companion&com.timeflow.app.data.model.TrendPoint-com.timeflow.app.data.model.EfficiencyPattern7com.timeflow.app.data.model.EfficiencyPattern.Companion0com.timeflow.app.data.model.GoalProgressOverview:com.timeflow.app.data.model.GoalProgressOverview.Companion/com.timeflow.app.data.model.HabitStreakOverview9com.timeflow.app.data.model.HabitStreakOverview.Companion)com.timeflow.app.data.model.DailyTimeData-com.timeflow.app.data.model.EmotionTrendPoint$com.timeflow.app.data.model.ViewMode$com.timeflow.app.data.model.TaskType+com.timeflow.app.data.model.TaskType.NORMAL'com.timeflow.app.data.model.TaskType.AI.com.timeflow.app.data.model.TaskType.RECURRING-com.timeflow.app.data.model.TaskType.FLOATING.com.timeflow.app.data.model.TaskType.Companion com.timeflow.app.data.model.Task$com.timeflow.app.data.model.TimeSlot,com.timeflow.app.data.model.TimeSlotCategory%com.timeflow.app.data.model.TaskGroup)com.timeflow.app.data.model.TaskGroupType#com.timeflow.app.data.model.TaskTag$com.timeflow.app.data.model.TaskTime/com.timeflow.app.data.model.TaskTimeUpdateEvent,com.timeflow.app.data.model.TaskTimeConflict,com.timeflow.app.data.model.TimeConflictType'com.timeflow.app.data.model.TimeSession(com.timeflow.app.data.model.TimeSlotInfo(com.timeflow.app.data.model.ViewTimeSlot,com.timeflow.app.data.model.ViewTimeCategory%com.timeflow.app.data.model.WishModel(com.timeflow.app.data.model.WishCategory&com.timeflow.app.data.model.WishStatus*com.timeflow.app.data.model.WishDifficulty'com.timeflow.app.data.model.WorkManager:com.timeflow.app.data.notification.GoalNotificationManagerDcom.timeflow.app.data.notification.GoalNotificationManager.Companion2com.timeflow.app.data.notification.AchievementType2com.timeflow.app.data.notification.SmartSuggestion1com.timeflow.app.data.notification.SuggestionType<<EMAIL>=com.timeflow.app.data.notification.GoalDeadlineReminderWorker8com.timeflow.app.data.preferences.UserPreferencesManagerBcom.timeflow.app.data.preferences.UserPreferencesManager.Companion1com.timeflow.app.data.repository.AiTaskRepository5com.timeflow.app.data.repository.AiTaskRepositoryImpl/com.timeflow.app.data.repository.BaseRepository0com.timeflow.app.data.repository.CycleRepository;com.timeflow.app.data.repository.DefaultTemplateInitializer8com.timeflow.app.data.repository.EmotionRecordRepository<com.timeflow.app.data.repository.EmotionRecordRepositoryImpl0com.timeflow.app.data.repository.EventRepository4com.timeflow.app.data.repository.EventRepositoryImpl/com.timeflow.app.data.repository.GoalRepository*com.timeflow.app.data.repository.GoalStats1com.timeflow.app.data.repository.GoalWithSubtasks,com.timeflow.app.data.repository.AiApiResult3com.timeflow.app.data.repository.GoalRepositoryImpl7com.timeflow.app.data.repository.GoalTemplateRepository;com.timeflow.app.data.repository.GoalTemplateRepositoryImpl0com.timeflow.app.data.repository.HabitRepository3com.timeflow.app.data.repository.HabitAnalyticsData5com.timeflow.app.data.repository.HabitPerformanceData0com.timeflow.app.data.repository.HabitStreakData4com.timeflow.app.data.repository.HabitRepositoryImpl8com.timeflow.app.data.repository.HistoryPeriodRepositoryBcom.timeflow.app.data.repository.HistoryPeriodRepository.Companion6com.timeflow.app.data.repository.KanbanBoardRepository:com.timeflow.app.data.repository.KanbanBoardRepositoryImpl7com.timeflow.app.data.repository.KanbanColumnRepository;com.timeflow.app.data.repository.KanbanColumnRepositoryImpl1com.timeflow.app.data.repository.KanbanRepository5com.timeflow.app.data.repository.MedicationRepository9com.timeflow.app.data.repository.MedicationRepositoryImpl2com.timeflow.app.data.repository.SharedFilterState0com.timeflow.app.data.repository.PendingDeletion:com.timeflow.app.data.repository.PendingDeletion.Companion<com.timeflow.app.data.repository.PendingDeletion.$serializer;com.timeflow.app.data.repository.SharedPendingDeletionStateEcom.timeflow.app.data.repository.SharedPendingDeletionState.Companion/com.timeflow.app.data.repository.TaskRepository/com.timeflow.app.data.repository.TaskStatistics3com.timeflow.app.data.repository.TaskRepositoryImpl=com.timeflow.app.data.repository.TaskRepositoryImpl.Companion3com.timeflow.app.data.repository.TaskTimeRepository=com.timeflow.app.data.repository.TaskTimeRepository.Companion8com.timeflow.app.data.repository.TimeAnalyticsRepository<com.timeflow.app.data.repository.TimeAnalyticsRepositoryImpl6com.timeflow.app.data.repository.TimeSessionRepository/com.timeflow.app.data.repository.SessionSummary/com.timeflow.app.data.repository.DailyFocusData2com.timeflow.app.data.repository.TransactionHelper<com.timeflow.app.data.repository.TransactionHelper.Companion9com.timeflow.app.data.repository.UserPreferenceRepository0com.timeflow.app.data.repository.UserPreferences=com.timeflow.app.data.repository.UserPreferenceRepositoryImpl/com.timeflow.app.data.repository.WishRepository3com.timeflow.app.data.repository.WishRepositoryImpl=com.timeflow.app.data.service.SimpleRealDataStatisticsServiceGcom.timeflow.app.data.service.SimpleRealDataStatisticsService.Companion)com.timeflow.app.debug.PriorityUpdateTest#com.timeflow.app.di.AnalyticsModule$com.timeflow.app.di.AnalyticsTracker)com.timeflow.app.di.LocalAnalyticsTracker!com.timeflow.app.di.CrashReporter&com.timeflow.app.di.LocalCrashReporter"com.timeflow.app.di.AppInitializercom.timeflow.app.di.AppModule#com.timeflow.app.di.DataStoreModule2com.timeflow.app.di.DataStoreModule.ThemeDataStore7com.timeflow.app.di.DataStoreModule.MedicationDataStore9com.timeflow.app.di.DataStoreModule.NotificationDataStore"com.timeflow.app.di.DatabaseModulecom.timeflow.app.di.ImageModule$com.timeflow.app.di.MedicationModule"com.timeflow.app.di.PreferenceKeys$com.timeflow.app.di.ReflectionModule$com.timeflow.app.di.RepositoryModule,com.timeflow.app.di.RepositoryBindingsModule$com.timeflow.app.di.StatisticsModulecom.timeflow.app.di.SyncModule"com.timeflow.app.di.TaskTimeModulecom.timeflow.app.di.UtilsModule com.timeflow.app.di.ViewModelKey#com.timeflow.app.di.ViewModelModulecom.timeflow.app.di.WishModule*com.timeflow.app.di.WorkManagerInitializerKcom.timeflow.app.di.WorkManagerInitializer.WorkManagerInitializerEntryPoint%com.timeflow.app.di.WorkManagerModule/com.timeflow.app.domain.usecase.TaskTimeUseCase9com.timeflow.app.domain.usecase.TaskTimeUseCase.Companion=com.timeflow.app.domain.usecase.goal.QuickGoalCreationUseCase9com.timeflow.app.domain.usecase.goal.SmartTemplateUseCase5com.timeflow.app.initializer.RecurringTaskInitializerXcom.timeflow.app.initializer.RecurringTaskInitializer.RecurringTaskInitializerEntryPoint+com.timeflow.app.navigation.AppDestinations+com.timeflow.app.navigation.NavDestinations/com.timeflow.app.receiver.BootCompletedReceiver?com.timeflow.app.receiver.BootCompletedReceiver.PreferencesKeys9com.timeflow.app.receiver.BootCompletedReceiver.Companion2com.timeflow.app.receiver.DailyReviewAlarmReceiverBcom.timeflow.app.receiver.DailyReviewAlarmReceiver.PreferencesKeys<com.timeflow.app.receiver.DailyReviewAlarmReceiver.Companion2com.timeflow.app.receiver.FocusTimerActionReceiver<com.timeflow.app.receiver.FocusTimerActionReceiver.Companion,com.timeflow.app.receiver.HabitAlarmReceiver6com.timeflow.app.receiver.HabitAlarmReceiver.Companion+com.timeflow.app.receiver.TaskAlarmReceiver;com.timeflow.app.receiver.TaskAlarmReceiver.PreferencesKeys5com.timeflow.app.receiver.TaskAlarmReceiver.CompanionBcom.timeflow.app.receiver.TaskPersistentNotificationActionReceiverLcom.timeflow.app.receiver.TaskPersistentNotificationActionReceiver.Companion.com.timeflow.app.service.AiSuggestionScheduler8com.timeflow.app.service.AiSuggestionScheduler.Companion*com.timeflow.app.service.AutoBackupService4com.timeflow.app.service.AutoBackupService.Companion/com.timeflow.app.service.DailyReviewDataService9com.timeflow.app.service.DailyReviewDataService.Companion(com.timeflow.app.service.DailyReviewData"com.timeflow.app.service.TaskStats#com.timeflow.app.service.HabitStats#com.timeflow.app.service.FocusStats(com.timeflow.app.service.ReflectionStats"com.timeflow.app.service.GoalStats9com.timeflow.app.service.DailyReviewNotificationGeneratorCcom.timeflow.app.service.DailyReviewNotificationGenerator.Companion,com.timeflow.app.service.NotificationContent-com.timeflow.app.service.DailyReviewScheduler7com.timeflow.app.service.DailyReviewScheduler.Companion*com.timeflow.app.service.FocusTimerManager4com.timeflow.app.service.FocusTimerManager.Companion*com.timeflow.app.service.FocusTimerService4com.timeflow.app.service.FocusTimerService.Companion2com.timeflow.app.service.MedicationReminderManager<com.timeflow.app.service.MedicationReminderManager.Companion3com.timeflow.app.service.MedicationReminderReceiver1com.timeflow.app.service.MedicationActionReceiver2com.timeflow.app.service.NotificationConfigManager<com.timeflow.app.service.NotificationConfigManager.Companion$com.timeflow.app.service.ConfigStats0com.timeflow.app.service.NotificationTestService:com.timeflow.app.service.NotificationTestService.Companion'com.timeflow.app.service.PaymentManager1com.timeflow.app.service.PaymentManager.Companion-com.timeflow.app.service.RecurrenceCalculator7com.timeflow.app.service.RecurrenceCalculator.Companion-com.timeflow.app.service.RecurringTaskManager7com.timeflow.app.service.RecurringTaskManager.Companion:com.timeflow.app.service.TaskPersistentNotificationManagerDcom.timeflow.app.service.TaskPersistentNotificationManager.Companion:com.timeflow.app.service.TaskPersistentNotificationServiceDcom.timeflow.app.service.TaskPersistentNotificationService.CompanionFcom.timeflow.app.service.TaskPersistentNotificationService.LocalBinder.com.timeflow.app.service.TaskReminderScheduler8com.timeflow.app.service.TaskReminderScheduler.Companion,com.timeflow.app.service.TimeTrackingService6com.timeflow.app.service.TimeTrackingService.Companion8com.timeflow.app.service.TimeTrackingService.LocalBinder.com.timeflow.app.test.NotificationSettingsTest8com.timeflow.app.test.NotificationSettingsTest.Companion com.timeflow.app.ui.MainActivity*com.timeflow.app.ui.MainActivity.Companion&com.timeflow.app.ui.base.SafeViewModel"com.timeflow.app.ui.base.ViewState'com.timeflow.app.ui.base.ViewState.Idle*com.timeflow.app.ui.base.ViewState.Loading*com.timeflow.app.ui.base.ViewState.Success(com.timeflow.app.ui.base.ViewState.Error0com.timeflow.app.ui.component.goal.MorandiColors.com.timeflow.app.ui.component.goal.MonetColors.com.timeflow.app.ui.component.goal.DayProgress,com.timeflow.app.ui.component.goal.ChartData,com.timeflow.app.ui.component.goal.ChartType-com.timeflow.app.ui.components.SakuraParticle,com.timeflow.app.ui.language.LanguageManager><EMAIL>>com.timeflow.app.ui.language.LanguageSettingsViewModel.UiState4com.timeflow.app.ui.navigation.AnimationConfigurator=com.timeflow.app.ui.navigation.AnimationConfigurator.PageTypeHcom.timeflow.app.ui.navigation.AnimationConfigurator.NavigationDirection1com.timeflow.app.ui.navigation.AnimationOptimizerBcom.timeflow.app.ui.navigation.AnimationOptimizer.PerformanceLevel.com.timeflow.app.ui.navigation.AppDestinations8com.timeflow.app.ui.navigation.BottomNavAnimationManagerKcom.timeflow.app.ui.navigation.BottomNavAnimationManager.TabSwitchDirectionDcom.timeflow.app.ui.navigation.BottomNavAnimationManager.WeChatStyleDcom.timeflow.app.ui.navigation.BottomNavAnimationManager.TikTokStyleAcom.timeflow.app.ui.navigation.BottomNavAnimationManager.iOSStylePcom.timeflow.app.ui.navigation.BottomNavAnimationManager.BottomNavAnimationStyle6com.timeflow.app.ui.navigation.BottomNavAnimationState,com.timeflow.app.ui.navigation.BottomNavItem2com.timeflow.app.ui.navigation.MainPageTransitions/com.timeflow.app.ui.navigation.ModalTransitions/com.timeflow.app.ui.navigation.LightTransitions1com.timeflow.app.ui.navigation.SpecialTransitions3com.timeflow.app.ui.navigation.BottomNavTransitions.com.timeflow.app.ui.navigation.PageTransitions+com.timeflow.app.ui.navigation.AnimationSet%com.timeflow.app.ui.navigation.Screen*com.timeflow.app.ui.navigation.Screen.Home.com.timeflow.app.ui.navigation.Screen.TaskList0com.timeflow.app.ui.navigation.Screen.TaskDetail-com.timeflow.app.ui.navigation.Screen.AddTask.com.timeflow.app.ui.navigation.Screen.Calendar.com.timeflow.app.ui.navigation.Screen.Settings-com.timeflow.app.ui.navigation.Screen.Profile/com.timeflow.app.ui.navigation.Screen.Analytics.com.timeflow.app.ui.navigation.Screen.Discover2com.timeflow.app.ui.navigation.Screen.TimeTracking-com.timeflow.app.ui.navigation.ScreenWrappers0com.timeflow.app.ui.navigation.ScreenWrappersFix0com.timeflow.app.ui.navigation.TimeFlowNavigator4com.timeflow.app.ui.navigation.TimeFlowNavigatorImpl>com.timeflow.app.ui.optimization.ComposeRecompositionOptimizerLcom.timeflow.app.ui.optimization.ComposeRecompositionOptimizer.StableWrapperPcom.timeflow.app.ui.optimization.ComposeRecompositionOptimizer.BatchStateUpdaterIcom.timeflow.app.ui.optimization.ComposeRecompositionOptimizer.StableListHcom.timeflow.app.ui.optimization.ComposeRecompositionOptimizer.StableMapHcom.timeflow.app.ui.optimization.ComposeRecompositionOptimizer.StableSet,com.timeflow.app.ui.optimization.BuildConfig5com.timeflow.app.ui.optimization.TaskItemUiProperties?com.timeflow.app.ui.optimization.TaskItemUiProperties.Companion/com.timeflow.app.ui.optimization.TaskStatistics,com.timeflow.app.ui.screen.SharedFilterState*com.timeflow.app.ui.screen.ai.AIReviewData'com.timeflow.app.ui.screen.ai.TaskStats'com.timeflow.app.ui.screen.ai.TimeStats'com.timeflow.app.ui.screen.ai.GoalStats'com.timeflow.app.ui.screen.ai.AIInsight.com.timeflow.app.ui.screen.ai.AIRecommendation)com.timeflow.app.ui.screen.ai.InsightType+com.timeflow.app.ui.screen.ai.InsightImpact0com.timeflow.app.ui.screen.ai.RecommendationType4com.timeflow.app.ui.screen.ai.RecommendationPriority+com.timeflow.app.ui.screen.ai.TrendAnalysis(com.timeflow.app.ui.screen.ai.TrendPoint,com.timeflow.app.ui.screen.ai.TrendDirection0com.timeflow.app.ui.screen.ai.EfficiencyPatterns1com.timeflow.app.ui.screen.ai.InterruptionPattern1com.timeflow.app.ui.screen.ai.FocusSessionPattern2com.timeflow.app.ui.screen.ai.PersonalizedInsights*com.timeflow.app.ui.screen.ai.WorkingStyle0com.timeflow.app.ui.screen.ai.ComparisonAnalysis.com.timeflow.app.ui.screen.ai.PeriodComparison/com.timeflow.app.ui.screen.ai.AverageComparison2com.timeflow.app.ui.screen.ai.BestPeriodComparison.com.timeflow.app.ui.screen.ai.AIAnalysisResult/com.timeflow.app.ui.screen.ai.AIReviewViewModel9com.timeflow.app.ui.screen.ai.AIReviewViewModel.Companion1com.timeflow.app.ui.screen.analytics.ActivityData1com.timeflow.app.ui.screen.analytics.BestTimeItem5com.timeflow.app.ui.screen.analytics.FocusQualityItem9com.timeflow.app.ui.screen.analytics.AnalyticsDataServiceCcom.timeflow.app.ui.screen.analytics.AnalyticsDataService.Companion:com.timeflow.app.ui.screen.analytics.ProductivityScoreData3com.timeflow.app.ui.screen.analytics.GoalStatistics4com.timeflow.app.ui.screen.analytics.HabitStatistics9com.timeflow.app.ui.screen.analytics.TimeDistributionData<com.timeflow.app.ui.screen.analytics.AnalyticsInsightServiceFcom.timeflow.app.ui.screen.analytics.AnalyticsInsightService.Companion6com.timeflow.app.ui.screen.analytics.CompletedTaskData7com.timeflow.app.ui.screen.analytics.AnalyticsViewModelAcom.timeflow.app.ui.screen.analytics.AnalyticsViewModel.CompanionAcom.timeflow.app.ui.screen.analytics.AnalyticsViewModel.TimeRange4com.timeflow.app.ui.screen.calendar.CalendarViewType=com.timeflow.app.ui.screen.calendar.NoRippleInteractionSource3com.timeflow.app.ui.screen.calendar.EventLayoutInfo;com.timeflow.app.ui.screen.calendar.CalendarViewTypeContext1com.timeflow.app.ui.screen.calendar.DragDropState1com.timeflow.app.ui.screen.calendar.CalendarEvent1com.timeflow.app.ui.screen.calendar.CalendarState7com.timeflow.app.ui.screen.calendar.UserColorPreference5com.timeflow.app.ui.screen.calendar.CalendarViewModel?<EMAIL>?com.timeflow.app.ui.screen.discover.DiscoverViewModel.Companion1com.timeflow.app.ui.screen.goal.MockGoalViewModel2com.timeflow.app.ui.screen.goal.MockGoalRepository'com.timeflow.app.ui.screen.goal.SubGoal-com.timeflow.app.ui.screen.goal.TimeScaleData.com.timeflow.app.ui.screen.goal.DashboardStats,com.timeflow.app.ui.screen.goal.CategoryStat.com.timeflow.app.ui.screen.goal.RecentActivity$com.timeflow.app.ui.screen.goal.Goal.com.timeflow.app.ui.screen.goal.HeatmapDayData/com.timeflow.app.ui.screen.goal.SupportingHabit/com.timeflow.app.ui.screen.goal.TemplateUiState4com.timeflow.app.ui.screen.goal.TemplateUiState.Idle7com.timeflow.app.ui.screen.goal.TemplateUiState.Loading7com.timeflow.app.ui.screen.goal.TemplateUiState.Success5com.timeflow.app.ui.screen.goal.TemplateUiState.Error3com.timeflow.app.ui.screen.goal.TemplateFilterState1com.timeflow.app.ui.screen.goal.TemplateSortOrder5com.timeflow.app.ui.screen.goal.GoalTemplateViewModel-com.timeflow.app.ui.screen.goal.GoalViewModel+com.timeflow.app.ui.screen.goal.GoalUiState0com.timeflow.app.ui.screen.goal.GoalUiState.Idle3com.timeflow.app.ui.screen.goal.GoalUiState.Loading3com.timeflow.app.ui.screen.goal.GoalUiState.Success1com.timeflow.app.ui.screen.goal.GoalUiState.Error.com.timeflow.app.ui.screen.goal.BreakdownState3com.timeflow.app.ui.screen.goal.BreakdownState.Idle9com.timeflow.app.ui.screen.goal.BreakdownState.Processing<com.timeflow.app.ui.screen.goal.BreakdownState.ShowingResult8com.timeflow.app.ui.screen.goal.BreakdownState.Completed4com.timeflow.app.ui.screen.goal.BreakdownState.Error)com.timeflow.app.ui.screen.goal.GoalState1com.timeflow.app.ui.screen.goal.GoalState.Loading1com.timeflow.app.ui.screen.goal.GoalState.Success/com.timeflow.app.ui.screen.goal.GoalState.Error3com.timeflow.app.ui.screen.health.AddHabitViewModel1com.timeflow.app.ui.screen.health.AddHabitUiState-com.timeflow.app.ui.screen.health.QuickAction/com.timeflow.app.ui.screen.health.FrequencyType,com.timeflow.app.ui.screen.health.HabitStats5com.timeflow.app.ui.screen.health.OptimizedHabitStats4com.timeflow.app.ui.screen.health.PendingHabitAction1com.timeflow.app.ui.screen.health.HabitActionType+com.timeflow.app.ui.screen.health.HabitData-com.timeflow.app.ui.screen.health.CycleColors.com.timeflow.app.ui.screen.health.QuickSymptom5com.timeflow.app.ui.screen.health.PersonalizedInsight(com.timeflow.app.ui.screen.health.Tuple4Bcom.timeflow.app.ui.screen.health.UserPreferencesManagerEntryPoint4com.timeflow.app.ui.screen.health.MedicationTemplateAcom.timeflow.app.ui.screen.health.ProfessionalMedicationViewModel=com.timeflow.app.ui.screen.health.ProfessionalMedicationState8com.timeflow.app.ui.screen.health.ProfessionalMedication-com.timeflow.app.ui.screen.health.SafetyAlert/com.timeflow.app.ui.screen.health.AlertSeverity+com.timeflow.app.ui.screen.health.AlertType1com.timeflow.app.ui.screen.health.DrugInteraction1com.timeflow.app.ui.screen.health.InteractionType5com.timeflow.app.ui.screen.health.InteractionSeverity5com.timeflow.app.ui.screen.health.MissedDosesAnalysis3com.timeflow.app.ui.screen.health.EffectivenessData0com.timeflow.app.ui.screen.health.SafetyResource.com.timeflow.app.ui.screen.health.ResourceType2com.timeflow.app.ui.screen.health.EmergencyContact-com.timeflow.app.ui.screen.health.ContactType3com.timeflow.app.ui.screen.health.UndoSnackbarState9com.timeflow.app.ui.screen.health.DeleteUndoSnackbarState/com.timeflow.app.ui.screen.health.SmartReminder.com.timeflow.app.ui.screen.health.ReminderType2com.timeflow.app.ui.screen.health.ReminderPriority3com.timeflow.app.ui.screen.health.AdherenceAnalysis0com.timeflow.app.ui.screen.health.TrendDirection4com.timeflow.app.ui.screen.health.AdherenceRiskLevel2com.timeflow.app.ui.screen.health.ReminderSettings5com.timeflow.app.ui.screen.health.AdherenceChartPoint2com.timeflow.app.ui.screen.health.MissedDoseReason8com.timeflow.app.ui.screen.health.PersonalizedSuggestion0com.timeflow.app.ui.screen.health.SuggestionType4com.timeflow.app.ui.screen.health.SuggestionPriority4com.timeflow.app.ui.screen.health.HeatmapDialogState/com.timeflow.app.ui.screen.health.HeatmapPeriod2com.timeflow.app.ui.screen.health.HeatmapDataPoint-com.timeflow.app.ui.screen.health.HeatmapData/com.timeflow.app.ui.screen.health.DailySymptoms3com.timeflow.app.ui.screen.health.ModernSymptomItem-com.timeflow.app.ui.screen.home.TaskListState.com.timeflow.app.ui.screen.home.TaskRowUiState8com.timeflow.app.ui.screen.home.TaskRowUiState.Companion.com.timeflow.app.ui.screen.milestone.Milestone6com.timeflow.app.ui.screen.milestone.MilestoneCategory6com.timeflow.app.ui.screen.milestone.MilestoneInsights4com.timeflow.app.ui.screen.milestone.MilestoneRoutes7com.timeflow.app.ui.screen.milestone.MonetMorandiColors5com.timeflow.app.ui.screen.milestone.LongPressHandler2com.timeflow.app.ui.screen.milestone.MilestoneType5com.timeflow.app.ui.screen.milestone.MilestoneUiState7com.timeflow.app.ui.screen.milestone.MilestoneViewModel/com.timeflow.app.ui.screen.milestone.TextFormat-com.timeflow.app.ui.screen.milestone.ViewMode1com.timeflow.app.ui.screen.profile.EmotionTrigger+com.timeflow.app.ui.screen.profile.ViewMode0com.timeflow.app.ui.screen.profile.EmotionFilter0com.timeflow.app.ui.screen.profile.TimelineGroup3com.timeflow.app.ui.screen.profile.EmotionChartData-com.timeflow.app.ui.screen.profile.DailyTrend3com.timeflow.app.ui.screen.profile.EmotionStatistic4com.timeflow.app.ui.screen.profile.EmotionStatRecord/com.timeflow.app.ui.screen.profile.EmotionTrend3com.timeflow.app.ui.screen.profile.AIAnalysisResult3com.timeflow.app.ui.screen.profile.HeatmapDataPoint5com.timeflow.app.ui.screen.profile.EmotionHeatmapData,com.timeflow.app.ui.screen.profile.TimeRange9com.timeflow.app.ui.screen.profile.EmotionStatisticsState=com.timeflow.app.ui.screen.profile.EmotionStatisticsViewModel.com.timeflow.app.ui.screen.profile.EmotionType0com.timeflow.app.ui.screen.profile.EmotionRecord(com.timeflow.app.ui.screen.profile.Habit3com.timeflow.app.ui.screen.profile.ProfileViewModel1com.timeflow.app.ui.screen.reflection.GoalSummary0com.timeflow.app.ui.screen.reflection.Reflection2com.timeflow.app.ui.screen.reflection.ContentBlock4com.timeflow.app.ui.screen.reflection.ReflectionType0com.timeflow.app.ui.screen.reflection.TimeFilter4com.timeflow.app.ui.screen.reflection.AdvancedFilter;com.timeflow.app.ui.screen.reflection.ReflectionScreenState:com.timeflow.app.ui.screen.reflection.ReflectionRepository=com.timeflow.app.ui.screen.reflection.SearchSuggestionService<com.timeflow.app.ui.screen.reflection.ReflectionCreatedEvent9com.timeflow.app.ui.screen.reflection.ReflectionListState/com.timeflow.app.ui.screen.reflection.SortOrder9com.timeflow.app.ui.screen.reflection.ReflectionViewModel?com.timeflow.app.ui.screen.reflection.ReflectionDetailViewModel;com.timeflow.app.ui.screen.reflection.ReflectionDetailStateCcom.timeflow.app.ui.screen.reflection.data.ReflectionRepositoryImplFcom.timeflow.app.ui.screen.reflection.data.SearchSuggestionServiceImpl?com.timeflow.app.ui.screen.reflection.model.ReflectionListState9com.timeflow.app.ui.screen.reflection.model.TimeDimension7com.timeflow.app.ui.screen.reflection.model.DayViewData9com.timeflow.app.ui.screen.reflection.model.MonthViewData/com.timeflow.app.ui.screen.settings.FeatureItem+com.timeflow.app.ui.screen.settings.AppInfo.com.timeflow.app.ui.screen.settings.BackupInfo7com.timeflow.app.ui.screen.settings.AutoBackupFrequency5com.timeflow.app.ui.screen.settings.DataManagementTab/com.timeflow.app.ui.screen.settings.StorageInfo8com.timeflow.app.ui.screen.settings.NotificationSettingsAcom.timeflow.app.ui.screen.settings.NotificationSettingsViewModelQcom.timeflow.app.ui.screen.settings.NotificationSettingsViewModel.PreferencesKeys2com.timeflow.app.ui.screen.settings.MembershipType,<EMAIL>.$serializer,com.timeflow.app.ui.screen.settings.S3Config6com.timeflow.app.ui.screen.settings.S3Config.Companion8com.timeflow.app.ui.screen.settings.S3Config.$serializer4com.timeflow.app.ui.screen.settings.SerializableTask><EMAIL>.$serializer2com.timeflow.app.ui.screen.settings.TaskSerializer6com.timeflow.app.ui.screen.settings.CloudStorageClient2com.timeflow.app.ui.screen.settings.SyncRepository=com.timeflow.app.ui.screen.settings.CloudStorageClientFactory6com.timeflow.app.ui.screen.settings.AwsS3StorageClient;com.timeflow.app.ui.screen.settings.QiniuCloudStorageClientEcom.timeflow.app.ui.screen.settings.QiniuCloudStorageClient.Companion=com.timeflow.app.ui.screen.settings.TencentCloudStorageClient:com.timeflow.app.ui.screen.settings.AliyunOssStorageClient6com.timeflow.app.ui.screen.settings.SyncRepositoryImpl9com.timeflow.app.ui.screen.settings.SyncSettingsViewModelCcom.timeflow.app.ui.screen.settings.SyncSettingsViewModel.Companion0com.timeflow.app.ui.screen.settings.SyncSettings:com.timeflow.app.ui.screen.settings.CloudStorageConfigDataDcom.timeflow.app.ui.screen.settings.CloudStorageConfigData.CompanionFcom.timeflow.app.ui.screen.settings.CloudStorageConfigData.$serializer0com.timeflow.app.ui.screen.settings.S3ConfigData:com.timeflow.app.ui.screen.settings.S3ConfigData.Companion.com.timeflow.app.ui.screen.settings.SyncStatus1com.timeflow.app.ui.screen.settings.LocalResource0com.timeflow.app.ui.screen.settings.ResourceType'com.timeflow.app.ui.screen.task.SubTask1com.timeflow.app.ui.screen.task.SubTaskInputState(com.timeflow.app.util.NotificationCenter.com.timeflow.app.ui.screen.task.PostponeOption3com.timeflow.app.ui.screen.task.TaskDetailViewModel1com.timeflow.app.ui.screen.task.TaskDetailUiState9com.timeflow.app.ui.screen.task.TaskDetailUiState.Loading9com.timeflow.app.ui.screen.task.TaskDetailUiState.Success7com.timeflow.app.ui.screen.task.TaskDetailUiState.Error9com.timeflow.app.ui.screen.task.TaskDetailUiState.Deleted9com.timeflow.app.ui.screen.task.NoRippleInteractionSource4com.timeflow.app.ui.screen.task.CompletionFilterMode,com.timeflow.app.ui.screen.task.TaskSortMode,com.timeflow.app.ui.screen.task.UndoTaskInfo)com.timeflow.app.ui.screen.task.UndoTimer6com.timeflow.app.ui.screen.task.ExperimentalComposeApi:com.timeflow.app.ui.screen.task.ExperimentalTypeConversion-com.timeflow.app.ui.screen.task.ScaffoldState+com.timeflow.app.ui.screen.task.DrawerValue+com.timeflow.app.ui.screen.task.DrawerState,com.timeflow.app.ui.screen.task.GroupedTasks(com.timeflow.app.ui.screen.task.TaskData+com.timeflow.app.ui.screen.task.TaskUrgency-com.timeflow.app.ui.screen.task.UrgencyColors3com.timeflow.app.ui.screen.task.TaskTimeUpdateEvent/com.timeflow.app.ui.screen.task.TaskTimeManager,com.timeflow.app.ui.screen.task.TaskTimeSync0com.timeflow.app.ui.screen.task.TaskTimeSyncTest-com.timeflow.app.ui.screen.task.TaskViewModel7com.timeflow.app.ui.screen.task.TaskViewModel.Companion9com.timeflow.app.ui.screen.task.TaskViewModel.TaskUiStateAcom.timeflow.app.ui.screen.task.TaskViewModel.TaskUiState.LoadingAcom.timeflow.app.ui.screen.task.TaskViewModel.TaskUiState.Success?com.timeflow.app.ui.screen.task.TaskViewModel.TaskUiState.Error2com.timeflow.app.ui.screen.task.model.FeedbackData3com.timeflow.app.ui.screen.task.model.TaskDataModel/com.timeflow.app.ui.screen.task.model.TaskModel1com.timeflow.app.ui.screen.task.model.TaskUrgency3com.timeflow.app.ui.screen.task.model.UrgencyColors2com.timeflow.app.ui.screen.wishlist.WishStatistics3com.timeflow.app.ui.screen.wishlist.WishHeatMapData/com.timeflow.app.ui.screen.wishlist.WishInsight3com.timeflow.app.ui.screen.wishlist.WishAchievement.com.timeflow.app.ui.screen.wishlist.HeatMapDay0com.timeflow.app.ui.screen.wishlist.WishInsights/com.timeflow.app.ui.screen.wishlist.Achievement/com.timeflow.app.ui.screen.wishlist.WishFilters&com.timeflow.app.ui.settings.ColorType1com.timeflow.app.ui.settings.PresetThemeViewModel&com.timeflow.app.ui.settings.ThemeMode&com.timeflow.app.ui.settings.AppColors3com.timeflow.app.ui.settings.ThemeSettingsViewModel;com.timeflow.app.ui.settings.ThemeSettingsViewModel.UiState'<EMAIL>(com.timeflow.app.ui.task.KanbanViewModel*com.timeflow.app.ui.task.SharedFilterState*com.timeflow.app.ui.task.TaskListOptimizer<com.timeflow.app.ui.task.TaskListOptimizer.OptimizationLevel=com.timeflow.app.ui.task.TaskListOptimizer.DeviceCapabilities&com.timeflow.app.ui.task.TaskListState&com.timeflow.app.ui.task.DeleteMessage*com.timeflow.app.ui.task.TaskListViewModel4com.timeflow.app.ui.task.TaskListViewModel.Companion6com.timeflow.app.ui.task.TaskListViewModel.TaskUiState;com.timeflow.app.ui.task.TaskListViewModel.DataLoadingState0com.timeflow.app.ui.task.components.AiSuggestion1com.timeflow.app.ui.task.components.TaskCardStyle,com.timeflow.app.ui.task.components.TaskItem2com.timeflow.app.ui.task.components.ParsedTaskInfo:com.timeflow.app.ui.task.components.common.PriorityVariant8com.timeflow.app.ui.task.components.common.TaskCardTheme9com.timeflow.app.ui.task.components.common.TaskCardThemes><EMAIL>>com.timeflow.app.ui.task.components.common.cache.ExpiringCacheIcom.timeflow.app.ui.task.components.common.cache.ExpiringCache.CacheEntry9com.timeflow.app.ui.task.components.common.event.AppEventCcom.timeflow.app.ui.task.components.common.event.AppEvent.BaseEventEcom.timeflow.app.ui.task.components.common.event.AppEvent.TaskUpdatedEcom.timeflow.app.ui.task.components.common.event.AppEvent.TaskDeletedIcom.timeflow.app.ui.task.components.common.event.AppEvent.SubtasksChangedKcom.timeflow.app.ui.task.components.common.event.AppEvent.DatabaseRefreshedOcom.timeflow.app.ui.task.components.common.event.AppEvent.TaskCompletionChangedKcom.timeflow.app.ui.task.components.common.event.AppEvent.TaskGroupsChangedKcom.timeflow.app.ui.task.components.common.event.AppEvent.KanbanDataChangedEcom.timeflow.app.ui.task.components.common.event.AppEvent.TaskCreatedCcom.timeflow.app.ui.task.components.common.event.AppEvent.TaskMovedNcom.timeflow.app.ui.task.components.common.event.AppEvent.ThemeSettingsChangedOcom.timeflow.app.ui.task.components.common.event.AppEvent.PageBackgroundChangedPcom.timeflow.app.ui.task.components.common.event.AppEvent.ThemeSettingsRequestedIcom.timeflow.app.ui.task.components.common.event.AppEvent.TaskTimeChangedIcom.timeflow.app.ui.task.components.common.event.AppEvent.TaskTimeUpdatedFcom.timeflow.app.ui.task.components.common.event.AppEvent.SubTaskAddedHcom.timeflow.app.ui.task.components.common.event.AppEvent.SubTaskUpdatedHcom.timeflow.app.ui.task.components.common.event.AppEvent.SubTaskDeletedJcom.timeflow.app.ui.task.components.common.event.AppEvent.TaskColorChangedFcom.timeflow.app.ui.task.components.common.event.AppEvent.ThemeChangedNcom.timeflow.app.ui.task.components.common.event.AppEvent.NotificationReceivedMcom.timeflow.app.ui.task.components.common.event.AppEvent.CalendarDateChangedLcom.timeflow.app.ui.task.components.common.event.AppEvent.CalendarEventAddedNcom.timeflow.app.ui.task.components.common.event.AppEvent.CalendarEventUpdatedNcom.timeflow.app.ui.task.components.common.event.AppEvent.CalendarEventDeletedIcom.timeflow.app.ui.task.components.common.event.AppEvent.SettingsChangedFcom.timeflow.app.ui.task.components.common.event.AppEvent.UserLoggedInGcom.timeflow.app.ui.task.components.common.event.AppEvent.UserLoggedOutEcom.timeflow.app.ui.task.components.common.event.AppEvent.SyncStartedGcom.timeflow.app.ui.task.components.common.event.AppEvent.SyncCompletedGcom.timeflow.app.ui.task.components.common.event.AppEvent.ErrorOccurred9com.timeflow.app.ui.task.components.common.event.EventBus:<EMAIL>=com.timeflow.app.ui.task.components.common.state.ManagedState9com.timeflow.app.ui.task.components.common.util.RetryUtil)com.timeflow.app.ui.task.model.SortOption3com.timeflow.app.ui.task.model.SortOption.Companion)com.timeflow.app.ui.task.model.TaskStatus3com.timeflow.app.ui.task.model.TaskStatus.Companion/com.timeflow.app.ui.task.model.TaskStatusChange.com.timeflow.app.ui.task.model.TasksStatistics8com.timeflow.app.ui.task.model.TasksStatistics.Companion'com.timeflow.app.ui.task.model.ViewMode1com.timeflow.app.ui.task.model.ViewMode.Companion(com.timeflow.app.ui.theme.AnalyticsTheme4com.timeflow.app.ui.theme.AnalyticsTheme.MonetColors6com.timeflow.app.ui.theme.AnalyticsTheme.MorandiColors9com.timeflow.app.ui.theme.AnalyticsTheme.FunctionalColors3com.timeflow.app.ui.theme.AnalyticsTheme.TextColors4com.timeflow.app.ui.theme.AnalyticsTheme.ChartColors3com.timeflow.app.ui.theme.AnalyticsTheme.Typography3com.timeflow.app.ui.theme.AnalyticsTheme.Dimensions,com.timeflow.app.ui.theme.AnalyticsThemeData.com.timeflow.app.ui.theme.AnalyticsColorScheme*com.timeflow.app.ui.theme.MonetColorScheme,com.timeflow.app.ui.theme.MorandiColorScheme/com.timeflow.app.ui.theme.FunctionalColorScheme)com.timeflow.app.ui.theme.TextColorScheme*com.timeflow.app.ui.theme.ChartColorScheme-com.timeflow.app.ui.theme.AnalyticsTypography-com.timeflow.app.ui.theme.AnalyticsDimensions,com.timeflow.app.ui.theme.DynamicThemeHelper)com.timeflow.app.ui.theme.CustomThemeData(com.timeflow.app.ui.theme.PriorityColors*com.timeflow.app.ui.theme.CompletionColors&com.timeflow.app.ui.theme.ThemeManager,com.timeflow.app.ui.theme.PresetThemeManager#com.timeflow.app.ui.theme.ThemeMode-com.timeflow.app.ui.theme.UserThemePreference%com.timeflow.app.ui.theme.PresetTheme*com.timeflow.app.ui.theme.PresetThemeState(com.timeflow.app.ui.theme.BuiltInPresets%com.timeflow.app.ui.theme.FontManager-com.timeflow.app.ui.timetracking.CategoryData3com.timeflow.app.ui.timetracking.EfficiencyTimeSlot<com.timeflow.app.ui.timetracking.TaskTimeStatisticsViewModel:<EMAIL>+com.timeflow.app.ui.timetracking.TimerState%com.timeflow.app.ui.timetracking.Task*com.timeflow.app.ui.timetracking.TimerType8com.timeflow.app.ui.timetracking.components.CalendarInfo6com.timeflow.app.ui.timetracking.components.TimerState)com.timeflow.app.ui.utils.DateFormatUtils0com.timeflow.app.ui.viewmodel.AiAssistantUiState2com.timeflow.app.ui.viewmodel.AiAssistantViewModelBcom.timeflow.app.ui.viewmodel.AiAssistantViewModel.DiagnosisResult/com.timeflow.app.ui.viewmodel.AiConfigViewModel1com.timeflow.app.ui.viewmodel.AiSettingsViewModel2com.timeflow.app.ui.viewmodel.GlobalTimerViewModel<com.timeflow.app.ui.viewmodel.GlobalTimerViewModel.Companion3com.timeflow.app.ui.viewmodel.GoalCreationViewModelGcom.timeflow.app.ui.viewmodel.GoalCreationViewModel.GoalCreationUiStateNcom.timeflow.app.ui.viewmodel.GoalCreationViewModel.GoalCreationUiState.Normal^com.timeflow.app.ui.viewmodel.GoalCreationViewModel.GoalCreationUiState.TemplateRecommendationPcom.timeflow.app.ui.viewmodel.GoalCreationViewModel.GoalCreationUiState.CreatingOcom.timeflow.app.ui.viewmodel.GoalCreationViewModel.GoalCreationUiState.SuccessMcom.timeflow.app.ui.viewmodel.GoalCreationViewModel.GoalCreationUiState.Error+com.timeflow.app.ui.viewmodel.GoalViewModel)com.timeflow.app.ui.viewmodel.GoalUiState.com.timeflow.app.ui.viewmodel.GoalUiState.Idle1com.timeflow.app.ui.viewmodel.GoalUiState.Loading1com.timeflow.app.ui.viewmodel.GoalUiState.Success/com.timeflow.app.ui.viewmodel.GoalUiState.Error+com.timeflow.app.ui.viewmodel.HabitTemplate,com.timeflow.app.ui.viewmodel.HabitViewModel;com.timeflow.app.ui.viewmodel.HabitViewModel.HabitFormState3com.timeflow.app.ui.viewmodel.MenstrualCycleUiState&com.timeflow.app.ui.viewmodel.CycleDay'com.timeflow.app.ui.viewmodel.CycleType$com.timeflow.app.ui.viewmodel.Tuple75com.timeflow.app.ui.viewmodel.MenstrualCycleViewModel?com.timeflow.app.ui.viewmodel.MenstrualCycleViewModel.Companion.com.timeflow.app.ui.viewmodel.PeriodStatistics.com.timeflow.app.ui.viewmodel.ValidationResult4com.timeflow.app.ui.viewmodel.ValidationResult.Valid6com.timeflow.app.ui.viewmodel.ValidationResult.Warning6com.timeflow.app.ui.viewmodel.ValidationResult.Invalid1com.timeflow.app.ui.viewmodel.ConflictCheckResult<<EMAIL>>com.timeflow.app.ui.viewmodel.OperationResult.ConflictResolved/com.timeflow.app.ui.viewmodel.TaskTimeViewModel9com.timeflow.app.ui.viewmodel.TaskTimeViewModel.Companion-com.timeflow.app.ui.viewmodel.TaskTimeUiState/com.timeflow.app.ui.viewmodel.TimeFlowViewModel-com.timeflow.app.ui.viewmodel.TimeFlowUiState/com.timeflow.app.ui.viewmodel.WishListViewModel-com.timeflow.app.ui.viewmodel.WishListUiState5com.timeflow.app.ui.viewmodel.WishStatisticsViewModel3com.timeflow.app.ui.viewmodel.WishStatisticsUiState.com.timeflow.app.ui.viewmodel.WishTimelineItem*com.timeflow.app.util.DataConsistencyFixer4com.timeflow.app.util.DataConsistencyFixer.Companion<com.timeflow.app.util.DataConsistencyFixer.ConsistencyReport;com.timeflow.app.util.DataConsistencyFixer.InconsistentTask4com.timeflow.app.util.DataConsistencyFixer.ErrorTask+com.timeflow.app.util.DataConsistencyHelper#com.timeflow.app.util.EventListener&com.timeflow.app.util.HabitGoalManager/com.timeflow.app.util.HabitGoalAssociationStats*com.timeflow.app.util.HabitGoalAssociation'com.timeflow.app.util.MarkdownFormatter&com.timeflow.app.util.TaskRefreshEvent'com.timeflow.app.util.NewTaskAddedEvent1com.timeflow.app.util.AIAssistantTaskCreatedEvent'com.timeflow.app.util.HabitDeletedEvent(com.timeflow.app.util.HabitArchivedEvent-com.timeflow.app.util.HabitStatusChangedEvent%com.timeflow.app.util.HabitChangeTypecom.timeflow.app.util.TaskUtils.com.timeflow.app.utils.ActivityContextProvider*com.timeflow.app.utils.AppExceptionHandler.com.timeflow.app.utils.BinderTransactionHelper!com.timeflow.app.utils.ColorUtils-com.timeflow.app.utils.ComposeScreenshotUtils!com.timeflow.app.utils.CycleUtils,com.timeflow.app.utils.DatabaseBackupManager6com.timeflow.app.utils.DatabaseBackupManager.Companion7com.timeflow.app.utils.DatabaseBackupManager.BackupInfo;com.timeflow.app.utils.DatabaseBackupManager.BackupFileInfo;com.timeflow.app.utils.DatabaseBackupManager.BackupSettings$com.timeflow.app.utils.DateTimeUtilscom.timeflow.app.utils.EventBus)com.timeflow.app.utils.EventBus.Companion(com.timeflow.app.utils.HwcLutsErrorFixer3com.timeflow.app.utils.HwcLutsErrorFixer.ErrorState7com.timeflow.app.utils.HwcLutsErrorFixer.SurfaceMonitor"com.timeflow.app.utils.ImageLoader,com.timeflow.app.utils.ImageLoader.Companion!com.timeflow.app.utils.ImageUtils com.timeflow.app.utils.LogConfig,com.timeflow.app.utils.LogConfig.ReleaseTree*com.timeflow.app.utils.NavigationOptimizer)com.timeflow.app.utils.NotificationHelper3com.timeflow.app.utils.NotificationHelper.Companion3com.timeflow.app.utils.NotificationPermissionHelper=com.timeflow.app.utils.NotificationPermissionHelper.Companion2com.timeflow.app.utils.NotificationPermissionState)com.timeflow.app.utils.PerformanceMonitor!com.timeflow.app.utils.RenderMode+com.timeflow.app.utils.PerformanceOptimizer%com.timeflow.app.utils.PreferenceKeys-com.timeflow.app.utils.ReflectionShareManager&com.timeflow.app.utils.RenderOptimizer"com.timeflow.app.utils.RenderUtils.com.timeflow.app.utils.RenderOptimizationLevel+com.timeflow.app.utils.RestrictedReflection&com.timeflow.app.utils.RippleOptimizer&com.timeflow.app.utils.SafeImageLoader0com.timeflow.app.utils.SafeImageLoader.Companion%com.timeflow.app.utils.SafeNavigation'com.timeflow.app.utils.SafeParcelHelpercom.timeflow.app.utils.Priority"com.timeflow.app.utils.SafetyGuard*com.timeflow.app.utils.SampleDataGenerator&com.timeflow.app.utils.SettingsManager0com.timeflow.app.utils.SettingsManager.Companion%com.timeflow.app.utils.StatusBarUtils'com.timeflow.app.utils.SystemBarManager;com.timeflow.app.utils.TaskPersistentNotificationTestHelperEcom.timeflow.app.utils.TaskPersistentNotificationTestHelper.Companion(com.timeflow.app.utils.TaskReminderUtils2com.timeflow.app.utils.TaskReminderUtils.Companion$com.timeflow.app.utils.TemplateCache*com.timeflow.app.utils.PagedTemplateResult2com.timeflow.app.utils.TimeFlowNotificationManager<com.timeflow.app.utils.TimeFlowNotificationManager.Companion!com.timeflow.app.utils.DailyStats"com.timeflow.app.utils.WeeklyStats1com.timeflow.app.utils.NotificationActionReceiver$com.timeflow.app.utils.TimeZoneUtils1com.timeflow.app.viewmodel.BackupRestoreViewModel/com.timeflow.app.viewmodel.BackupRestoreUiState2com.timeflow.app.viewmodel.BackupSettingsViewModel)com.timeflow.app.viewmodel.BackupSettings2com.timeflow.app.viewmodel.DataManagementViewModel<com.timeflow.app.viewmodel.DataManagementViewModel.Companion=com.timeflow.app.viewmodel.DataManagementViewModel.ExportDataGcom.timeflow.app.viewmodel.DataManagementViewModel.ExportData.CompanionIcom.timeflow.app.viewmodel.DataManagementViewModel.ExportData.$serializer0com.timeflow.app.viewmodel.DataManagementUiState(com.timeflow.app.widget.FocusTimerWidget2com.timeflow.app.widget.FocusTimerWidget.Companion*com.timeflow.app.widget.GoalProgressWidget(com.timeflow.app.widget.QuickTimerWidget2com.timeflow.app.widget.QuickTimerWidget.Companion(com.timeflow.app.widget.TimerWidgetState)com.timeflow.app.widget.TimeInsightWidget*com.timeflow.app.widget.TimerWidgetUpdater.com.timeflow.app.widget.TodayTasksDataProvider8com.timeflow.app.widget.TodayTasksDataProvider.Companion&com.timeflow.app.widget.TodayTasksData"com.timeflow.app.widget.SimpleTask.com.timeflow.app.widget.SimpleTaskWithPriority#com.timeflow.app.widget.DisplayTask(com.timeflow.app.widget.TodayTasksWidget2com.timeflow.app.widget.TodayTasksWidget.Companion)com.timeflow.app.widget.WeeklyStatsWidget+com.timeflow.app.widget.WidgetUpdateManager*com.timeflow.app.worker.AiSuggestionWorker4com.timeflow.app.worker.AiSuggestionWorker.Companion:com.timeflow.app.worker.AiSuggestionWorker.PreferencesKeys$com.timeflow.app.worker.AiSuggestion(com.timeflow.app.worker.AutoBackupWorker2com.timeflow.app.worker.AutoBackupWorker.Companion)com.timeflow.app.worker.DailyReviewWorker3com.timeflow.app.worker.DailyReviewWorker.Companion+com.timeflow.app.worker.HabitReminderWorker5com.timeflow.app.worker.HabitReminderWorker.Companion.com.timeflow.app.worker.OverdueTaskCheckWorker8com.timeflow.app.worker.OverdueTaskCheckWorker.Companion8com.timeflow.app.worker.OverdueTaskCheckWorkerEntryPoint+com.timeflow.app.worker.RecurringTaskWorker5com.timeflow.app.worker.RecurringTaskWorker.Companion*com.timeflow.app.worker.TaskReminderWorker4com.timeflow.app.worker.TaskReminderWorker.Companioncom.timeflow.app.BuildConfig                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              