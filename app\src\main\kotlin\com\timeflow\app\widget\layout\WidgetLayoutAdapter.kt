package com.timeflow.app.widget.layout

import android.content.Context
import android.widget.RemoteViews
import com.timeflow.app.R
import com.timeflow.app.widget.layout.WidgetSizeManager.*

/**
 * 小组件布局适配器
 * 根据不同尺寸自动调整布局和样式
 */
class WidgetLayoutAdapter(private val context: Context) {

    private val sizeManager = WidgetSizeManager.getInstance(context)

    /**
     * 适配小组件布局
     */
    fun adaptLayout(
        remoteViews: RemoteViews,
        size: WidgetSize,
        layoutType: LayoutType
    ): RemoteViews {
        val config = sizeManager.getLayoutConfig(size)
        
        // 应用基础样式
        applyBaseStyles(remoteViews, config)
        
        // 根据布局类型应用特定样式
        when (layoutType) {
            LayoutType.TODAY_TASKS -> adaptTodayTasksLayout(remoteViews, config)
            LayoutType.FOCUS_TIMER -> adaptFocusTimerLayout(remoteViews, config)
            LayoutType.QUICK_TIMER -> adaptQuickTimerLayout(remoteViews, config)
            LayoutType.TIME_INSIGHT -> adaptTimeInsightLayout(remoteViews, config)
            LayoutType.WEEKLY_STATS -> adaptWeeklyStatsLayout(remoteViews, config)
            LayoutType.GOAL_PROGRESS -> adaptGoalProgressLayout(remoteViews, config)
        }
        
        return remoteViews
    }

    /**
     * 布局类型
     */
    enum class LayoutType {
        TODAY_TASKS,
        FOCUS_TIMER,
        QUICK_TIMER,
        TIME_INSIGHT,
        WEEKLY_STATS,
        GOAL_PROGRESS
    }

    /**
     * 应用基础样式
     */
    private fun applyBaseStyles(remoteViews: RemoteViews, config: LayoutConfig) {
        // 设置容器内边距
        try {
            val paddingPx = sizeManager.dpToPx(config.paddingDp)
            remoteViews.setViewPadding(R.id.widget_container, paddingPx, paddingPx, paddingPx, paddingPx)
        } catch (e: Exception) {
            // 忽略不存在的视图
        }

        // 设置文本大小缩放
        applyTextScaling(remoteViews, config.textScale)
    }

    /**
     * 应用文本缩放
     */
    private fun applyTextScaling(remoteViews: RemoteViews, scale: Float) {
        val textViews = listOf(
            R.id.widget_date,
            R.id.widget_weekday,
            R.id.widget_task_1,
            R.id.widget_task_2,
            R.id.widget_task_3,
            R.id.widget_task_4,
            R.id.widget_task_5,
            R.id.widget_progress_text,
            R.id.widget_completed_count,
            R.id.widget_total_count
        )

        textViews.forEach { viewId ->
            try {
                // 获取基础文本大小并应用缩放
                val baseSize = getBaseTextSizeForView(viewId)
                val scaledSize = baseSize * scale
                remoteViews.setTextViewTextSize(viewId, android.util.TypedValue.COMPLEX_UNIT_SP, scaledSize)
            } catch (e: Exception) {
                // 忽略不存在的视图
            }
        }
    }

    /**
     * 获取视图的基础文本大小
     */
    private fun getBaseTextSizeForView(viewId: Int): Float {
        return when (viewId) {
            R.id.widget_date -> 48f
            R.id.widget_weekday -> 16f
            R.id.widget_task_1, R.id.widget_task_2, R.id.widget_task_3,
            R.id.widget_task_4, R.id.widget_task_5 -> 16f
            R.id.widget_progress_text -> 12f
            R.id.widget_completed_count -> 14f
            R.id.widget_total_count -> 12f
            else -> 14f
        }
    }

    /**
     * 适配今日待办小组件布局
     */
    private fun adaptTodayTasksLayout(remoteViews: RemoteViews, config: LayoutConfig) {
        // 根据尺寸调整显示的任务数量
        val maxTasks = minOf(config.maxItems, 5)
        
        // 隐藏超出数量的任务项
        val taskContainers = listOf(
            R.id.widget_task_container_1,
            R.id.widget_task_container_2,
            R.id.widget_task_container_3,
            R.id.widget_task_container_4,
            R.id.widget_task_container_5
        )

        taskContainers.forEachIndexed { index, containerId ->
            try {
                val visibility = if (index < maxTasks) {
                    android.view.View.VISIBLE
                } else {
                    android.view.View.GONE
                }
                remoteViews.setViewVisibility(containerId, visibility)
            } catch (e: Exception) {
                // 忽略不存在的视图
            }
        }

        // 调整心情emoji大小
        if (config.size == WidgetSize.SMALL) {
            try {
                remoteViews.setTextViewTextSize(
                    R.id.widget_mood_emoji,
                    android.util.TypedValue.COMPLEX_UNIT_SP,
                    24f
                )
            } catch (e: Exception) {
                // 忽略
            }
        }

        // 根据尺寸调整日期显示
        when (config.size) {
            WidgetSize.SMALL -> {
                // 小尺寸只显示日期数字
                try {
                    remoteViews.setViewVisibility(R.id.widget_weekday, android.view.View.GONE)
                    remoteViews.setTextViewTextSize(
                        R.id.widget_date,
                        android.util.TypedValue.COMPLEX_UNIT_SP,
                        32f
                    )
                } catch (e: Exception) {
                    // 忽略
                }
            }
            WidgetSize.MEDIUM_HORIZONTAL -> {
                // 横向布局调整
                try {
                    remoteViews.setTextViewTextSize(
                        R.id.widget_date,
                        android.util.TypedValue.COMPLEX_UNIT_SP,
                        36f
                    )
                } catch (e: Exception) {
                    // 忽略
                }
            }
            else -> {
                // 保持默认大小
            }
        }
    }

    /**
     * 适配专注计时器小组件布局
     */
    private fun adaptFocusTimerLayout(remoteViews: RemoteViews, config: LayoutConfig) {
        // 根据尺寸调整计时器显示大小
        val timerTextSize = when (config.size) {
            WidgetSize.SMALL -> 24f
            WidgetSize.MEDIUM_HORIZONTAL -> 32f
            WidgetSize.MEDIUM -> 40f
            WidgetSize.LARGE_HORIZONTAL -> 48f
            WidgetSize.LARGE -> 56f
            WidgetSize.EXTRA_LARGE -> 64f
        }

        try {
            remoteViews.setTextViewTextSize(
                R.id.widget_timer_display,
                android.util.TypedValue.COMPLEX_UNIT_SP,
                timerTextSize
            )
        } catch (e: Exception) {
            // 忽略
        }

        // 小尺寸隐藏详细信息
        if (config.size == WidgetSize.SMALL) {
            val hideViews = listOf(
                R.id.widget_status_text,
                R.id.widget_timer_type,
                R.id.widget_pomodoro_count,
                R.id.widget_progress_bar
            )

            hideViews.forEach { viewId ->
                try {
                    remoteViews.setViewVisibility(viewId, android.view.View.GONE)
                } catch (e: Exception) {
                    // 忽略
                }
            }
        }

        // 调整按钮大小
        val buttonPadding = sizeManager.getSpacing(config.size, SpacingType.SMALL)
        try {
            remoteViews.setViewPadding(
                R.id.widget_play_pause_button,
                buttonPadding, buttonPadding / 2, buttonPadding, buttonPadding / 2
            )
        } catch (e: Exception) {
            // 忽略
        }
    }

    /**
     * 适配快速计时小组件布局
     */
    private fun adaptQuickTimerLayout(remoteViews: RemoteViews, config: LayoutConfig) {
        // 根据尺寸调整按钮数量和大小
        val maxButtons = when (config.size) {
            WidgetSize.SMALL -> 1
            WidgetSize.MEDIUM_HORIZONTAL -> 3
            WidgetSize.MEDIUM -> 3
            WidgetSize.LARGE_HORIZONTAL -> 4
            WidgetSize.LARGE -> 6
            WidgetSize.EXTRA_LARGE -> 8
        }

        // 调整按钮可见性
        val timerButtons = listOf(
            R.id.widget_timer_25,
            R.id.widget_timer_45,
            R.id.widget_timer_60,
            R.id.widget_timer_custom
        )

        timerButtons.forEachIndexed { index, buttonId ->
            try {
                val visibility = if (index < maxButtons) {
                    android.view.View.VISIBLE
                } else {
                    android.view.View.GONE
                }
                remoteViews.setViewVisibility(buttonId, visibility)
            } catch (e: Exception) {
                // 忽略
            }
        }

        // 小尺寸只显示一个主要按钮
        if (config.size == WidgetSize.SMALL) {
            try {
                remoteViews.setViewVisibility(R.id.widget_timer_25, android.view.View.VISIBLE)
                remoteViews.setViewVisibility(R.id.widget_timer_45, android.view.View.GONE)
                remoteViews.setViewVisibility(R.id.widget_timer_60, android.view.View.GONE)
                remoteViews.setViewVisibility(R.id.widget_timer_custom, android.view.View.GONE)
            } catch (e: Exception) {
                // 忽略
            }
        }
    }

    /**
     * 适配时间洞察小组件布局
     */
    private fun adaptTimeInsightLayout(remoteViews: RemoteViews, config: LayoutConfig) {
        // 时间洞察小组件的适配逻辑
        // 暂时为空，等待具体实现
    }

    /**
     * 适配周统计小组件布局
     */
    private fun adaptWeeklyStatsLayout(remoteViews: RemoteViews, config: LayoutConfig) {
        // 周统计小组件的适配逻辑
        // 暂时为空，等待具体实现
    }

    /**
     * 适配目标进度小组件布局
     */
    private fun adaptGoalProgressLayout(remoteViews: RemoteViews, config: LayoutConfig) {
        // 目标进度小组件的适配逻辑
        // 暂时为空，等待具体实现
    }

    /**
     * 获取推荐的布局资源ID
     */
    fun getRecommendedLayoutId(size: WidgetSize, layoutType: LayoutType): Int {
        return when (layoutType) {
            LayoutType.TODAY_TASKS -> when (size) {
                WidgetSize.SMALL -> R.layout.widget_today_tasks_small
                WidgetSize.MEDIUM_HORIZONTAL -> R.layout.widget_today_tasks_medium
                WidgetSize.MEDIUM -> R.layout.widget_today_tasks_medium
                WidgetSize.LARGE_HORIZONTAL -> R.layout.widget_today_tasks_large
                WidgetSize.LARGE -> R.layout.widget_today_tasks_large
                WidgetSize.EXTRA_LARGE -> R.layout.widget_today_tasks_large
            }
            LayoutType.FOCUS_TIMER -> when (size) {
                WidgetSize.SMALL -> R.layout.widget_focus_timer_small
                WidgetSize.MEDIUM_HORIZONTAL -> R.layout.widget_focus_timer
                WidgetSize.MEDIUM -> R.layout.widget_focus_timer
                WidgetSize.LARGE_HORIZONTAL -> R.layout.widget_focus_timer_large
                WidgetSize.LARGE -> R.layout.widget_focus_timer_large
                WidgetSize.EXTRA_LARGE -> R.layout.widget_focus_timer_large
            }
            LayoutType.QUICK_TIMER -> when (size) {
                WidgetSize.SMALL -> R.layout.widget_quick_timer_small
                else -> R.layout.widget_quick_timer
            }
            LayoutType.TIME_INSIGHT -> R.layout.widget_time_insight
            LayoutType.WEEKLY_STATS -> R.layout.widget_weekly_stats
            LayoutType.GOAL_PROGRESS -> R.layout.widget_goal_progress
        }
    }

    /**
     * 应用响应式间距
     */
    fun applyResponsiveSpacing(remoteViews: RemoteViews, size: WidgetSize) {
        val smallSpacing = sizeManager.getSpacing(size, SpacingType.SMALL)
        val mediumSpacing = sizeManager.getSpacing(size, SpacingType.MEDIUM)
        val largeSpacing = sizeManager.getSpacing(size, SpacingType.LARGE)

        // 应用到各种间距
        val spacingMappings = mapOf(
            R.id.widget_header to largeSpacing,
            R.id.widget_task_container to mediumSpacing,
            R.id.widget_footer to smallSpacing
        )

        spacingMappings.forEach { (viewId, spacing) ->
            try {
                remoteViews.setViewPadding(viewId, spacing, spacing, spacing, spacing)
            } catch (e: Exception) {
                // 忽略不存在的视图
            }
        }
    }
}