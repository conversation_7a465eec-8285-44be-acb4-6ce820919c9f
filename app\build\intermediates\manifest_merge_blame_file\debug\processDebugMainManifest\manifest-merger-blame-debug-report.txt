1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.timeflow.app"
4    android:versionCode="8"
5    android:versionName="0.5.3" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <!-- Calendar permissions -->
12    <uses-permission android:name="android.permission.READ_CALENDAR" />
12-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:6:5-72
12-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:6:22-69
13    <uses-permission android:name="android.permission.WRITE_CALENDAR" />
13-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:7:5-73
13-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:7:22-70
14
15    <!-- Network permissions -->
16    <uses-permission android:name="android.permission.INTERNET" />
16-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:10:5-67
16-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:10:22-64
17    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
17-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:11:5-79
17-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:11:22-76
18
19    <!-- Notification permissions -->
20    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
20-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:14:5-77
20-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:14:22-74
21    <uses-permission android:name="android.permission.VIBRATE" />
21-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:15:5-66
21-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:15:22-63
22
23    <!-- Background work permissions -->
24    <uses-permission android:name="android.permission.WAKE_LOCK" />
24-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:18:5-68
24-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:18:22-65
25    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
25-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:19:5-81
25-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:19:22-78
26
27    <!-- 🔧 新增：前台服务权限 -->
28    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
28-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:22:5-77
28-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:22:22-74
29    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
29-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:23:5-89
29-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:23:22-86
30
31    <!-- Alarm permissions for medication reminders -->
32    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
32-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:26:5-79
32-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:26:22-76
33    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
33-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:27:5-74
33-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:27:22-71
34
35    <!-- Storage permissions -->
36    <uses-permission
36-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:30:5-31:51
37        android:name="android.permission.READ_EXTERNAL_STORAGE"
37-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:30:22-77
38        android:maxSdkVersion="32" />
38-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:31:22-48
39    <uses-permission
39-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:32:5-33:51
40        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
40-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:32:22-78
41        android:maxSdkVersion="29" />
41-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:33:22-48
42    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
42-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:34:5-76
42-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:34:22-73
43
44    <permission
44-->[androidx.core:core:1.12.0] D:\development\Android\gradle\caches\8.11.1\transforms\443a6cfc0de72abe8b7a8ef4c7b7a3e3\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
45        android:name="com.timeflow.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
45-->[androidx.core:core:1.12.0] D:\development\Android\gradle\caches\8.11.1\transforms\443a6cfc0de72abe8b7a8ef4c7b7a3e3\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
46        android:protectionLevel="signature" />
46-->[androidx.core:core:1.12.0] D:\development\Android\gradle\caches\8.11.1\transforms\443a6cfc0de72abe8b7a8ef4c7b7a3e3\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
47
48    <uses-permission android:name="com.timeflow.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
48-->[androidx.core:core:1.12.0] D:\development\Android\gradle\caches\8.11.1\transforms\443a6cfc0de72abe8b7a8ef4c7b7a3e3\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
48-->[androidx.core:core:1.12.0] D:\development\Android\gradle\caches\8.11.1\transforms\443a6cfc0de72abe8b7a8ef4c7b7a3e3\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
49
50    <application
50-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:36:5-290:19
51        android:name="com.timeflow.app.TimeFlowApplication"
51-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:37:9-44
52        android:allowBackup="true"
52-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:38:9-35
53        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
53-->[androidx.core:core:1.12.0] D:\development\Android\gradle\caches\8.11.1\transforms\443a6cfc0de72abe8b7a8ef4c7b7a3e3\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
54        android:dataExtractionRules="@xml/data_extraction_rules"
54-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:39:9-65
55        android:debuggable="true"
56        android:enableOnBackInvokedCallback="true"
56-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:46:9-51
57        android:extractNativeLibs="false"
58        android:fullBackupContent="@xml/backup_rules"
58-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:40:9-54
59        android:hardwareAccelerated="@bool/enable_hardware_acceleration"
59-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:47:9-73
60        android:icon="@mipmap/ic_launcher"
60-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:41:9-43
61        android:label="@string/app_name"
61-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:42:9-41
62        android:roundIcon="@mipmap/ic_launcher_round"
62-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:43:9-54
63        android:supportsRtl="true"
63-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:44:9-35
64        android:theme="@style/Theme.TimeFlow.OptimizedAnimation" >
64-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:45:9-65
65
66        <!-- 主活动 - 默认启用硬件加速，但会在运行时根据设备能力进行调整 -->
67        <activity
67-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:51:9-69:20
68            android:name="com.timeflow.app.ui.MainActivity"
68-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:52:13-44
69            android:exported="true"
69-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:53:13-36
70            android:hardwareAccelerated="true"
70-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:54:13-47
71            android:launchMode="singleTop"
71-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:55:13-43
72            android:theme="@style/Theme.TimeFlow.OptimizedAnimation"
72-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:56:13-69
73            android:windowSoftInputMode="adjustResize" >
73-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:57:13-55
74            <intent-filter>
74-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:58:13-61:29
75                <action android:name="android.intent.action.MAIN" />
75-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:59:17-69
75-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:59:25-66
76
77                <category android:name="android.intent.category.LAUNCHER" />
77-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:60:17-77
77-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:60:27-74
78            </intent-filter>
79            <!-- 添加深度链接支持任务页面直接访问 -->
80            <intent-filter>
80-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:63:13-68:29
81                <action android:name="android.intent.action.VIEW" />
81-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:64:17-69
81-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:64:25-66
82
83                <category android:name="android.intent.category.DEFAULT" />
83-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:65:17-76
83-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:65:27-73
84                <category android:name="android.intent.category.BROWSABLE" />
84-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:66:17-78
84-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:66:27-75
85
86                <data
86-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:67:17-72
87                    android:host="tasks"
87-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:67:49-69
88                    android:scheme="timeflow" />
88-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:67:23-48
89            </intent-filter>
90        </activity>
91
92        <!-- 习惯提醒广播接收器 -->
93        <receiver
93-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:72:9-74:40
94            android:name="com.timeflow.app.receiver.HabitAlarmReceiver"
94-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:73:13-56
95            android:exported="false" />
95-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:74:13-37
96
97        <!-- 任务提醒广播接收器 -->
98        <receiver
98-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:77:9-79:40
99            android:name="com.timeflow.app.receiver.TaskAlarmReceiver"
99-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:78:13-55
100            android:exported="false" />
100-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:79:13-37
101
102        <!-- 每日回顾闹钟接收器 -->
103        <receiver
103-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:82:9-84:40
104            android:name="com.timeflow.app.receiver.DailyReviewAlarmReceiver"
104-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:83:13-62
105            android:exported="false" />
105-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:84:13-37
106
107        <!-- 用药提醒广播接收器 -->
108        <receiver
108-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:87:9-89:40
109            android:name="com.timeflow.app.service.MedicationReminderReceiver"
109-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:88:13-63
110            android:exported="false" />
110-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:89:13-37
111
112        <!-- 用药动作广播接收器 -->
113        <receiver
113-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:92:9-99:20
114            android:name="com.timeflow.app.service.MedicationActionReceiver"
114-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:93:13-61
115            android:exported="false" >
115-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:94:13-37
116            <intent-filter>
116-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:95:13-98:29
117                <action android:name="MEDICATION_TAKEN" />
117-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:96:17-59
117-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:96:25-56
118                <action android:name="MEDICATION_SNOOZE" />
118-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:97:17-60
118-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:97:25-57
119            </intent-filter>
120        </receiver>
121
122        <!-- 在应用启动时接收启动完成广播，重新设置闹钟 -->
123        <receiver
123-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:102:9-108:20
124            android:name="com.timeflow.app.receiver.BootCompletedReceiver"
124-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:103:13-59
125            android:exported="false" >
125-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:104:13-37
126            <intent-filter>
126-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:105:13-107:29
127                <action android:name="android.intent.action.BOOT_COMPLETED" />
127-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:106:17-79
127-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:106:25-76
128            </intent-filter>
129        </receiver>
130
131        <!-- 通知操作接收器 -->
132        <receiver
132-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:111:9-120:20
133            android:name="com.timeflow.app.utils.NotificationActionReceiver"
133-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:112:13-61
134            android:exported="false" >
134-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:113:13-37
135            <intent-filter>
135-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:114:13-119:29
136                <action android:name="COMPLETE_TASK" />
136-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:115:17-56
136-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:115:25-53
137                <action android:name="POSTPONE_TASK" />
137-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:116:17-56
137-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:116:25-53
138                <action android:name="COMPLETE_HABIT" />
138-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:117:17-57
138-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:117:25-54
139                <action android:name="REMIND_HABIT_LATER" />
139-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:118:17-61
139-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:118:25-58
140            </intent-filter>
141        </receiver>
142
143        <!-- 🔧 新增：专注计时操作接收器 -->
144        <receiver
144-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:123:9-133:20
145            android:name="com.timeflow.app.receiver.FocusTimerActionReceiver"
145-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:124:13-62
146            android:exported="false" >
146-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:125:13-37
147            <intent-filter>
147-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:126:13-132:29
148                <action android:name="START_TIMER" />
148-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:127:17-54
148-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:127:25-51
149                <action android:name="PAUSE_TIMER" />
149-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:128:17-54
149-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:128:25-51
150                <action android:name="RESUME_TIMER" />
150-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:129:17-55
150-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:129:25-52
151                <action android:name="STOP_TIMER" />
151-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:130:17-53
151-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:130:25-50
152                <action android:name="SHOW_APP" />
152-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:131:17-51
152-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:131:25-48
153            </intent-filter>
154        </receiver>
155
156        <!-- 🔧 新增：任务常驻通知操作接收器 -->
157        <receiver
157-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:136:9-146:20
158            android:name="com.timeflow.app.receiver.TaskPersistentNotificationActionReceiver"
158-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:137:13-78
159            android:exported="false" >
159-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:138:13-37
160            <intent-filter>
160-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:139:13-145:29
161                <action android:name="START_PERSISTENT" />
161-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:140:17-59
161-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:140:25-56
162                <action android:name="STOP_PERSISTENT" />
162-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:141:17-58
162-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:141:25-55
163                <action android:name="COMPLETE_TASK" />
163-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:115:17-56
163-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:115:25-53
164                <action android:name="SHOW_TASKS" />
164-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:143:17-53
164-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:143:25-50
165                <action android:name="REFRESH_TASKS" />
165-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:144:17-56
165-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:144:25-53
166            </intent-filter>
167        </receiver>
168
169        <!-- 通知测试服务 -->
170        <service
170-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:149:9-151:40
171            android:name="com.timeflow.app.service.NotificationTestService"
171-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:150:13-60
172            android:exported="false" />
172-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:151:13-37
173
174        <!-- 🔧 新增：专注计时前台服务 -->
175        <service
175-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:154:9-157:58
176            android:name="com.timeflow.app.service.FocusTimerService"
176-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:155:13-54
177            android:exported="false"
177-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:156:13-37
178            android:foregroundServiceType="specialUse" />
178-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:157:13-55
179
180        <!-- 🔧 新增：任务常驻通知前台服务 -->
181        <service
181-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:160:9-163:58
182            android:name="com.timeflow.app.service.TaskPersistentNotificationService"
182-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:161:13-70
183            android:exported="false"
183-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:162:13-37
184            android:foregroundServiceType="specialUse" />
184-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:163:13-55
185
186        <!-- WorkManager initialization -->
187        <provider
188            android:name="androidx.startup.InitializationProvider"
188-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:167:13-67
189            android:authorities="com.timeflow.app.androidx-startup"
189-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:168:13-68
190            android:exported="false" >
190-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:169:13-37
191            <meta-data
191-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:175:13-177:52
192                android:name="com.timeflow.app.di.WorkManagerInitializer"
192-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:176:17-74
193                android:value="androidx.startup" />
193-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:177:17-49
194            <meta-data
194-->[androidx.emoji2:emoji2:1.3.0] D:\development\Android\gradle\caches\8.11.1\transforms\f7bae21354e558eb0376dedde1961243\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
195                android:name="androidx.emoji2.text.EmojiCompatInitializer"
195-->[androidx.emoji2:emoji2:1.3.0] D:\development\Android\gradle\caches\8.11.1\transforms\f7bae21354e558eb0376dedde1961243\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
196                android:value="androidx.startup" />
196-->[androidx.emoji2:emoji2:1.3.0] D:\development\Android\gradle\caches\8.11.1\transforms\f7bae21354e558eb0376dedde1961243\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
197            <meta-data
197-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\development\Android\gradle\caches\8.11.1\transforms\60fc766cbb27c715f12e8233d3d92850\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
198                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
198-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\development\Android\gradle\caches\8.11.1\transforms\60fc766cbb27c715f12e8233d3d92850\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
199                android:value="androidx.startup" />
199-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\development\Android\gradle\caches\8.11.1\transforms\60fc766cbb27c715f12e8233d3d92850\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
200            <meta-data
200-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
201                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
201-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
202                android:value="androidx.startup" />
202-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
203        </provider>
204
205        <!-- Notification channels for OPPO devices -->
206        <meta-data
206-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:181:9-183:35
207            android:name="android.max_aspect"
207-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:182:13-46
208            android:value="2.4" />
208-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:183:13-32
209
210        <!-- Automatic backup settings -->
211        <meta-data
211-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:186:9-188:82
212            android:name="com.google.android.backup.api_key"
212-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:187:13-61
213            android:value="AEdPqrEAAAAIqYVTTbHXb52YeDRQIqNiSUnJtpQEJCOLy7Qo0A" />
213-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:188:13-79
214
215        <!-- 添加 FileProvider -->
216        <provider
217            android:name="androidx.core.content.FileProvider"
217-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:192:13-62
218            android:authorities="com.timeflow.app.fileprovider"
218-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:193:13-64
219            android:exported="false"
219-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:194:13-37
220            android:grantUriPermissions="true" >
220-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:195:13-47
221            <meta-data
221-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:196:13-198:54
222                android:name="android.support.FILE_PROVIDER_PATHS"
222-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:197:17-67
223                android:resource="@xml/file_paths" />
223-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:198:17-51
224        </provider>
225
226        <!-- 小组件 -->
227        <!-- 时间洞察小组件 -->
228        <receiver
228-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:203:9-212:20
229            android:name="com.timeflow.app.widget.TimeInsightWidget"
229-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:204:13-53
230            android:exported="true" >
230-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:205:13-36
231            <intent-filter>
231-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:206:13-208:29
232                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
232-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:207:17-84
232-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:207:25-81
233            </intent-filter>
234
235            <meta-data
235-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:209:13-211:68
236                android:name="android.appwidget.provider"
236-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:210:17-58
237                android:resource="@xml/time_insight_widget_info" />
237-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:211:17-65
238        </receiver>
239
240        <!-- 今日待办小组件 -->
241        <receiver
241-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:215:9-224:20
242            android:name="com.timeflow.app.widget.TodayTasksWidget"
242-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:216:13-52
243            android:exported="true" >
243-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:217:13-36
244            <intent-filter>
244-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:206:13-208:29
245                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
245-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:207:17-84
245-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:207:25-81
246            </intent-filter>
247
248            <meta-data
248-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:209:13-211:68
249                android:name="android.appwidget.provider"
249-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:210:17-58
250                android:resource="@xml/today_tasks_widget_info" />
250-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:211:17-65
251        </receiver>
252
253        <!-- 快速计时小组件 -->
254        <receiver
254-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:227:9-238:20
255            android:name="com.timeflow.app.widget.QuickTimerWidget"
255-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:228:13-52
256            android:exported="true" >
256-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:229:13-36
257            <intent-filter>
257-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:230:13-234:29
258                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
258-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:207:17-84
258-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:207:25-81
259                <action android:name="com.timeflow.app.widget.START_TIMER" />
259-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:232:17-78
259-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:232:25-75
260                <action android:name="com.timeflow.app.widget.STOP_TIMER" />
260-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:233:17-77
260-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:233:25-74
261            </intent-filter>
262
263            <meta-data
263-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:209:13-211:68
264                android:name="android.appwidget.provider"
264-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:210:17-58
265                android:resource="@xml/quick_timer_widget_info" />
265-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:211:17-65
266        </receiver>
267
268        <!-- 专注计时器小组件 -->
269        <receiver
269-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:241:9-253:20
270            android:name="com.timeflow.app.widget.FocusTimerWidget"
270-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:242:13-52
271            android:exported="true" >
271-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:243:13-36
272            <intent-filter>
272-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:244:13-249:29
273                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
273-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:207:17-84
273-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:207:25-81
274                <action android:name="com.timeflow.app.widget.PLAY_PAUSE" />
274-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:246:17-77
274-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:246:25-74
275                <action android:name="com.timeflow.app.widget.STOP" />
275-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:247:17-71
275-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:247:25-68
276                <action android:name="com.timeflow.app.widget.REFRESH" />
276-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:248:17-74
276-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:248:25-71
277            </intent-filter>
278
279            <meta-data
279-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:209:13-211:68
280                android:name="android.appwidget.provider"
280-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:210:17-58
281                android:resource="@xml/focus_timer_widget_info" />
281-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:211:17-65
282        </receiver>
283
284        <!-- 周统计小组件 -->
285        <receiver
285-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:256:9-265:20
286            android:name="com.timeflow.app.widget.WeeklyStatsWidget"
286-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:257:13-53
287            android:exported="true" >
287-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:258:13-36
288            <intent-filter>
288-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:206:13-208:29
289                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
289-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:207:17-84
289-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:207:25-81
290            </intent-filter>
291
292            <meta-data
292-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:209:13-211:68
293                android:name="android.appwidget.provider"
293-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:210:17-58
294                android:resource="@xml/weekly_stats_widget_info" />
294-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:211:17-65
295        </receiver>
296
297        <!-- 目标进度小组件 -->
298        <receiver
298-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:268:9-277:20
299            android:name="com.timeflow.app.widget.GoalProgressWidget"
299-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:269:13-54
300            android:exported="true" >
300-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:270:13-36
301            <intent-filter>
301-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:206:13-208:29
302                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
302-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:207:17-84
302-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:207:25-81
303            </intent-filter>
304
305            <meta-data
305-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:209:13-211:68
306                android:name="android.appwidget.provider"
306-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:210:17-58
307                android:resource="@xml/goal_progress_widget_info" />
307-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:211:17-65
308        </receiver>
309
310        <!-- 🔧 循环任务初始化器 -->
311        <provider
312            android:name="androidx.startup.InitializationProvider"
312-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:167:13-67
313            android:authorities="com.timeflow.app.androidx-startup"
313-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:168:13-68
314            android:exported="false" >
314-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:169:13-37
315            <meta-data
315-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:285:13-287:52
316                android:name="com.timeflow.app.initializer.RecurringTaskInitializer"
316-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:286:17-85
317                android:value="androidx.startup" />
317-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:287:17-49
318        </provider>
319
320        <service
320-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
321            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
321-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
322            android:directBootAware="false"
322-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
323            android:enabled="@bool/enable_system_alarm_service_default"
323-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
324            android:exported="false" />
324-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
325        <service
325-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
326            android:name="androidx.work.impl.background.systemjob.SystemJobService"
326-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
327            android:directBootAware="false"
327-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
328            android:enabled="@bool/enable_system_job_service_default"
328-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
329            android:exported="true"
329-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
330            android:permission="android.permission.BIND_JOB_SERVICE" />
330-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
331        <service
331-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
332            android:name="androidx.work.impl.foreground.SystemForegroundService"
332-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
333            android:directBootAware="false"
333-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
334            android:enabled="@bool/enable_system_foreground_service_default"
334-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
335            android:exported="false" />
335-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
336
337        <receiver
337-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
338            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
338-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
339            android:directBootAware="false"
339-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
340            android:enabled="true"
340-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
341            android:exported="false" />
341-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
342        <receiver
342-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
343            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
343-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
344            android:directBootAware="false"
344-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
345            android:enabled="false"
345-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
346            android:exported="false" >
346-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
347            <intent-filter>
347-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
348                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
348-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
348-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
349                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
349-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
349-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
350            </intent-filter>
351        </receiver>
352        <receiver
352-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
353            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
353-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
354            android:directBootAware="false"
354-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
355            android:enabled="false"
355-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
356            android:exported="false" >
356-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
357            <intent-filter>
357-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
358                <action android:name="android.intent.action.BATTERY_OKAY" />
358-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
358-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
359                <action android:name="android.intent.action.BATTERY_LOW" />
359-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
359-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
360            </intent-filter>
361        </receiver>
362        <receiver
362-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
363            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
363-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
364            android:directBootAware="false"
364-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
365            android:enabled="false"
365-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
366            android:exported="false" >
366-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
367            <intent-filter>
367-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
368                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
368-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
368-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
369                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
369-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
369-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
370            </intent-filter>
371        </receiver>
372        <receiver
372-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
373            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
373-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
374            android:directBootAware="false"
374-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
375            android:enabled="false"
375-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
376            android:exported="false" >
376-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
377            <intent-filter>
377-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
378                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
378-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
378-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
379            </intent-filter>
380        </receiver>
381        <receiver
381-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
382            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
382-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
383            android:directBootAware="false"
383-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
384            android:enabled="false"
384-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
385            android:exported="false" >
385-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
386            <intent-filter>
386-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
387                <action android:name="android.intent.action.BOOT_COMPLETED" />
387-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:106:17-79
387-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:106:25-76
388                <action android:name="android.intent.action.TIME_SET" />
388-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
388-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
389                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
389-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
389-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
390            </intent-filter>
391        </receiver>
392        <receiver
392-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
393            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
393-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
394            android:directBootAware="false"
394-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
395            android:enabled="@bool/enable_system_alarm_service_default"
395-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
396            android:exported="false" >
396-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
397            <intent-filter>
397-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
398                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
398-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
398-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
399            </intent-filter>
400        </receiver>
401        <receiver
401-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
402            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
402-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
403            android:directBootAware="false"
403-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
404            android:enabled="true"
404-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
405            android:exported="true"
405-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
406            android:permission="android.permission.DUMP" >
406-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
407            <intent-filter>
407-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
408                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
408-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
408-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
409            </intent-filter>
410        </receiver>
411
412        <activity
412-->[androidx.compose.ui:ui-tooling-android:1.6.2] D:\development\Android\gradle\caches\8.11.1\transforms\e0323427d6271e66aa0784cfd040b6fb\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
413            android:name="androidx.compose.ui.tooling.PreviewActivity"
413-->[androidx.compose.ui:ui-tooling-android:1.6.2] D:\development\Android\gradle\caches\8.11.1\transforms\e0323427d6271e66aa0784cfd040b6fb\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
414            android:exported="true" />
414-->[androidx.compose.ui:ui-tooling-android:1.6.2] D:\development\Android\gradle\caches\8.11.1\transforms\e0323427d6271e66aa0784cfd040b6fb\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
415        <activity
415-->[androidx.compose.ui:ui-test-manifest:1.6.2] D:\development\Android\gradle\caches\8.11.1\transforms\14c1da9b40958075b50036a34014f52c\transformed\ui-test-manifest-1.6.2\AndroidManifest.xml:23:9-25:39
416            android:name="androidx.activity.ComponentActivity"
416-->[androidx.compose.ui:ui-test-manifest:1.6.2] D:\development\Android\gradle\caches\8.11.1\transforms\14c1da9b40958075b50036a34014f52c\transformed\ui-test-manifest-1.6.2\AndroidManifest.xml:24:13-63
417            android:exported="true" />
417-->[androidx.compose.ui:ui-test-manifest:1.6.2] D:\development\Android\gradle\caches\8.11.1\transforms\14c1da9b40958075b50036a34014f52c\transformed\ui-test-manifest-1.6.2\AndroidManifest.xml:25:13-36
418
419        <uses-library
419-->[androidx.window:window:1.0.0] D:\development\Android\gradle\caches\8.11.1\transforms\ac812fd31ead2ff3fba2b67a79fdb627\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
420            android:name="androidx.window.extensions"
420-->[androidx.window:window:1.0.0] D:\development\Android\gradle\caches\8.11.1\transforms\ac812fd31ead2ff3fba2b67a79fdb627\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
421            android:required="false" />
421-->[androidx.window:window:1.0.0] D:\development\Android\gradle\caches\8.11.1\transforms\ac812fd31ead2ff3fba2b67a79fdb627\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
422        <uses-library
422-->[androidx.window:window:1.0.0] D:\development\Android\gradle\caches\8.11.1\transforms\ac812fd31ead2ff3fba2b67a79fdb627\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
423            android:name="androidx.window.sidecar"
423-->[androidx.window:window:1.0.0] D:\development\Android\gradle\caches\8.11.1\transforms\ac812fd31ead2ff3fba2b67a79fdb627\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
424            android:required="false" />
424-->[androidx.window:window:1.0.0] D:\development\Android\gradle\caches\8.11.1\transforms\ac812fd31ead2ff3fba2b67a79fdb627\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
425
426        <service
426-->[androidx.room:room-runtime:2.6.1] D:\development\Android\gradle\caches\8.11.1\transforms\83fec271716eb865a80add3efead4ed6\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
427            android:name="androidx.room.MultiInstanceInvalidationService"
427-->[androidx.room:room-runtime:2.6.1] D:\development\Android\gradle\caches\8.11.1\transforms\83fec271716eb865a80add3efead4ed6\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
428            android:directBootAware="true"
428-->[androidx.room:room-runtime:2.6.1] D:\development\Android\gradle\caches\8.11.1\transforms\83fec271716eb865a80add3efead4ed6\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
429            android:exported="false" />
429-->[androidx.room:room-runtime:2.6.1] D:\development\Android\gradle\caches\8.11.1\transforms\83fec271716eb865a80add3efead4ed6\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
430
431        <receiver
431-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
432            android:name="androidx.profileinstaller.ProfileInstallReceiver"
432-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
433            android:directBootAware="false"
433-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
434            android:enabled="true"
434-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
435            android:exported="true"
435-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
436            android:permission="android.permission.DUMP" >
436-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
437            <intent-filter>
437-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
438                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
438-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
438-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
439            </intent-filter>
440            <intent-filter>
440-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
441                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
441-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
441-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
442            </intent-filter>
443            <intent-filter>
443-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
444                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
444-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
444-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
445            </intent-filter>
446            <intent-filter>
446-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
447                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
447-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
447-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
448            </intent-filter>
449        </receiver>
450
451        <uses-library
451-->[com.amazonaws:aws-android-sdk-core:2.76.0] D:\development\Android\gradle\caches\8.11.1\transforms\7d417b4ddf552c8a183d7e185e1abe83\transformed\aws-android-sdk-core-2.76.0\AndroidManifest.xml:11:9-13:40
452            android:name="org.apache.http.legacy"
452-->[com.amazonaws:aws-android-sdk-core:2.76.0] D:\development\Android\gradle\caches\8.11.1\transforms\7d417b4ddf552c8a183d7e185e1abe83\transformed\aws-android-sdk-core-2.76.0\AndroidManifest.xml:12:13-50
453            android:required="false" />
453-->[com.amazonaws:aws-android-sdk-core:2.76.0] D:\development\Android\gradle\caches\8.11.1\transforms\7d417b4ddf552c8a183d7e185e1abe83\transformed\aws-android-sdk-core-2.76.0\AndroidManifest.xml:13:13-37
454    </application>
455
456</manifest>
