package com.timeflow.app.widget.layout

import android.content.Context
import androidx.test.core.app.ApplicationProvider
import com.timeflow.app.widget.layout.WidgetSizeManager.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue
import kotlin.test.assertFalse

/**
 * WidgetSizeManager 单元测试
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [28])
class WidgetSizeManagerTest {

    private lateinit var context: Context
    private lateinit var sizeManager: WidgetSizeManager

    @Before
    fun setup() {
        context = ApplicationProvider.getApplicationContext()
        sizeManager = WidgetSizeManager.getInstance(context)
    }

    @Test
    fun `test singleton instance`() {
        val instance1 = WidgetSizeManager.getInstance(context)
        val instance2 = WidgetSizeManager.getInstance(context)
        assertEquals(instance1, instance2, "WidgetSizeManager should be singleton")
    }

    @Test
    fun `test widget size determination`() {
        // 测试小尺寸 (1x1)
        val smallSize = sizeManager.determineWidgetSize(100, 100)
        assertEquals(WidgetSize.SMALL, smallSize)

        // 测试中等横向 (2x1)
        val mediumHorizontalSize = sizeManager.determineWidgetSize(160, 100)
        assertEquals(WidgetSize.MEDIUM_HORIZONTAL, mediumHorizontalSize)

        // 测试中等尺寸 (2x2)
        val mediumSize = sizeManager.determineWidgetSize(160, 160)
        assertEquals(WidgetSize.MEDIUM, mediumSize)

        // 测试大尺寸横向 (4x2)
        val largeHorizontalSize = sizeManager.determineWidgetSize(300, 160)
        assertEquals(WidgetSize.LARGE_HORIZONTAL, largeHorizontalSize)

        // 测试大尺寸 (4x3)
        val largeSize = sizeManager.determineWidgetSize(300, 230)
        assertEquals(WidgetSize.LARGE, largeSize)

        // 测试超大尺寸 (4x4)
        val extraLargeSize = sizeManager.determineWidgetSize(350, 300)
        assertEquals(WidgetSize.EXTRA_LARGE, extraLargeSize)
    }

    @Test
    fun `test layout configuration`() {
        WidgetSize.values().forEach { size ->
            val config = sizeManager.getLayoutConfig(size)
            
            assertNotNull(config, "Layout config should not be null for $size")
            assertEquals(size, config.size, "Config size should match requested size")
            assertTrue(config.widthDp > 0, "Width should be positive for $size")
            assertTrue(config.heightDp > 0, "Height should be positive for $size")
            assertTrue(config.paddingDp >= 0, "Padding should be non-negative for $size")
            assertTrue(config.maxItems > 0, "Max items should be positive for $size")
            assertTrue(config.textScale > 0, "Text scale should be positive for $size")
            assertTrue(config.iconScale > 0, "Icon scale should be positive for $size")
            assertTrue(config.spacingScale > 0, "Spacing scale should be positive for $size")
        }
    }

    @Test
    fun `test text size calculation`() {
        TextType.values().forEach { textType ->
            WidgetSize.values().forEach { size ->
                val textSize = sizeManager.getOptimalTextSize(size, textType)
                assertTrue(textSize > 0, "Text size should be positive for $textType in $size")
                
                // 验证文本大小的相对关系
                if (textType == TextType.DISPLAY_LARGE) {
                    val smallTextSize = sizeManager.getOptimalTextSize(size, TextType.BODY_SMALL)
                    assertTrue(textSize > smallTextSize, "Display large should be larger than body small")
                }
            }
        }
    }

    @Test
    fun `test UI element visibility rules`() {
        // 标题应该总是显示
        WidgetSize.values().forEach { size ->
            assertTrue(
                sizeManager.shouldShowElement(UIElement.TITLE, size),
                "Title should always be visible for $size"
            )
        }

        // 副标题在小尺寸时不显示
        assertFalse(
            sizeManager.shouldShowElement(UIElement.SUBTITLE, WidgetSize.SMALL),
            "Subtitle should not be visible in small size"
        )
        assertTrue(
            sizeManager.shouldShowElement(UIElement.SUBTITLE, WidgetSize.MEDIUM),
            "Subtitle should be visible in medium size"
        )

        // 图表需要足够的空间
        assertFalse(
            sizeManager.shouldShowElement(UIElement.CHART, WidgetSize.SMALL),
            "Chart should not be visible in small size"
        )
        assertTrue(
            sizeManager.shouldShowElement(UIElement.CHART, WidgetSize.MEDIUM),
            "Chart should be visible in medium size"
        )
    }

    @Test
    fun `test icon size scaling`() {
        val sizes = WidgetSize.values()
        val iconSizes = sizes.map { sizeManager.getIconSize(it) }
        
        // 验证图标大小随尺寸增加
        for (i in 1 until iconSizes.size) {
            assertTrue(
                iconSizes[i] >= iconSizes[i-1],
                "Icon size should not decrease with larger widget size"
            )
        }
        
        // 验证所有图标大小都是正数
        iconSizes.forEach { iconSize ->
            assertTrue(iconSize > 0, "Icon size should be positive")
        }
    }

    @Test
    fun `test spacing calculation`() {
        SpacingType.values().forEach { spacingType ->
            WidgetSize.values().forEach { size ->
                val spacing = sizeManager.getSpacing(size, spacingType)
                assertTrue(spacing > 0, "Spacing should be positive for $spacingType in $size")
            }
        }

        // 验证间距类型的相对大小
        val size = WidgetSize.MEDIUM
        val extraSmall = sizeManager.getSpacing(size, SpacingType.EXTRA_SMALL)
        val small = sizeManager.getSpacing(size, SpacingType.SMALL)
        val medium = sizeManager.getSpacing(size, SpacingType.MEDIUM)
        val large = sizeManager.getSpacing(size, SpacingType.LARGE)
        val extraLarge = sizeManager.getSpacing(size, SpacingType.EXTRA_LARGE)

        assertTrue(extraSmall < small, "Extra small spacing should be less than small")
        assertTrue(small < medium, "Small spacing should be less than medium")
        assertTrue(medium < large, "Medium spacing should be less than large")
        assertTrue(large < extraLarge, "Large spacing should be less than extra large")
    }

    @Test
    fun `test size spec calculation`() {
        val widthPx = 320
        val heightPx = 240
        val sizeSpec = sizeManager.getSizeSpec(widthPx, heightPx)
        
        assertEquals(widthPx, sizeSpec.widthPx, "Width in pixels should match")
        assertEquals(heightPx, sizeSpec.heightPx, "Height in pixels should match")
        assertTrue(sizeSpec.widthDp > 0, "Width in dp should be positive")
        assertTrue(sizeSpec.heightDp > 0, "Height in dp should be positive")
        assertTrue(sizeSpec.density > 0, "Density should be positive")
    }

    @Test
    fun `test recommended columns and rows`() {
        WidgetSize.values().forEach { size ->
            val columns = sizeManager.getRecommendedColumns(size)
            val rows = sizeManager.getRecommendedRows(size)
            
            assertTrue(columns > 0, "Columns should be positive for $size")
            assertTrue(rows > 0, "Rows should be positive for $size")
            
            // 验证列数和行数与尺寸的关系
            when (size) {
                WidgetSize.SMALL -> {
                    assertEquals(1, columns, "Small size should have 1 column")
                    assertEquals(1, rows, "Small size should have 1 row")
                }
                WidgetSize.MEDIUM_HORIZONTAL -> {
                    assertEquals(2, columns, "Medium horizontal should have 2 columns")
                    assertEquals(1, rows, "Medium horizontal should have 1 row")
                }
                WidgetSize.MEDIUM -> {
                    assertEquals(1, columns, "Medium should have 1 column")
                    assertTrue(rows > 1, "Medium should have more than 1 row")
                }
                else -> {
                    // 其他尺寸的验证
                }
            }
        }
    }

    @Test
    fun `test adaptive text size calculation`() {
        val shortText = "Hi"
        val longText = "This is a very long text that should result in smaller font size"
        
        val maxWidthPx = 200
        val maxHeightPx = 100
        
        val shortTextSize = sizeManager.calculateAdaptiveTextSize(shortText, maxWidthPx, maxHeightPx)
        val longTextSize = sizeManager.calculateAdaptiveTextSize(longText, maxWidthPx, maxHeightPx)
        
        assertTrue(shortTextSize > 0, "Short text size should be positive")
        assertTrue(longTextSize > 0, "Long text size should be positive")
        assertTrue(shortTextSize >= longTextSize, "Short text should have larger or equal font size")
        
        // 验证文本大小在合理范围内
        assertTrue(shortTextSize >= 8f, "Text size should not be too small")
        assertTrue(shortTextSize <= 24f, "Text size should not be too large")
    }

    @Test
    fun `test responsive margin calculation`() {
        MarginType.values().forEach { marginType ->
            WidgetSize.values().forEach { size ->
                val margin = sizeManager.getResponsiveMargin(size, marginType)
                assertTrue(margin > 0, "Margin should be positive for $marginType in $size")
            }
        }
    }

    @Test
    fun `test layout type detection`() {
        // 测试紧凑布局
        assertTrue(sizeManager.isCompactLayout(WidgetSize.SMALL), "Small should be compact")
        assertTrue(sizeManager.isCompactLayout(WidgetSize.MEDIUM_HORIZONTAL), "Medium horizontal should be compact")
        assertFalse(sizeManager.isCompactLayout(WidgetSize.LARGE), "Large should not be compact")

        // 测试宽屏布局
        assertFalse(sizeManager.isWideLayout(WidgetSize.SMALL), "Small should not be wide")
        assertFalse(sizeManager.isWideLayout(WidgetSize.MEDIUM), "Medium should not be wide")
        assertTrue(sizeManager.isWideLayout(WidgetSize.LARGE_HORIZONTAL), "Large horizontal should be wide")

        // 测试高屏布局
        assertFalse(sizeManager.isTallLayout(WidgetSize.SMALL), "Small should not be tall")
        assertFalse(sizeManager.isTallLayout(WidgetSize.MEDIUM_HORIZONTAL), "Medium horizontal should not be tall")
        assertTrue(sizeManager.isTallLayout(WidgetSize.LARGE), "Large should be tall")
    }

    @Test
    fun `test layout orientation`() {
        assertEquals(
            LayoutOrientation.SQUARE,
            sizeManager.getLayoutOrientation(WidgetSize.SMALL),
            "Small should be square"
        )
        assertEquals(
            LayoutOrientation.HORIZONTAL,
            sizeManager.getLayoutOrientation(WidgetSize.MEDIUM_HORIZONTAL),
            "Medium horizontal should be horizontal"
        )
        assertEquals(
            LayoutOrientation.SQUARE,
            sizeManager.getLayoutOrientation(WidgetSize.MEDIUM),
            "Medium should be square"
        )
        assertEquals(
            LayoutOrientation.VERTICAL,
            sizeManager.getLayoutOrientation(WidgetSize.LARGE),
            "Large should be vertical"
        )
    }

    @Test
    fun `test unit conversion methods`() {
        val dpValue = 16
        val spValue = 14f
        
        val pxFromDp = sizeManager.dpToPx(dpValue)
        val dpFromPx = sizeManager.pxToDp(pxFromDp)
        val pxFromSp = sizeManager.spToPx(spValue)
        
        assertTrue(pxFromDp > 0, "Converted px from dp should be positive")
        assertTrue(pxFromSp > 0, "Converted px from sp should be positive")
        assertEquals(dpValue, dpFromPx, "Round-trip dp conversion should be accurate")
    }

    @Test
    fun `test density category detection`() {
        val densityCategory = sizeManager.getDensityCategory()
        assertNotNull(densityCategory, "Density category should not be null")
        
        // 验证密度分类是有效的枚举值
        assertTrue(
            DensityCategory.values().contains(densityCategory),
            "Density category should be a valid enum value"
        )
    }

    @Test
    fun `test adaptation suggestions`() {
        WidgetSize.values().forEach { size ->
            val suggestions = sizeManager.getAdaptationSuggestions(size)
            
            assertTrue(suggestions.isNotEmpty(), "Suggestions should not be empty for $size")
            suggestions.forEach { suggestion ->
                assertTrue(suggestion.isNotBlank(), "Each suggestion should not be blank")
            }
        }
    }

    @Test
    fun `test widget size enum properties`() {
        WidgetSize.values().forEach { size ->
            assertTrue(size.width > 0, "Width should be positive for $size")
            assertTrue(size.height > 0, "Height should be positive for $size")
            assertTrue(size.displayName.isNotBlank(), "Display name should not be blank for $size")
        }
    }

    @Test
    fun `test layout config scaling consistency`() {
        val baseSize = WidgetSize.MEDIUM
        val baseConfig = sizeManager.getLayoutConfig(baseSize)
        
        WidgetSize.values().forEach { size ->
            val config = sizeManager.getLayoutConfig(size)
            
            // 验证缩放因子的一致性
            assertTrue(config.textScale > 0, "Text scale should be positive for $size")
            assertTrue(config.iconScale > 0, "Icon scale should be positive for $size")
            assertTrue(config.spacingScale > 0, "Spacing scale should be positive for $size")
            
            // 小尺寸的缩放因子应该小于等于基准尺寸
            if (size.width <= baseSize.width && size.height <= baseSize.height) {
                assertTrue(
                    config.textScale <= baseConfig.textScale,
                    "Smaller size should have smaller or equal text scale"
                )
            }
        }
    }
}