package com.timeflow.app.ui.statistics

import android.app.Activity
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Share
import androidx.compose.material.icons.filled.TrendingUp
import androidx.compose.material.icons.filled.ViewList
import androidx.compose.material.icons.filled.WatchLater
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.unit.TextUnit
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.timeflow.app.utils.SystemBarManager
import com.timeflow.app.utils.SystemBarManager.getFixedStatusBarHeight
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.format.TextStyle
import java.util.*
import kotlin.math.roundToInt
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.foundation.layout.Arrangement.spacedBy
import androidx.compose.ui.layout.layout
import androidx.compose.ui.unit.Constraints
import androidx.compose.ui.unit.Dp
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.ui.layout.Layout
import androidx.compose.ui.layout.MeasurePolicy
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import android.util.Log
import androidx.compose.material3.FilterChip
import androidx.compose.material3.FilterChipDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.runtime.collectAsState

/**
 * 时间统计页面
 * 展示用户时间使用情况的详细统计与分析
 */
@OptIn(ExperimentalMaterial3Api::class, ExperimentalLayoutApi::class)
@Composable
fun TimeStatisticsScreen(
    navController: NavController,
    viewModel: TimeStatisticsViewModel = hiltViewModel(),
    realViewModel: SimpleRealTimeStatisticsViewModel = hiltViewModel() // 🔧 使用简化的真实数据ViewModel
) {
    // 获取上下文和Activity引用
    val context = LocalContext.current
    val activity = remember { context as? Activity }
    
    // 处理状态栏
    SideEffect {
        activity?.let { 
            SystemBarManager.setupStatisticsPageSystemBars(it)  // 使用专门为统计页面设计的系统栏设置
        }
    }
    
    activity?.let { act ->
        DisposableEffect(key1 = Unit) {
            val window = act.window
            
            // 保存原始值以在dispose时恢复
            val originalStatusBarColor = window.statusBarColor
            val originalNavigationBarColor = window.navigationBarColor
            
            // 应用统计页面专用的状态栏设置
            SystemBarManager.setupStatisticsPageSystemBars(act)
            
            onDispose {
                // 恢复原始状态栏和导航栏颜色
                window.statusBarColor = originalStatusBarColor
                window.navigationBarColor = originalNavigationBarColor
                Log.d("TimeStatisticsScreen", "TimeStatisticsScreen disposed")
            }
        }
    }
    
    // 🔧 使用真实数据ViewModel的状态
    val selectedTimeRange by realViewModel.selectedTimeRange.collectAsState()
    val selectedViewMode by realViewModel.selectedViewMode.collectAsState()
    val isLoading by realViewModel.isLoading.collectAsState()
    val error by realViewModel.error.collectAsState()
    val monthlyData by realViewModel.monthlyData.collectAsState()
    val yearlyData by realViewModel.yearlyData.collectAsState()
    val concreteData by realViewModel.concreteData.collectAsState()
    val macroData by realViewModel.macroData.collectAsState()

    // 本地状态
    var selectedTab by remember { mutableStateOf(0) }
    
    // 颜色设置 - 更柔和的配色方案
    val primaryColor = Color(0xFFCCAEC5) // 新的主色调
    val secondaryColor = Color(0xFFD9C2D4) // 新的辅助色调
    val accentColor = Color(0xFFFF8FB3)
    val neutralColor = Color(0xFFB9A0FF)
    
    // 渐变主题
    val backgroundGradient = Brush.verticalGradient(
        colors = listOf(
            Color(0xFFFFFFFF),  // 纯白色 #ffffff
            Color(0xFFf9e9f3)   // 淡粉色 #ffdde1
        )
    )
    
    // 🔧 使用真实数据替换硬编码数据
    val currentData = when {
        selectedTimeRange.contains("月") -> monthlyData
        selectedTimeRange.contains("年") -> yearlyData
        else -> monthlyData
    }

    // 从真实数据中提取显示数据
    val productivityHours = currentData?.timeStatistics?.totalMinutes?.div(60f) ?: 0f
    val totalHours = 8.0f // 可以从用户设置中获取
    val focusSessions = currentData?.timeStatistics?.focusSessions ?: 0
    val productivityScore = currentData?.productivityScore ?: 0

    // 效率趋势数据
    val dailyData = when (selectedViewMode) {
        com.timeflow.app.data.model.ViewMode.CONCRETE -> concreteData?.dailyBreakdown?.map { it.productivityScore.toFloat() } ?: emptyList()
        com.timeflow.app.data.model.ViewMode.MACRO -> macroData?.productivityTrend?.map { it.value } ?: emptyList()
    }

    // 分类数据
    val categoryData = currentData?.timeStatistics?.timeDistribution ?: emptyMap()

    // 时间分布（基于效率模式）
    val timeDistribution = macroData?.let { macro ->
        val totalTime = productivityHours
        val highEfficiency = totalTime * macro.efficiencyPattern.averageEfficiency
        val mediumEfficiency = totalTime * 0.6f
        val lowEfficiency = totalTime - highEfficiency - mediumEfficiency

        mapOf(
            "高效时间" to maxOf(0f, highEfficiency),
            "中性时间" to maxOf(0f, mediumEfficiency),
            "低效时间" to maxOf(0f, lowEfficiency)
        )
    } ?: mapOf("暂无数据" to 0f)

    // 任务标签（从具象数据中获取）
    val taskTags = concreteData?.tagAnalysis?.topTags?.map {
        it.tag to it.frequency
    } ?: emptyList()

    // 热力图数据（从具象数据中获取）
    val heatmapData = concreteData?.hourlyHeatmap?.data ?: Array(7) { Array(24) { 0 } }

    // 主界面 - 使用padding来避免顶部内容被状态栏覆盖
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(brush = backgroundGradient)
            .padding(top = getFixedStatusBarHeight()) // 直接在Column上添加顶部内边距
    ) {
        // 顶部标题栏
        TopAppBar(
            title = { Text("时间统计") },
            navigationIcon = {
                IconButton(onClick = { navController.popBackStack() }) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                }
            },
            actions = {
                IconButton(onClick = { /* 分享统计数据 */ }) {
                    Icon(Icons.Default.Share, contentDescription = "分享")
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = Color.White,  // 使用白色背景，与状态栏颜色匹配
                titleContentColor = MaterialTheme.colorScheme.primary
            )
        )
        
        // 时间范围选择器
        LazyRow(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 8.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            val timeRanges = listOf("今日", "本周", "本月", "季度", "年度")
            items(timeRanges) { range ->
                FilterChip(
                    selected = selectedTimeRange == range,
                    onClick = { realViewModel.setTimeRange(range) },
                    label = { Text(range) },
                    colors = FilterChipDefaults.filterChipColors(
                        selectedContainerColor = primaryColor,
                        selectedLabelColor = Color.White
                    )
                )
            }
        }
        
        // 主要内容
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 时间范围选择器已经在上面实现了

            // 🔧 具象/宏观视图切换按钮
            item {
                ViewModeSelector(
                    selectedMode = selectedViewMode,
                    onModeSelected = { mode ->
                        realViewModel.setViewMode(mode)
                    },
                    primaryColor = primaryColor
                )
            }

            // 🔧 加载状态和错误处理
            if (isLoading) {
                item {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(32.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator(color = primaryColor)
                    }
                }
            }

            error?.let { errorMessage ->
                item {
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(containerColor = Color(0xFFFFEBEE))
                    ) {
                        Text(
                            text = "加载失败: $errorMessage",
                            modifier = Modifier.padding(16.dp),
                            color = Color(0xFFD32F2F)
                        )
                    }
                }
            }

            // 头部总览卡片
            if (!isLoading && error == null) {
                item {
                    SummaryCard(
                        productivityHours = productivityHours,
                        totalHours = totalHours,
                        focusSessions = focusSessions,
                        productivityScore = productivityScore,
                        primaryColor = primaryColor
                    )
                }
            }
            
            // 🔧 根据视图模式显示不同内容
            when (selectedViewMode) {
                com.timeflow.app.data.model.ViewMode.CONCRETE -> {
                    // 具象视图：详细数据展示
                    item {
                        ConcreteViewContent(
                            concreteData = concreteData,
                            primaryColor = primaryColor,
                            secondaryColor = secondaryColor,
                            accentColor = accentColor
                        )
                    }
                }
                com.timeflow.app.data.model.ViewMode.MACRO -> {
                    // 宏观视图：趋势和概览
                    item {
                        MacroViewContent(
                            macroData = macroData,
                            monthlyData = monthlyData,
                            yearlyData = yearlyData,
                            selectedTimeRange = selectedTimeRange,
                            primaryColor = primaryColor,
                            secondaryColor = secondaryColor,
                            accentColor = accentColor
                        )
                    }
                }
            }
            
            // 分类统计标题
            item {
                Text(
                    text = "分类统计",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }
            
            // 任务签云
            item {
                TaskTagCloudCard(taskTags, primaryColor)
            }
            
            // 时段热力图
            item {
                TimeHeatmapCard(heatmapData, primaryColor)
            }
            
            // 底部空白
            item {
                Spacer(modifier = Modifier.height(32.dp))
            }
        }
    }
}

/**
 * 头部总览卡片组件
 */
@Composable
fun SummaryCard(
    productivityHours: Float,
    totalHours: Float,
    focusSessions: Int,
    productivityScore: Int,
    primaryColor: Color
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 标题
            Text(
                text = "总览",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            // 4个关键指标
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                // 生产力得分
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Box(
                        contentAlignment = Alignment.Center,
                        modifier = Modifier
                            .size(60.dp)
                            .drawBehind {
                                // 绘制圆形进度条
                                val strokeWidth = 5.dp.toPx()
                                val center = Offset(size.width / 2, size.height / 2)
                                val radius = (size.minDimension - strokeWidth) / 2
                                
                                // 背景圆
                                drawCircle(
                                    color = Color.LightGray.copy(alpha = 0.3f),
                                    radius = radius,
                                    center = center,
                                    style = Stroke(width = strokeWidth)
                                )
                                
                                // 进度圆
                                val sweepAngle = 360f * productivityScore / 100
                                drawArc(
                                    color = primaryColor,
                                    startAngle = -90f,
                                    sweepAngle = sweepAngle,
                                    useCenter = false,
                                    topLeft = Offset(center.x - radius, center.y - radius),
                                    size = Size(radius * 2, radius * 2),
                                    style = Stroke(width = strokeWidth, cap = StrokeCap.Round)
                                )
                            }
                    ) {
                        Text(
                            text = "$productivityScore",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(4.dp))
                    
                    Text(
                        text = "效率分",
                        style = MaterialTheme.typography.bodySmall,
                        color = Color.Gray
                    )
                }
                
                // 专注时间
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "${productivityHours}小时",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                    
                    Spacer(modifier = Modifier.height(4.dp))
                    
                    Text(
                        text = "专注时间",
                        style = MaterialTheme.typography.bodySmall,
                        color = Color.Gray
                    )
                }
                
                // 专注次数
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "${focusSessions}次",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                    
                    Spacer(modifier = Modifier.height(4.dp))
                    
                    Text(
                        text = "专注次数",
                        style = MaterialTheme.typography.bodySmall,
                        color = Color.Gray
                    )
                }
                
                // 总记录时间
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "${totalHours}小时",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                    
                    Spacer(modifier = Modifier.height(4.dp))
                    
                    Text(
                        text = "总时间",
                        style = MaterialTheme.typography.bodySmall,
                        color = Color.Gray
                    )
                }
            }
        }
    }
}

/**
 * 时间分布卡片组件
 */
@Composable
fun TimeDistributionCard(
    timeDistribution: Map<String, Float>,
    primaryColor: Color
) {
    val total = timeDistribution.values.sum()
    val colorMap = mapOf(
        "高效时间" to Color(0xFFCCAEC5), // 主色调
        "中性时间" to Color(0xFFB9A0FF), // 紫色
        "低效时间" to Color(0xFFFFB5CD)  // 粉色
    )
    
    // 计算每个部分的百分比
    val percentages = timeDistribution.mapValues { (_, value) -> 
        (value / total * 100).toInt() 
    }
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(300.dp), // 增加高度以容纳更多内容
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // 卡片标题
            Text(
                text = "时间分布",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 饼图和图例区域
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
            ) {
                // 饼图区域
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxHeight(),
                    contentAlignment = Alignment.Center
                ) {
                    // 饼图
                    Canvas(
                        modifier = Modifier
                            .size(180.dp)
                            .padding(8.dp)
                    ) {
                        val donutWidth = size.width * 0.25f // 环形图宽度
                        val outerRadius = size.minDimension / 2.0f
                        val innerRadius = outerRadius - donutWidth
                        val center = Offset(size.width / 2, size.height / 2)
                        
                        var startAngle = 0f
                        val gapDegrees = 2f // 扇形间的间隙
                        
                        // 绘制饼图扇形
                        timeDistribution.forEach { (name, value) ->
                            val sweepAngle = 360f * (value / total) - gapDegrees
                            
                            // 绘制扇形
                            drawArc(
                                color = colorMap[name] ?: Color.Gray,
                                startAngle = startAngle + (gapDegrees / 2),
                                sweepAngle = sweepAngle,
                                useCenter = false,
                                topLeft = Offset(
                                    center.x - outerRadius,
                                    center.y - outerRadius
                                ),
                                size = Size(outerRadius * 2, outerRadius * 2),
                                style = Stroke(width = donutWidth)
                            )
                            
                            startAngle += 360f * (value / total)
                        }
                    }
                    
                    // 中间的总时间
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "${total.toInt()}h",
                            style = MaterialTheme.typography.headlineMedium,
                            fontWeight = FontWeight.Bold
                        )
                        Text(
                            text = "总时间",
                            style = MaterialTheme.typography.bodySmall,
                            color = Color.Gray
                        )
                    }
                }
                
                // 图例区域 - 改为垂直列表
                Column(
                    modifier = Modifier
                        .weight(0.8f)
                        .fillMaxHeight()
                        .padding(start = 8.dp),
                    verticalArrangement = Arrangement.Center
                ) {
                    timeDistribution.forEach { (name, value) ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 4.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // 颜色指示器
                            Box(
                                modifier = Modifier
                                    .size(12.dp)
                                    .background(colorMap[name] ?: Color.Gray, CircleShape)
                            )
                            
                            Spacer(modifier = Modifier.width(8.dp))
                            
                            // 文本区域
                            Column(modifier = Modifier.weight(1f)) {
                                Text(
                                    text = name,
                                    style = MaterialTheme.typography.bodyMedium,
                                    fontWeight = FontWeight.Medium
                                )
                                
                                Row(
                                    horizontalArrangement = Arrangement.SpaceBetween,
                                    modifier = Modifier.fillMaxWidth()
                                ) {
                                    Text(
                                        text = "${value}小时",
                                        style = MaterialTheme.typography.bodySmall,
                                        fontWeight = FontWeight.Bold,
                                        color = colorMap[name] ?: Color.Gray
                                    )
                                    
                                    // 百分比
                                    Text(
                                        text = "${percentages[name]}%",
                                        style = MaterialTheme.typography.bodySmall,
                                        fontWeight = FontWeight.Bold,
                                        color = colorMap[name]?.copy(alpha = 0.7f) ?: Color.Gray
                                    )
                                }
                            }
                        }
                        
                        // 如果不是最后一项，添加一个分隔线
                        if (name != timeDistribution.keys.last()) {
                            Spacer(modifier = Modifier.height(2.dp))
                            Divider(
                                modifier = Modifier.padding(start = 20.dp),
                                color = Color.LightGray.copy(alpha = 0.5f)
                            )
                            Spacer(modifier = Modifier.height(2.dp))
                        }
                    }
                }
            }
        }
    }
}

/**
 * 效率趋势卡片组件
 */
@Composable
fun EfficiencyTrendCard(
    dailyData: List<Float>,
    primaryColor: Color
) {
    val maxValue = dailyData.maxOrNull() ?: 1f
    val today = LocalDate.now()
    val dayNames = (0 until 7).map { i ->
        today.minusDays(6L - i).dayOfWeek.getDisplayName(TextStyle.SHORT, Locale.CHINA)
    }
    
    // 映射日期名和对应数据
    val dateDataMap = dayNames.zip(dailyData)
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(300.dp),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // 卡片标题
            Text(
                text = "效率趋势",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 柱状图
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
            ) {
                // 垂直网格线
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(bottom = 40.dp, start = 20.dp, end = 20.dp, top = 20.dp)
                ) {
                    Canvas(
                        modifier = Modifier.fillMaxSize()
                    ) {
                        val canvasWidth = size.width
                        val canvasHeight = size.height
                        val barSpacing = canvasWidth / 8 // 7个柱子，8个等分空间
                        
                        // 绘制水平辅助线
                        val horizontalLines = 4
                        val lineStrokeWidth = 1.dp.toPx()
                        
                        // 绘制水平辅助线
                        for (i in 0..horizontalLines) {
                            val y = canvasHeight - (canvasHeight * i / horizontalLines)
                            drawLine(
                                color = Color.LightGray.copy(alpha = 0.5f),
                                start = Offset(0f, y),
                                end = Offset(canvasWidth, y),
                                strokeWidth = lineStrokeWidth
                            )
                        }
                    }
                }
                
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 40.dp, start = 20.dp, end = 20.dp, top = 20.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.Bottom
                ) {
                    dateDataMap.forEachIndexed { index, (day, value) ->
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            // 柱状图条
                            Box(
                                modifier = Modifier
                                    .width(30.dp)
                                    .height((value / maxValue * 150).dp) // 增加柱子高度
                                    .clip(RoundedCornerShape(topStart = 4.dp, topEnd = 4.dp))
                                    .background(
                                        brush = Brush.verticalGradient(
                                            colors = listOf(
                                                Color(0xFFCCAEC5), // 主色调
                                                Color(0xFFCCAEC5).copy(alpha = 0.6f)
                                            )
                                        )
                                    )
                            )
                            
                            Spacer(modifier = Modifier.height(8.dp))
                            
                            // 日期标签
                            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                                // 小时值
                                Text(
                                    text = "${value}h",
                                    style = MaterialTheme.typography.bodySmall,
                                    fontWeight = FontWeight.Bold,
                                    color = Color(0xFFCCAEC5),
                                    fontSize = 11.sp
                                )
                                
                                // 日期标签
                                Text(
                                    text = day,
                                    style = MaterialTheme.typography.bodySmall,
                                    color = Color.Gray,
                                    fontSize = 11.sp
                                )
                            }
                        }
                    }
                }
            }
            
            // 趋势分析
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp, 8.dp, 16.dp, 0.dp), // 左、上、右、下
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.TrendingUp,
                    contentDescription = null,
                    tint = Color(0xFFCCAEC5),
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text(
                    text = "本周效率上升12%",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(0xFFCCAEC5),
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

/**
 * 任务标签云卡片组件
 */
@OptIn(ExperimentalLayoutApi::class)
@Composable
fun TaskTagCloudCard(
    taskTags: List<Pair<String, Int>>,
    primaryColor: Color
) {
    val maxCount = taskTags.maxOfOrNull { it.second } ?: 1
    
    Card(
        modifier = Modifier
            .fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 卡片标题
            Text(
                text = "任务标签云",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 标签云 - 使用Compose提供的FlowRow
            FlowRow(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                taskTags.forEach { (tag, count) ->
                    // 使用计算后的文字大小
                    val baseSize = 14
                    val addedSize = (4f * count / maxCount).toInt()
                    val tagSize = (baseSize + addedSize).sp
                    
                    val tagAlpha = 0.5f + (0.5f * count / maxCount)
                    val tagColor = primaryColor.copy(alpha = tagAlpha)
                    
                    Surface(
                        modifier = Modifier
                            .padding(4.dp),
                        shape = RoundedCornerShape(16.dp),
                        color = tagColor.copy(alpha = 0.1f)
                    ) {
                        Text(
                            text = tag,
                            modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp),
                            fontSize = tagSize,
                            color = tagColor,
                            fontWeight = if (count > maxCount / 2) FontWeight.Bold else FontWeight.Normal
                        )
                    }
                }
            }
        }
    }
}

/**
 * 时间热力图卡片组件
 */
@Composable
fun TimeHeatmapCard(
    heatmapData: Array<Array<Int>>,
    primaryColor: Color
) {
    val dayNames = listOf("周一", "周二", "周三", "周四", "周五", "周六", "周日")
    
    Card(
        modifier = Modifier
            .fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 卡片标题
            Text(
                text = "时段热力图",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 热力图
            Column(
                modifier = Modifier.fillMaxWidth()
            ) {
                // 小时标签 (每4小时显示一个)
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(start = 32.dp)
                ) {
                    (0..24 step 4).forEach { hour ->
                        Box(
                            modifier = Modifier.weight(4f),
                            contentAlignment = Alignment.CenterStart
                        ) {
                            if (hour < 24) {
                                Text(
                                    text = "${hour}:00",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = Color.Gray
                                )
                            }
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(4.dp))
                
                // 热力图主体
                heatmapData.forEachIndexed { rowIndex, row ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(28.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // 星期标签
                        Box(
                            modifier = Modifier.width(32.dp),
                            contentAlignment = Alignment.CenterEnd
                        ) {
                            Text(
                                text = dayNames[rowIndex],
                                style = MaterialTheme.typography.bodySmall,
                                color = Color.Gray
                            )
                        }
                        
                        Spacer(modifier = Modifier.width(4.dp))
                        
                        // 热力格子
                        row.forEachIndexed { colIndex, value ->
                            val intensity = value / 10f
                            Box(
                                modifier = Modifier
                                    .weight(1f)
                                    .height(24.dp)
                                    .padding(horizontal = 1.dp)
                                    .background(
                                        color = primaryColor.copy(alpha = intensity),
                                        shape = RoundedCornerShape(2.dp)
                                    )
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 图例
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "较少",
                        style = MaterialTheme.typography.bodySmall,
                        color = Color.Gray
                    )
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Row(
                        modifier = Modifier.weight(1f)
                    ) {
                        (1..5).forEach { i ->
                            Box(
                                modifier = Modifier
                                    .weight(1f)
                                    .height(12.dp)
                                    .padding(horizontal = 2.dp)
                                    .background(
                                        color = primaryColor.copy(alpha = i * 0.2f),
                                        shape = RoundedCornerShape(2.dp)
                                    )
                            )
                        }
                    }
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Text(
                        text = "较多",
                        style = MaterialTheme.typography.bodySmall,
                        color = Color.Gray
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 周期内统计
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.WatchLater,
                    contentDescription = null,
                    tint = primaryColor,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text(
                    text = "工作日9:00-11:00是您的高效时段",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color.Black.copy(alpha = 0.7f)
                )
            }
        }
    }
}

/**
 * 流式布局容器（用于标签云）
 */
@Composable
fun FlowRow(
    modifier: Modifier = Modifier,
    mainAxisSpacing: Dp = 0.dp,
    crossAxisSpacing: Dp = 0.dp,
    content: @Composable () -> Unit
) {
    val measurePolicy = remember(mainAxisSpacing, crossAxisSpacing) {
        createFlowRowMeasurePolicy(mainAxisSpacing, crossAxisSpacing)
    }
    Layout(
        modifier = modifier,
        content = content,
        measurePolicy = measurePolicy
    )
}

/**
 * 创建流式布局测量策略
 */
private fun createFlowRowMeasurePolicy(
    mainAxisSpacing: Dp,
    crossAxisSpacing: Dp
) = MeasurePolicy { measurables, constraints ->
    val spacingWidth = mainAxisSpacing.roundToPx()
    val spacingHeight = crossAxisSpacing.roundToPx()
    
    val placementInfos = mutableListOf<Pair<Int, Int>>() // x, y coordinates
    val sizes = measurables.map { measurable ->
        val placeable = measurable.measure(constraints.copy(minWidth = 0, minHeight = 0))
        placeable
    }
    
    layout(
        width = constraints.maxWidth,
        height = constraints.maxHeight
    ) {
        var xPosition = 0
        var yPosition = 0
        var rowHeight = 0
        
        sizes.forEachIndexed { index, placeable ->
            val elementWidth = placeable.width
            
            if (xPosition + elementWidth > constraints.maxWidth) {
                xPosition = 0
                yPosition += rowHeight + spacingHeight
                rowHeight = 0
            }
            
            placementInfos.add(Pair(xPosition, yPosition))
            rowHeight = maxOf(rowHeight, placeable.height)
            xPosition += elementWidth + spacingWidth
        }
        
        sizes.forEachIndexed { index, placeable ->
            val (x, y) = placementInfos[index]
            placeable.placeRelative(x, y)
        }
    }
}

/**
 * 🔧 视图模式选择器组件
 * 参照calflow应用的具象和宏观按钮功能
 */
@Composable
fun ViewModeSelector(
    selectedMode: com.timeflow.app.data.model.ViewMode,
    onModeSelected: (com.timeflow.app.data.model.ViewMode) -> Unit,
    primaryColor: Color,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "数据视图",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = primaryColor
            )

            Spacer(modifier = Modifier.height(12.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // 具象视图按钮
                FilterChip(
                    selected = selectedMode == com.timeflow.app.data.model.ViewMode.CONCRETE,
                    onClick = { onModeSelected(com.timeflow.app.data.model.ViewMode.CONCRETE) },
                    label = {
                        Text(
                            text = "具象",
                            fontSize = 14.sp,
                            fontWeight = if (selectedMode == com.timeflow.app.data.model.ViewMode.CONCRETE) FontWeight.Bold else FontWeight.Normal
                        )
                    },
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Default.ViewList,
                            contentDescription = "具象视图",
                            modifier = Modifier.size(18.dp)
                        )
                    },
                    colors = FilterChipDefaults.filterChipColors(
                        selectedContainerColor = primaryColor.copy(alpha = 0.2f),
                        selectedLabelColor = primaryColor,
                        selectedLeadingIconColor = primaryColor
                    ),
                    modifier = Modifier.weight(1f)
                )

                // 宏观视图按钮
                FilterChip(
                    selected = selectedMode == com.timeflow.app.data.model.ViewMode.MACRO,
                    onClick = { onModeSelected(com.timeflow.app.data.model.ViewMode.MACRO) },
                    label = {
                        Text(
                            text = "宏观",
                            fontSize = 14.sp,
                            fontWeight = if (selectedMode == com.timeflow.app.data.model.ViewMode.MACRO) FontWeight.Bold else FontWeight.Normal
                        )
                    },
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Default.TrendingUp,
                            contentDescription = "宏观视图",
                            modifier = Modifier.size(18.dp)
                        )
                    },
                    colors = FilterChipDefaults.filterChipColors(
                        selectedContainerColor = primaryColor.copy(alpha = 0.2f),
                        selectedLabelColor = primaryColor,
                        selectedLeadingIconColor = primaryColor
                    ),
                    modifier = Modifier.weight(1f)
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            // 视图模式说明
            Text(
                text = when (selectedMode) {
                    com.timeflow.app.data.model.ViewMode.CONCRETE -> "具象视图：显示详细的任务数据、时间分布和标签分析"
                    com.timeflow.app.data.model.ViewMode.MACRO -> "宏观视图：显示趋势分析、效率模式和整体概览"
                },
                style = MaterialTheme.typography.bodySmall,
                color = Color.Gray,
                fontSize = 12.sp
            )
        }
    }
}

/**
 * 🔧 具象视图内容组件
 * 显示详细的任务数据、时间分布和标签分析
 */
@Composable
fun ConcreteViewContent(
    concreteData: com.timeflow.app.data.model.ConcreteViewData?,
    primaryColor: Color,
    secondaryColor: Color,
    accentColor: Color,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        if (concreteData == null) {
            // 加载状态
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(containerColor = Color.White)
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(32.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text("正在加载详细数据...")
                }
            }
            return
        }

        // 每日分解数据
        Text(
            text = "每日详细分析",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = primaryColor
        )

        if (concreteData.dailyBreakdown.isNotEmpty()) {
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                contentPadding = PaddingValues(horizontal = 4.dp)
            ) {
                items(concreteData.dailyBreakdown) { dayData ->
                    DailyBreakdownCard(
                        dayData = dayData,
                        primaryColor = primaryColor
                    )
                }
            }
        } else {
            Text(
                text = "暂无每日数据",
                style = MaterialTheme.typography.bodyMedium,
                color = Color.Gray
            )
        }

        // 标签分析
        if (concreteData.tagAnalysis.topTags.isNotEmpty()) {
            Text(
                text = "标签分析",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = primaryColor
            )

            TagAnalysisCard(
                tagAnalysis = concreteData.tagAnalysis,
                primaryColor = primaryColor
            )
        }
    }
}

/**
 * 🔧 宏观视图内容组件
 * 显示趋势分析、效率模式和整体概览
 */
@Composable
fun MacroViewContent(
    macroData: com.timeflow.app.data.model.MacroViewData?,
    monthlyData: com.timeflow.app.data.model.MonthlyStatisticsData?,
    yearlyData: com.timeflow.app.data.model.YearlyStatisticsData?,
    selectedTimeRange: String,
    primaryColor: Color,
    secondaryColor: Color,
    accentColor: Color,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        if (macroData == null) {
            // 加载状态
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(containerColor = Color.White)
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(32.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text("正在加载趋势数据...")
                }
            }
            return
        }

        // 生产力趋势
        Text(
            text = "生产力趋势",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = primaryColor
        )

        ProductivityTrendCard(
            trendData = macroData.productivityTrend,
            primaryColor = primaryColor
        )

        // 分类分布
        if (macroData.categoryDistribution.isNotEmpty()) {
            Text(
                text = "时间分布",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = primaryColor
            )

            CategoryDistributionCard(
                distribution = macroData.categoryDistribution,
                primaryColor = primaryColor
            )
        }

        // 智能洞察
        if (macroData.overallInsights.isNotEmpty()) {
            Text(
                text = "智能洞察",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = primaryColor
            )

            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(containerColor = accentColor.copy(alpha = 0.1f))
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    macroData.overallInsights.forEachIndexed { index, insight ->
                        Row(
                            verticalAlignment = Alignment.Top
                        ) {
                            Text(
                                text = "•",
                                color = accentColor,
                                fontWeight = FontWeight.Bold,
                                modifier = Modifier.padding(end = 8.dp)
                            )
                            Text(
                                text = insight,
                                style = MaterialTheme.typography.bodyMedium,
                                color = Color.DarkGray
                            )
                        }
                    }
                }
            }
        }
    }
}

// 临时占位组件，需要根据实际需求实现
@Composable
fun DailyBreakdownCard(dayData: com.timeflow.app.data.model.DailyBreakdown, primaryColor: Color) {
    Card(
        modifier = Modifier.width(120.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column(
            modifier = Modifier.padding(12.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "${dayData.date.monthValue}/${dayData.date.dayOfMonth}",
                style = MaterialTheme.typography.bodySmall,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = "${dayData.completedTasks}/${dayData.totalTasks}",
                style = MaterialTheme.typography.bodyMedium,
                color = primaryColor
            )
            Text(
                text = "${dayData.focusMinutes}分钟",
                style = MaterialTheme.typography.bodySmall,
                color = Color.Gray
            )
        }
    }
}

@Composable
fun TagAnalysisCard(tagAnalysis: com.timeflow.app.data.model.TagAnalysis, primaryColor: Color) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            tagAnalysis.topTags.take(5).forEach { tagFreq ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 4.dp),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(text = tagFreq.tag)
                    Text(
                        text = "${tagFreq.frequency}次",
                        color = primaryColor,
                        fontWeight = FontWeight.Bold
                    )
                }
            }
        }
    }
}

@Composable
fun ProductivityTrendCard(trendData: List<com.timeflow.app.data.model.TrendPoint>, primaryColor: Color) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "生产力趋势图",
                style = MaterialTheme.typography.bodyMedium,
                color = Color.Gray
            )
            Spacer(modifier = Modifier.height(16.dp))

            // 简单的趋势显示
            if (trendData.isNotEmpty()) {
                val avgValue = trendData.map { it.value }.average()
                Text(
                    text = "平均生产力: ${String.format("%.1f", avgValue)}",
                    style = MaterialTheme.typography.bodyLarge,
                    color = primaryColor,
                    fontWeight = FontWeight.Bold
                )
            }
        }
    }
}

@Composable
fun CategoryDistributionCard(distribution: Map<String, Float>, primaryColor: Color) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            distribution.entries.take(5).forEach { (category, percentage) ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 4.dp),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(text = category)
                    Text(
                        text = "${String.format("%.1f", percentage)}%",
                        color = primaryColor,
                        fontWeight = FontWeight.Bold
                    )
                }
            }
        }
    }
}