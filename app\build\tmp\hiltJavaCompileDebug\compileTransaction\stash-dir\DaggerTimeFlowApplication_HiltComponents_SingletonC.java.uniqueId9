package com.timeflow.app;

import android.app.Activity;
import android.app.Service;
import android.content.Context;
import android.content.SharedPreferences;
import android.view.View;
import androidx.datastore.core.DataStore;
import androidx.datastore.preferences.core.Preferences;
import androidx.fragment.app.Fragment;
import androidx.hilt.work.HiltWorkerFactory;
import androidx.hilt.work.WorkerAssistedFactory;
import androidx.hilt.work.WorkerFactoryModule_ProvideFactoryFactory;
import androidx.lifecycle.SavedStateHandle;
import androidx.lifecycle.ViewModel;
import androidx.room.RoomDatabase;
import androidx.work.ListenableWorker;
import androidx.work.WorkManager;
import androidx.work.WorkerParameters;
import com.google.errorprone.annotations.CanIgnoreReturnValue;
import com.timeflow.app.data.dao.AppUsageDao;
import com.timeflow.app.data.dao.CycleDao;
import com.timeflow.app.data.dao.EmotionRecordDao;
import com.timeflow.app.data.dao.GoalDao;
import com.timeflow.app.data.dao.GoalTemplateDao;
import com.timeflow.app.data.dao.HabitDao;
import com.timeflow.app.data.dao.KanbanBoardDao;
import com.timeflow.app.data.dao.KanbanColumnDao;
import com.timeflow.app.data.dao.MedicationRecordDao;
import com.timeflow.app.data.dao.ReflectionDao;
import com.timeflow.app.data.dao.TaskDao;
import com.timeflow.app.data.dao.TimeSessionDao;
import com.timeflow.app.data.dao.WishDao;
import com.timeflow.app.data.db.AppDatabase;
import com.timeflow.app.data.preferences.UserPreferencesManager;
import com.timeflow.app.data.repository.AiTaskRepositoryImpl;
import com.timeflow.app.data.repository.CycleRepository;
import com.timeflow.app.data.repository.DefaultTemplateInitializer;
import com.timeflow.app.data.repository.EmotionRecordRepository;
import com.timeflow.app.data.repository.GoalRepository;
import com.timeflow.app.data.repository.GoalTemplateRepositoryImpl;
import com.timeflow.app.data.repository.HabitRepositoryImpl;
import com.timeflow.app.data.repository.KanbanBoardRepositoryImpl;
import com.timeflow.app.data.repository.KanbanColumnRepositoryImpl;
import com.timeflow.app.data.repository.MedicationRepository;
import com.timeflow.app.data.repository.SharedFilterState;
import com.timeflow.app.data.repository.SharedPendingDeletionState;
import com.timeflow.app.data.repository.TaskRepository;
import com.timeflow.app.data.repository.TaskRepositoryImpl;
import com.timeflow.app.data.repository.TaskTimeRepository;
import com.timeflow.app.data.repository.TimeAnalyticsRepository;
import com.timeflow.app.data.repository.TimeAnalyticsRepositoryImpl;
import com.timeflow.app.data.repository.TimeSessionRepository;
import com.timeflow.app.data.repository.UserPreferenceRepositoryImpl;
import com.timeflow.app.data.repository.WishRepository;
import com.timeflow.app.data.service.SimpleRealDataStatisticsService;
import com.timeflow.app.di.AnalyticsModule_ProvideCrashReporterFactory;
import com.timeflow.app.di.AppInitializer;
import com.timeflow.app.di.CrashReporter;
import com.timeflow.app.di.DataStoreModule_ProvideMedicationDataStoreFactory;
import com.timeflow.app.di.DataStoreModule_ProvideNotificationDataStoreFactory;
import com.timeflow.app.di.DataStoreModule_ProvideThemeDataStoreFactory;
import com.timeflow.app.di.DatabaseModule_ProvideAppDatabaseFactory;
import com.timeflow.app.di.DatabaseModule_ProvideAppUsageDaoFactory;
import com.timeflow.app.di.DatabaseModule_ProvideCycleDaoFactory;
import com.timeflow.app.di.DatabaseModule_ProvideDatabaseBackupManagerFactory;
import com.timeflow.app.di.DatabaseModule_ProvideEmotionRecordDaoFactory;
import com.timeflow.app.di.DatabaseModule_ProvideEmotionRecordRepositoryFactory;
import com.timeflow.app.di.DatabaseModule_ProvideGoalDaoFactory;
import com.timeflow.app.di.DatabaseModule_ProvideGoalRepositoryFactory;
import com.timeflow.app.di.DatabaseModule_ProvideGoalTemplateDaoFactory;
import com.timeflow.app.di.DatabaseModule_ProvideHabitDaoFactory;
import com.timeflow.app.di.DatabaseModule_ProvideKanbanBoardDaoFactory;
import com.timeflow.app.di.DatabaseModule_ProvideKanbanColumnDaoFactory;
import com.timeflow.app.di.DatabaseModule_ProvideReflectionDaoFactory;
import com.timeflow.app.di.DatabaseModule_ProvideRoomDatabaseFactory;
import com.timeflow.app.di.DatabaseModule_ProvideSharedPreferencesFactory;
import com.timeflow.app.di.DatabaseModule_ProvideTaskDaoFactory;
import com.timeflow.app.di.DatabaseModule_ProvideTimeSessionDaoFactory;
import com.timeflow.app.di.MedicationModule_ProvideMedicationRecordDaoFactory;
import com.timeflow.app.di.MedicationModule_ProvideMedicationRepositoryFactory;
import com.timeflow.app.di.RepositoryModule_ProvideContextFactory;
import com.timeflow.app.di.RepositoryModule_ProvideCycleRepositoryFactory;
import com.timeflow.app.di.RepositoryModule_ProvideSharedFilterStateFactory;
import com.timeflow.app.di.RepositoryModule_ProvideSharedPendingDeletionStateFactory;
import com.timeflow.app.di.RepositoryModule_ProvideTaskRepositoryFactory;
import com.timeflow.app.di.StatisticsModule_ProvideSimpleRealDataStatisticsServiceFactory;
import com.timeflow.app.di.TaskTimeModule_ProvideTaskTimeRepositoryFactory;
import com.timeflow.app.di.TaskTimeModule_ProvideTaskTimeUseCaseFactory;
import com.timeflow.app.di.UtilsModule_ProvideRenderOptimizerFactory;
import com.timeflow.app.di.UtilsModule_ProvideSystemBarManagerFactory;
import com.timeflow.app.di.WishModule_ProvideWishDaoFactory;
import com.timeflow.app.di.WishModule_ProvideWishRepositoryFactory;
import com.timeflow.app.di.WorkManagerModule_ProvideWorkManagerFactory;
import com.timeflow.app.domain.usecase.TaskTimeUseCase;
import com.timeflow.app.domain.usecase.goal.QuickGoalCreationUseCase;
import com.timeflow.app.domain.usecase.goal.SmartTemplateUseCase;
import com.timeflow.app.receiver.BootCompletedReceiver;
import com.timeflow.app.receiver.BootCompletedReceiver_MembersInjector;
import com.timeflow.app.receiver.DailyReviewAlarmReceiver;
import com.timeflow.app.receiver.DailyReviewAlarmReceiver_MembersInjector;
import com.timeflow.app.receiver.FocusTimerActionReceiver;
import com.timeflow.app.receiver.HabitAlarmReceiver;
import com.timeflow.app.receiver.HabitAlarmReceiver_MembersInjector;
import com.timeflow.app.receiver.TaskAlarmReceiver;
import com.timeflow.app.receiver.TaskAlarmReceiver_MembersInjector;
import com.timeflow.app.receiver.TaskPersistentNotificationActionReceiver;
import com.timeflow.app.service.AiSuggestionScheduler;
import com.timeflow.app.service.AutoBackupService;
import com.timeflow.app.service.AutoBackupService_MembersInjector;
import com.timeflow.app.service.DailyReviewScheduler;
import com.timeflow.app.service.FocusTimerManager;
import com.timeflow.app.service.FocusTimerService;
import com.timeflow.app.service.FocusTimerService_MembersInjector;
import com.timeflow.app.service.MedicationReminderManager;
import com.timeflow.app.service.NotificationConfigManager;
import com.timeflow.app.service.NotificationTestService;
import com.timeflow.app.service.NotificationTestService_MembersInjector;
import com.timeflow.app.service.PaymentManager;
import com.timeflow.app.service.RecurrenceCalculator;
import com.timeflow.app.service.RecurringTaskManager;
import com.timeflow.app.service.TaskPersistentNotificationManager;
import com.timeflow.app.service.TaskPersistentNotificationService;
import com.timeflow.app.service.TaskPersistentNotificationService_MembersInjector;
import com.timeflow.app.service.TaskReminderScheduler;
import com.timeflow.app.service.TimeTrackingService;
import com.timeflow.app.service.TimeTrackingService_MembersInjector;
import com.timeflow.app.ui.language.LanguageSettingsViewModel;
import com.timeflow.app.ui.language.LanguageSettingsViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.ui.screen.ai.AIReviewViewModel;
import com.timeflow.app.ui.screen.ai.AIReviewViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.ui.screen.analytics.AnalyticsDataService;
import com.timeflow.app.ui.screen.analytics.AnalyticsInsightService;
import com.timeflow.app.ui.screen.analytics.AnalyticsViewModel;
import com.timeflow.app.ui.screen.analytics.AnalyticsViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.ui.screen.calendar.CalendarViewModel;
import com.timeflow.app.ui.screen.calendar.CalendarViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.ui.screen.discover.DiscoverViewModel;
import com.timeflow.app.ui.screen.discover.DiscoverViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.ui.screen.goal.GoalTemplateViewModel;
import com.timeflow.app.ui.screen.goal.GoalTemplateViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.ui.screen.goal.GoalViewModel;
import com.timeflow.app.ui.screen.goal.GoalViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.ui.screen.health.AddHabitViewModel;
import com.timeflow.app.ui.screen.health.AddHabitViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.ui.screen.health.ProfessionalMedicationViewModel;
import com.timeflow.app.ui.screen.health.ProfessionalMedicationViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.ui.screen.milestone.MilestoneViewModel;
import com.timeflow.app.ui.screen.milestone.MilestoneViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.ui.screen.profile.EmotionStatisticsViewModel;
import com.timeflow.app.ui.screen.profile.EmotionStatisticsViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.ui.screen.profile.ProfileViewModel;
import com.timeflow.app.ui.screen.profile.ProfileViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.ui.screen.reflection.ReflectionDetailViewModel;
import com.timeflow.app.ui.screen.reflection.ReflectionDetailViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.ui.screen.reflection.ReflectionViewModel;
import com.timeflow.app.ui.screen.reflection.ReflectionViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.ui.screen.reflection.data.ReflectionRepositoryImpl;
import com.timeflow.app.ui.screen.reflection.data.SearchSuggestionServiceImpl;
import com.timeflow.app.ui.screen.settings.NotificationSettingsViewModel;
import com.timeflow.app.ui.screen.settings.NotificationSettingsViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.ui.screen.settings.SettingsViewModel;
import com.timeflow.app.ui.screen.settings.SettingsViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.ui.screen.settings.SyncRepositoryImpl;
import com.timeflow.app.ui.screen.settings.SyncSettingsViewModel;
import com.timeflow.app.ui.screen.settings.SyncSettingsViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.ui.screen.task.TaskDetailViewModel;
import com.timeflow.app.ui.screen.task.TaskDetailViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.ui.screen.task.TaskViewModel;
import com.timeflow.app.ui.screen.task.TaskViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.ui.settings.PresetThemeViewModel;
import com.timeflow.app.ui.settings.PresetThemeViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.ui.settings.ThemeSettingsViewModel;
import com.timeflow.app.ui.settings.ThemeSettingsViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.ui.statistics.SimpleRealTimeStatisticsViewModel;
import com.timeflow.app.ui.statistics.SimpleRealTimeStatisticsViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.ui.statistics.TimeStatisticsViewModel;
import com.timeflow.app.ui.statistics.TimeStatisticsViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.ui.task.KanbanViewModel;
import com.timeflow.app.ui.task.KanbanViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.ui.task.TaskListViewModel;
import com.timeflow.app.ui.task.TaskListViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.ui.timetracking.TaskTimeStatisticsViewModel;
import com.timeflow.app.ui.timetracking.TaskTimeStatisticsViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.ui.timetracking.TimeTrackingViewModel;
import com.timeflow.app.ui.timetracking.TimeTrackingViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.ui.viewmodel.AiAssistantViewModel;
import com.timeflow.app.ui.viewmodel.AiAssistantViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.ui.viewmodel.AiConfigViewModel;
import com.timeflow.app.ui.viewmodel.AiConfigViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.ui.viewmodel.AiSettingsViewModel;
import com.timeflow.app.ui.viewmodel.AiSettingsViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.ui.viewmodel.GlobalTimerViewModel;
import com.timeflow.app.ui.viewmodel.GlobalTimerViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.ui.viewmodel.GoalCreationViewModel;
import com.timeflow.app.ui.viewmodel.GoalCreationViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.ui.viewmodel.HabitViewModel;
import com.timeflow.app.ui.viewmodel.HabitViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.ui.viewmodel.MenstrualCycleViewModel;
import com.timeflow.app.ui.viewmodel.MenstrualCycleViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.ui.viewmodel.TaskTimeViewModel;
import com.timeflow.app.ui.viewmodel.TaskTimeViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.ui.viewmodel.TimeFlowViewModel;
import com.timeflow.app.ui.viewmodel.TimeFlowViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.ui.viewmodel.WishListViewModel;
import com.timeflow.app.ui.viewmodel.WishListViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.ui.viewmodel.WishStatisticsViewModel;
import com.timeflow.app.ui.viewmodel.WishStatisticsViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.utils.AppExceptionHandler;
import com.timeflow.app.utils.DatabaseBackupManager;
import com.timeflow.app.utils.NotificationHelper;
import com.timeflow.app.utils.NotificationPermissionHelper;
import com.timeflow.app.utils.RenderOptimizer;
import com.timeflow.app.utils.SampleDataGenerator;
import com.timeflow.app.utils.SystemBarManager;
import com.timeflow.app.utils.TaskReminderUtils;
import com.timeflow.app.utils.TemplateCache;
import com.timeflow.app.utils.TimeFlowNotificationManager;
import com.timeflow.app.viewmodel.BackupRestoreViewModel;
import com.timeflow.app.viewmodel.BackupRestoreViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.viewmodel.BackupSettingsViewModel;
import com.timeflow.app.viewmodel.BackupSettingsViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.viewmodel.DataManagementViewModel;
import com.timeflow.app.viewmodel.DataManagementViewModel_HiltModules_KeyModule_ProvideFactory;
import com.timeflow.app.worker.AutoBackupWorker;
import com.timeflow.app.worker.AutoBackupWorker_AssistedFactory;
import com.timeflow.app.worker.RecurringTaskWorker;
import com.timeflow.app.worker.RecurringTaskWorker_AssistedFactory;
import dagger.hilt.android.ActivityRetainedLifecycle;
import dagger.hilt.android.ViewModelLifecycle;
import dagger.hilt.android.internal.builders.ActivityComponentBuilder;
import dagger.hilt.android.internal.builders.ActivityRetainedComponentBuilder;
import dagger.hilt.android.internal.builders.FragmentComponentBuilder;
import dagger.hilt.android.internal.builders.ServiceComponentBuilder;
import dagger.hilt.android.internal.builders.ViewComponentBuilder;
import dagger.hilt.android.internal.builders.ViewModelComponentBuilder;
import dagger.hilt.android.internal.builders.ViewWithFragmentComponentBuilder;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories_InternalFactoryFactory_Factory;
import dagger.hilt.android.internal.managers.ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory;
import dagger.hilt.android.internal.managers.SavedStateHandleHolder;
import dagger.hilt.android.internal.modules.ApplicationContextModule;
import dagger.hilt.android.internal.modules.ApplicationContextModule_ProvideApplicationFactory;
import dagger.hilt.android.internal.modules.ApplicationContextModule_ProvideContextFactory;
import dagger.internal.DaggerGenerated;
import dagger.internal.DelegateFactory;
import dagger.internal.DoubleCheck;
import dagger.internal.MapBuilder;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.SetBuilder;
import dagger.internal.SingleCheck;
import java.util.Collections;
import java.util.Map;
import java.util.Set;
import javax.annotation.processing.Generated;

@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DaggerTimeFlowApplication_HiltComponents_SingletonC {
  private DaggerTimeFlowApplication_HiltComponents_SingletonC() {
  }

  public static Builder builder() {
    return new Builder();
  }

  public static final class Builder {
    private ApplicationContextModule applicationContextModule;

    private Builder() {
    }

    public Builder applicationContextModule(ApplicationContextModule applicationContextModule) {
      this.applicationContextModule = Preconditions.checkNotNull(applicationContextModule);
      return this;
    }

    public TimeFlowApplication_HiltComponents.SingletonC build() {
      Preconditions.checkBuilderRequirement(applicationContextModule, ApplicationContextModule.class);
      return new SingletonCImpl(applicationContextModule);
    }
  }

  private static final class ActivityRetainedCBuilder implements TimeFlowApplication_HiltComponents.ActivityRetainedC.Builder {
    private final SingletonCImpl singletonCImpl;

    private SavedStateHandleHolder savedStateHandleHolder;

    private ActivityRetainedCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public ActivityRetainedCBuilder savedStateHandleHolder(
        SavedStateHandleHolder savedStateHandleHolder) {
      this.savedStateHandleHolder = Preconditions.checkNotNull(savedStateHandleHolder);
      return this;
    }

    @Override
    public TimeFlowApplication_HiltComponents.ActivityRetainedC build() {
      Preconditions.checkBuilderRequirement(savedStateHandleHolder, SavedStateHandleHolder.class);
      return new ActivityRetainedCImpl(singletonCImpl, savedStateHandleHolder);
    }
  }

  private static final class ActivityCBuilder implements TimeFlowApplication_HiltComponents.ActivityC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private Activity activity;

    private ActivityCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ActivityCBuilder activity(Activity activity) {
      this.activity = Preconditions.checkNotNull(activity);
      return this;
    }

    @Override
    public TimeFlowApplication_HiltComponents.ActivityC build() {
      Preconditions.checkBuilderRequirement(activity, Activity.class);
      return new ActivityCImpl(singletonCImpl, activityRetainedCImpl, activity);
    }
  }

  private static final class FragmentCBuilder implements TimeFlowApplication_HiltComponents.FragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private Fragment fragment;

    private FragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public FragmentCBuilder fragment(Fragment fragment) {
      this.fragment = Preconditions.checkNotNull(fragment);
      return this;
    }

    @Override
    public TimeFlowApplication_HiltComponents.FragmentC build() {
      Preconditions.checkBuilderRequirement(fragment, Fragment.class);
      return new FragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragment);
    }
  }

  private static final class ViewWithFragmentCBuilder implements TimeFlowApplication_HiltComponents.ViewWithFragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private View view;

    private ViewWithFragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;
    }

    @Override
    public ViewWithFragmentCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public TimeFlowApplication_HiltComponents.ViewWithFragmentC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewWithFragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl, view);
    }
  }

  private static final class ViewCBuilder implements TimeFlowApplication_HiltComponents.ViewC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private View view;

    private ViewCBuilder(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public ViewCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public TimeFlowApplication_HiltComponents.ViewC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, view);
    }
  }

  private static final class ViewModelCBuilder implements TimeFlowApplication_HiltComponents.ViewModelC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private SavedStateHandle savedStateHandle;

    private ViewModelLifecycle viewModelLifecycle;

    private ViewModelCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ViewModelCBuilder savedStateHandle(SavedStateHandle handle) {
      this.savedStateHandle = Preconditions.checkNotNull(handle);
      return this;
    }

    @Override
    public ViewModelCBuilder viewModelLifecycle(ViewModelLifecycle viewModelLifecycle) {
      this.viewModelLifecycle = Preconditions.checkNotNull(viewModelLifecycle);
      return this;
    }

    @Override
    public TimeFlowApplication_HiltComponents.ViewModelC build() {
      Preconditions.checkBuilderRequirement(savedStateHandle, SavedStateHandle.class);
      Preconditions.checkBuilderRequirement(viewModelLifecycle, ViewModelLifecycle.class);
      return new ViewModelCImpl(singletonCImpl, activityRetainedCImpl, savedStateHandle, viewModelLifecycle);
    }
  }

  private static final class ServiceCBuilder implements TimeFlowApplication_HiltComponents.ServiceC.Builder {
    private final SingletonCImpl singletonCImpl;

    private Service service;

    private ServiceCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public ServiceCBuilder service(Service service) {
      this.service = Preconditions.checkNotNull(service);
      return this;
    }

    @Override
    public TimeFlowApplication_HiltComponents.ServiceC build() {
      Preconditions.checkBuilderRequirement(service, Service.class);
      return new ServiceCImpl(singletonCImpl, service);
    }
  }

  private static final class ViewWithFragmentCImpl extends TimeFlowApplication_HiltComponents.ViewWithFragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private final ViewWithFragmentCImpl viewWithFragmentCImpl = this;

    private ViewWithFragmentCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;


    }
  }

  private static final class FragmentCImpl extends TimeFlowApplication_HiltComponents.FragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl = this;

    private FragmentCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        Fragment fragmentParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return activityCImpl.getHiltInternalFactoryFactory();
    }

    @Override
    public ViewWithFragmentComponentBuilder viewWithFragmentComponentBuilder() {
      return new ViewWithFragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl);
    }
  }

  private static final class ViewCImpl extends TimeFlowApplication_HiltComponents.ViewC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final ViewCImpl viewCImpl = this;

    private ViewCImpl(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }
  }

  private static final class ActivityCImpl extends TimeFlowApplication_HiltComponents.ActivityC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl = this;

    private ActivityCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, Activity activityParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;


    }

    @Override
    public void injectMainActivity(MainActivity mainActivity) {
      injectMainActivity2(mainActivity);
    }

    @Override
    public void injectMainActivity(com.timeflow.app.ui.MainActivity mainActivity) {
      injectMainActivity3(mainActivity);
    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return DefaultViewModelFactories_InternalFactoryFactory_Factory.newInstance(getViewModelKeys(), new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl));
    }

    @Override
    public Set<String> getViewModelKeys() {
      return SetBuilder.<String>newSetBuilder(42).add(AIReviewViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(AddHabitViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(AiAssistantViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(AiConfigViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(AiSettingsViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(AnalyticsViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(BackupRestoreViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(BackupSettingsViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(CalendarViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(DataManagementViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(DiscoverViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(EmotionStatisticsViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(GlobalTimerViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(GoalCreationViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(GoalTemplateViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(GoalViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(com.timeflow.app.ui.viewmodel.GoalViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(HabitViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(KanbanViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(LanguageSettingsViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(MenstrualCycleViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(MilestoneViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(NotificationSettingsViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(PresetThemeViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(ProfessionalMedicationViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(ProfileViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(ReflectionDetailViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(ReflectionViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(SettingsViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(SimpleRealTimeStatisticsViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(SyncSettingsViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(TaskDetailViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(TaskListViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(TaskTimeStatisticsViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(TaskTimeViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(TaskViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(ThemeSettingsViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(TimeFlowViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(TimeStatisticsViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(TimeTrackingViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(WishListViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(WishStatisticsViewModel_HiltModules_KeyModule_ProvideFactory.provide()).build();
    }

    @Override
    public ViewModelComponentBuilder getViewModelComponentBuilder() {
      return new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public FragmentComponentBuilder fragmentComponentBuilder() {
      return new FragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }

    @Override
    public ViewComponentBuilder viewComponentBuilder() {
      return new ViewCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }

    @CanIgnoreReturnValue
    private MainActivity injectMainActivity2(MainActivity instance) {
      MainActivity_MembersInjector.injectThemeDataStore(instance, singletonCImpl.provideThemeDataStoreProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private com.timeflow.app.ui.MainActivity injectMainActivity3(
        com.timeflow.app.ui.MainActivity instance) {
      com.timeflow.app.ui.MainActivity_MembersInjector.injectAppDatabase(instance, singletonCImpl.provideAppDatabaseProvider.get());
      com.timeflow.app.ui.MainActivity_MembersInjector.injectSampleDataGenerator(instance, singletonCImpl.sampleDataGeneratorProvider.get());
      com.timeflow.app.ui.MainActivity_MembersInjector.injectRenderOptimizer(instance, singletonCImpl.provideRenderOptimizerProvider.get());
      com.timeflow.app.ui.MainActivity_MembersInjector.injectSystemBarManager(instance, singletonCImpl.provideSystemBarManagerProvider.get());
      com.timeflow.app.ui.MainActivity_MembersInjector.injectThemeDataStore(instance, singletonCImpl.provideThemeDataStoreProvider.get());
      com.timeflow.app.ui.MainActivity_MembersInjector.injectNotificationPermissionHelper(instance, singletonCImpl.notificationPermissionHelperProvider.get());
      return instance;
    }
  }

  private static final class ViewModelCImpl extends TimeFlowApplication_HiltComponents.ViewModelC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ViewModelCImpl viewModelCImpl = this;

    private Provider<AIReviewViewModel> aIReviewViewModelProvider;

    private Provider<AddHabitViewModel> addHabitViewModelProvider;

    private Provider<AiAssistantViewModel> aiAssistantViewModelProvider;

    private Provider<AiConfigViewModel> aiConfigViewModelProvider;

    private Provider<AiSettingsViewModel> aiSettingsViewModelProvider;

    private Provider<AnalyticsViewModel> analyticsViewModelProvider;

    private Provider<BackupRestoreViewModel> backupRestoreViewModelProvider;

    private Provider<BackupSettingsViewModel> backupSettingsViewModelProvider;

    private Provider<CalendarViewModel> calendarViewModelProvider;

    private Provider<DataManagementViewModel> dataManagementViewModelProvider;

    private Provider<DiscoverViewModel> discoverViewModelProvider;

    private Provider<EmotionStatisticsViewModel> emotionStatisticsViewModelProvider;

    private Provider<GlobalTimerViewModel> globalTimerViewModelProvider;

    private Provider<GoalCreationViewModel> goalCreationViewModelProvider;

    private Provider<GoalTemplateViewModel> goalTemplateViewModelProvider;

    private Provider<GoalViewModel> goalViewModelProvider;

    private Provider<com.timeflow.app.ui.viewmodel.GoalViewModel> goalViewModelProvider2;

    private Provider<HabitViewModel> habitViewModelProvider;

    private Provider<KanbanViewModel> kanbanViewModelProvider;

    private Provider<LanguageSettingsViewModel> languageSettingsViewModelProvider;

    private Provider<MenstrualCycleViewModel> menstrualCycleViewModelProvider;

    private Provider<MilestoneViewModel> milestoneViewModelProvider;

    private Provider<NotificationSettingsViewModel> notificationSettingsViewModelProvider;

    private Provider<PresetThemeViewModel> presetThemeViewModelProvider;

    private Provider<ProfessionalMedicationViewModel> professionalMedicationViewModelProvider;

    private Provider<ProfileViewModel> profileViewModelProvider;

    private Provider<ReflectionDetailViewModel> reflectionDetailViewModelProvider;

    private Provider<ReflectionViewModel> reflectionViewModelProvider;

    private Provider<SettingsViewModel> settingsViewModelProvider;

    private Provider<SimpleRealTimeStatisticsViewModel> simpleRealTimeStatisticsViewModelProvider;

    private Provider<SyncSettingsViewModel> syncSettingsViewModelProvider;

    private Provider<TaskDetailViewModel> taskDetailViewModelProvider;

    private Provider<TaskListViewModel> taskListViewModelProvider;

    private Provider<TaskTimeStatisticsViewModel> taskTimeStatisticsViewModelProvider;

    private Provider<TaskTimeViewModel> taskTimeViewModelProvider;

    private Provider<TaskViewModel> taskViewModelProvider;

    private Provider<ThemeSettingsViewModel> themeSettingsViewModelProvider;

    private Provider<TimeFlowViewModel> timeFlowViewModelProvider;

    private Provider<TimeStatisticsViewModel> timeStatisticsViewModelProvider;

    private Provider<TimeTrackingViewModel> timeTrackingViewModelProvider;

    private Provider<WishListViewModel> wishListViewModelProvider;

    private Provider<WishStatisticsViewModel> wishStatisticsViewModelProvider;

    private ViewModelCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, SavedStateHandle savedStateHandleParam,
        ViewModelLifecycle viewModelLifecycleParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;

      initialize(savedStateHandleParam, viewModelLifecycleParam);

    }

    private SmartTemplateUseCase smartTemplateUseCase() {
      return new SmartTemplateUseCase(singletonCImpl.goalTemplateRepositoryImplProvider.get(), singletonCImpl.userPreferenceRepositoryImplProvider.get());
    }

    private QuickGoalCreationUseCase quickGoalCreationUseCase() {
      return new QuickGoalCreationUseCase(singletonCImpl.provideGoalRepositoryProvider.get(), singletonCImpl.userPreferenceRepositoryImplProvider.get());
    }

    @SuppressWarnings("unchecked")
    private void initialize(final SavedStateHandle savedStateHandleParam,
        final ViewModelLifecycle viewModelLifecycleParam) {
      this.aIReviewViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 0);
      this.addHabitViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 1);
      this.aiAssistantViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 2);
      this.aiConfigViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 3);
      this.aiSettingsViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 4);
      this.analyticsViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 5);
      this.backupRestoreViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 6);
      this.backupSettingsViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 7);
      this.calendarViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 8);
      this.dataManagementViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 9);
      this.discoverViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 10);
      this.emotionStatisticsViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 11);
      this.globalTimerViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 12);
      this.goalCreationViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 13);
      this.goalTemplateViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 14);
      this.goalViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 15);
      this.goalViewModelProvider2 = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 16);
      this.habitViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 17);
      this.kanbanViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 18);
      this.languageSettingsViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 19);
      this.menstrualCycleViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 20);
      this.milestoneViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 21);
      this.notificationSettingsViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 22);
      this.presetThemeViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 23);
      this.professionalMedicationViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 24);
      this.profileViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 25);
      this.reflectionDetailViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 26);
      this.reflectionViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 27);
      this.settingsViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 28);
      this.simpleRealTimeStatisticsViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 29);
      this.syncSettingsViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 30);
      this.taskDetailViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 31);
      this.taskListViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 32);
      this.taskTimeStatisticsViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 33);
      this.taskTimeViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 34);
      this.taskViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 35);
      this.themeSettingsViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 36);
      this.timeFlowViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 37);
      this.timeStatisticsViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 38);
      this.timeTrackingViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 39);
      this.wishListViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 40);
      this.wishStatisticsViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 41);
    }

    @Override
    public Map<String, javax.inject.Provider<ViewModel>> getHiltViewModelMap() {
      return MapBuilder.<String, javax.inject.Provider<ViewModel>>newMapBuilder(42).put("com.timeflow.app.ui.screen.ai.AIReviewViewModel", ((Provider) aIReviewViewModelProvider)).put("com.timeflow.app.ui.screen.health.AddHabitViewModel", ((Provider) addHabitViewModelProvider)).put("com.timeflow.app.ui.viewmodel.AiAssistantViewModel", ((Provider) aiAssistantViewModelProvider)).put("com.timeflow.app.ui.viewmodel.AiConfigViewModel", ((Provider) aiConfigViewModelProvider)).put("com.timeflow.app.ui.viewmodel.AiSettingsViewModel", ((Provider) aiSettingsViewModelProvider)).put("com.timeflow.app.ui.screen.analytics.AnalyticsViewModel", ((Provider) analyticsViewModelProvider)).put("com.timeflow.app.viewmodel.BackupRestoreViewModel", ((Provider) backupRestoreViewModelProvider)).put("com.timeflow.app.viewmodel.BackupSettingsViewModel", ((Provider) backupSettingsViewModelProvider)).put("com.timeflow.app.ui.screen.calendar.CalendarViewModel", ((Provider) calendarViewModelProvider)).put("com.timeflow.app.viewmodel.DataManagementViewModel", ((Provider) dataManagementViewModelProvider)).put("com.timeflow.app.ui.screen.discover.DiscoverViewModel", ((Provider) discoverViewModelProvider)).put("com.timeflow.app.ui.screen.profile.EmotionStatisticsViewModel", ((Provider) emotionStatisticsViewModelProvider)).put("com.timeflow.app.ui.viewmodel.GlobalTimerViewModel", ((Provider) globalTimerViewModelProvider)).put("com.timeflow.app.ui.viewmodel.GoalCreationViewModel", ((Provider) goalCreationViewModelProvider)).put("com.timeflow.app.ui.screen.goal.GoalTemplateViewModel", ((Provider) goalTemplateViewModelProvider)).put("com.timeflow.app.ui.screen.goal.GoalViewModel", ((Provider) goalViewModelProvider)).put("com.timeflow.app.ui.viewmodel.GoalViewModel", ((Provider) goalViewModelProvider2)).put("com.timeflow.app.ui.viewmodel.HabitViewModel", ((Provider) habitViewModelProvider)).put("com.timeflow.app.ui.task.KanbanViewModel", ((Provider) kanbanViewModelProvider)).put("com.timeflow.app.ui.language.LanguageSettingsViewModel", ((Provider) languageSettingsViewModelProvider)).put("com.timeflow.app.ui.viewmodel.MenstrualCycleViewModel", ((Provider) menstrualCycleViewModelProvider)).put("com.timeflow.app.ui.screen.milestone.MilestoneViewModel", ((Provider) milestoneViewModelProvider)).put("com.timeflow.app.ui.screen.settings.NotificationSettingsViewModel", ((Provider) notificationSettingsViewModelProvider)).put("com.timeflow.app.ui.settings.PresetThemeViewModel", ((Provider) presetThemeViewModelProvider)).put("com.timeflow.app.ui.screen.health.ProfessionalMedicationViewModel", ((Provider) professionalMedicationViewModelProvider)).put("com.timeflow.app.ui.screen.profile.ProfileViewModel", ((Provider) profileViewModelProvider)).put("com.timeflow.app.ui.screen.reflection.ReflectionDetailViewModel", ((Provider) reflectionDetailViewModelProvider)).put("com.timeflow.app.ui.screen.reflection.ReflectionViewModel", ((Provider) reflectionViewModelProvider)).put("com.timeflow.app.ui.screen.settings.SettingsViewModel", ((Provider) settingsViewModelProvider)).put("com.timeflow.app.ui.statistics.SimpleRealTimeStatisticsViewModel", ((Provider) simpleRealTimeStatisticsViewModelProvider)).put("com.timeflow.app.ui.screen.settings.SyncSettingsViewModel", ((Provider) syncSettingsViewModelProvider)).put("com.timeflow.app.ui.screen.task.TaskDetailViewModel", ((Provider) taskDetailViewModelProvider)).put("com.timeflow.app.ui.task.TaskListViewModel", ((Provider) taskListViewModelProvider)).put("com.timeflow.app.ui.timetracking.TaskTimeStatisticsViewModel", ((Provider) taskTimeStatisticsViewModelProvider)).put("com.timeflow.app.ui.viewmodel.TaskTimeViewModel", ((Provider) taskTimeViewModelProvider)).put("com.timeflow.app.ui.screen.task.TaskViewModel", ((Provider) taskViewModelProvider)).put("com.timeflow.app.ui.settings.ThemeSettingsViewModel", ((Provider) themeSettingsViewModelProvider)).put("com.timeflow.app.ui.viewmodel.TimeFlowViewModel", ((Provider) timeFlowViewModelProvider)).put("com.timeflow.app.ui.statistics.TimeStatisticsViewModel", ((Provider) timeStatisticsViewModelProvider)).put("com.timeflow.app.ui.timetracking.TimeTrackingViewModel", ((Provider) timeTrackingViewModelProvider)).put("com.timeflow.app.ui.viewmodel.WishListViewModel", ((Provider) wishListViewModelProvider)).put("com.timeflow.app.ui.viewmodel.WishStatisticsViewModel", ((Provider) wishStatisticsViewModelProvider)).build();
    }

    @Override
    public Map<String, Object> getHiltViewModelAssistedMap() {
      return Collections.<String, Object>emptyMap();
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final ActivityRetainedCImpl activityRetainedCImpl;

      private final ViewModelCImpl viewModelCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
          ViewModelCImpl viewModelCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.activityRetainedCImpl = activityRetainedCImpl;
        this.viewModelCImpl = viewModelCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.timeflow.app.ui.screen.ai.AIReviewViewModel 
          return (T) new AIReviewViewModel(singletonCImpl.provideTaskRepositoryProvider.get(), singletonCImpl.bindTimeAnalyticsRepositoryProvider.get(), singletonCImpl.timeSessionRepositoryProvider.get(), singletonCImpl.provideGoalRepositoryProvider.get(), singletonCImpl.aiTaskRepositoryImplProvider.get(), ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 1: // com.timeflow.app.ui.screen.health.AddHabitViewModel 
          return (T) new AddHabitViewModel(singletonCImpl.habitRepositoryImplProvider.get(), singletonCImpl.provideGoalRepositoryProvider.get());

          case 2: // com.timeflow.app.ui.viewmodel.AiAssistantViewModel 
          return (T) new AiAssistantViewModel(singletonCImpl.aiTaskRepositoryImplProvider.get(), singletonCImpl.provideTaskRepositoryProvider.get(), singletonCImpl.userPreferenceRepositoryImplProvider.get());

          case 3: // com.timeflow.app.ui.viewmodel.AiConfigViewModel 
          return (T) new AiConfigViewModel();

          case 4: // com.timeflow.app.ui.viewmodel.AiSettingsViewModel 
          return (T) new AiSettingsViewModel();

          case 5: // com.timeflow.app.ui.screen.analytics.AnalyticsViewModel 
          return (T) new AnalyticsViewModel(singletonCImpl.analyticsDataServiceProvider.get(), singletonCImpl.bindTimeAnalyticsRepositoryProvider.get(), singletonCImpl.provideTaskRepositoryProvider.get(), singletonCImpl.analyticsInsightServiceProvider.get());

          case 6: // com.timeflow.app.viewmodel.BackupRestoreViewModel 
          return (T) new BackupRestoreViewModel(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 7: // com.timeflow.app.viewmodel.BackupSettingsViewModel 
          return (T) new BackupSettingsViewModel(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 8: // com.timeflow.app.ui.screen.calendar.CalendarViewModel 
          return (T) new CalendarViewModel(singletonCImpl.provideTaskRepositoryProvider.get(), singletonCImpl.provideSharedPendingDeletionStateProvider.get(), singletonCImpl.provideTaskTimeUseCaseProvider.get(), singletonCImpl.timeSessionRepositoryProvider.get());

          case 9: // com.timeflow.app.viewmodel.DataManagementViewModel 
          return (T) new DataManagementViewModel(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.provideDatabaseBackupManagerProvider.get(), singletonCImpl.provideAppDatabaseProvider.get());

          case 10: // com.timeflow.app.ui.screen.discover.DiscoverViewModel 
          return (T) new DiscoverViewModel(singletonCImpl.provideTaskRepositoryProvider.get());

          case 11: // com.timeflow.app.ui.screen.profile.EmotionStatisticsViewModel 
          return (T) new EmotionStatisticsViewModel(singletonCImpl.reflectionRepositoryImplProvider.get());

          case 12: // com.timeflow.app.ui.viewmodel.GlobalTimerViewModel 
          return (T) new GlobalTimerViewModel(singletonCImpl.provideSharedPreferencesProvider.get());

          case 13: // com.timeflow.app.ui.viewmodel.GoalCreationViewModel 
          return (T) new GoalCreationViewModel(viewModelCImpl.smartTemplateUseCase(), viewModelCImpl.quickGoalCreationUseCase());

          case 14: // com.timeflow.app.ui.screen.goal.GoalTemplateViewModel 
          return (T) new GoalTemplateViewModel(singletonCImpl.goalTemplateRepositoryImplProvider.get(), singletonCImpl.provideGoalRepositoryProvider.get(), viewModelCImpl.smartTemplateUseCase());

          case 15: // com.timeflow.app.ui.screen.goal.GoalViewModel 
          return (T) new GoalViewModel(ApplicationContextModule_ProvideApplicationFactory.provideApplication(singletonCImpl.applicationContextModule), singletonCImpl.provideGoalRepositoryProvider.get(), singletonCImpl.aiTaskRepositoryImplProvider.get());

          case 16: // com.timeflow.app.ui.viewmodel.GoalViewModel 
          return (T) new com.timeflow.app.ui.viewmodel.GoalViewModel(singletonCImpl.provideGoalRepositoryProvider.get());

          case 17: // com.timeflow.app.ui.viewmodel.HabitViewModel 
          return (T) new HabitViewModel(singletonCImpl.habitRepositoryImplProvider.get(), ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 18: // com.timeflow.app.ui.task.KanbanViewModel 
          return (T) new KanbanViewModel(singletonCImpl.provideTaskRepositoryProvider.get());

          case 19: // com.timeflow.app.ui.language.LanguageSettingsViewModel 
          return (T) new LanguageSettingsViewModel();

          case 20: // com.timeflow.app.ui.viewmodel.MenstrualCycleViewModel 
          return (T) new MenstrualCycleViewModel(singletonCImpl.provideCycleRepositoryProvider.get());

          case 21: // com.timeflow.app.ui.screen.milestone.MilestoneViewModel 
          return (T) new MilestoneViewModel();

          case 22: // com.timeflow.app.ui.screen.settings.NotificationSettingsViewModel 
          return (T) new NotificationSettingsViewModel(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.timeFlowNotificationManagerProvider.get(), singletonCImpl.dailyReviewSchedulerProvider.get(), singletonCImpl.notificationConfigManagerProvider.get(), singletonCImpl.taskPersistentNotificationManagerProvider.get(), singletonCImpl.provideNotificationDataStoreProvider.get());

          case 23: // com.timeflow.app.ui.settings.PresetThemeViewModel 
          return (T) new PresetThemeViewModel();

          case 24: // com.timeflow.app.ui.screen.health.ProfessionalMedicationViewModel 
          return (T) new ProfessionalMedicationViewModel(singletonCImpl.medicationReminderManagerProvider.get(), ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.provideMedicationDataStoreProvider.get(), singletonCImpl.provideMedicationRepositoryProvider.get());

          case 25: // com.timeflow.app.ui.screen.profile.ProfileViewModel 
          return (T) new ProfileViewModel(singletonCImpl.provideEmotionRecordRepositoryProvider.get());

          case 26: // com.timeflow.app.ui.screen.reflection.ReflectionDetailViewModel 
          return (T) new ReflectionDetailViewModel(singletonCImpl.reflectionRepositoryImplProvider.get());

          case 27: // com.timeflow.app.ui.screen.reflection.ReflectionViewModel 
          return (T) new ReflectionViewModel(singletonCImpl.reflectionRepositoryImplProvider.get(), singletonCImpl.searchSuggestionServiceImplProvider.get());

          case 28: // com.timeflow.app.ui.screen.settings.SettingsViewModel 
          return (T) new SettingsViewModel(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.paymentManagerProvider.get());

          case 29: // com.timeflow.app.ui.statistics.SimpleRealTimeStatisticsViewModel 
          return (T) new SimpleRealTimeStatisticsViewModel(singletonCImpl.provideSimpleRealDataStatisticsServiceProvider.get());

          case 30: // com.timeflow.app.ui.screen.settings.SyncSettingsViewModel 
          return (T) new SyncSettingsViewModel(singletonCImpl.syncRepositoryImplProvider.get(), singletonCImpl.provideTaskRepositoryProvider.get(), singletonCImpl.provideSharedPreferencesProvider.get());

          case 31: // com.timeflow.app.ui.screen.task.TaskDetailViewModel 
          return (T) new TaskDetailViewModel(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.provideTaskRepositoryProvider.get());

          case 32: // com.timeflow.app.ui.task.TaskListViewModel 
          return (T) new TaskListViewModel(singletonCImpl.provideTaskRepositoryProvider.get(), singletonCImpl.provideGoalRepositoryProvider.get(), singletonCImpl.userPreferenceRepositoryImplProvider.get(), singletonCImpl.provideWorkManagerProvider.get(), ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.provideSharedFilterStateProvider.get(), singletonCImpl.reflectionRepositoryImplProvider.get(), singletonCImpl.provideSharedPendingDeletionStateProvider.get());

          case 33: // com.timeflow.app.ui.timetracking.TaskTimeStatisticsViewModel 
          return (T) new TaskTimeStatisticsViewModel(singletonCImpl.timeSessionRepositoryProvider.get());

          case 34: // com.timeflow.app.ui.viewmodel.TaskTimeViewModel 
          return (T) new TaskTimeViewModel(singletonCImpl.provideTaskTimeUseCaseProvider.get());

          case 35: // com.timeflow.app.ui.screen.task.TaskViewModel 
          return (T) new TaskViewModel(singletonCImpl.provideTaskRepositoryProvider.get(), singletonCImpl.reflectionRepositoryImplProvider.get());

          case 36: // com.timeflow.app.ui.settings.ThemeSettingsViewModel 
          return (T) new ThemeSettingsViewModel(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.provideThemeDataStoreProvider.get());

          case 37: // com.timeflow.app.ui.viewmodel.TimeFlowViewModel 
          return (T) new TimeFlowViewModel();

          case 38: // com.timeflow.app.ui.statistics.TimeStatisticsViewModel 
          return (T) new TimeStatisticsViewModel();

          case 39: // com.timeflow.app.ui.timetracking.TimeTrackingViewModel 
          return (T) new TimeTrackingViewModel(singletonCImpl.bindTimeAnalyticsRepositoryProvider.get(), ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.provideTaskRepositoryProvider.get(), singletonCImpl.timeSessionRepositoryProvider.get(), singletonCImpl.provideSharedPreferencesProvider.get(), singletonCImpl.focusTimerManagerProvider.get());

          case 40: // com.timeflow.app.ui.viewmodel.WishListViewModel 
          return (T) new WishListViewModel(singletonCImpl.provideWishRepositoryProvider.get(), singletonCImpl.provideGoalRepositoryProvider.get());

          case 41: // com.timeflow.app.ui.viewmodel.WishStatisticsViewModel 
          return (T) new WishStatisticsViewModel(singletonCImpl.provideWishRepositoryProvider.get());

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ActivityRetainedCImpl extends TimeFlowApplication_HiltComponents.ActivityRetainedC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl = this;

    private Provider<ActivityRetainedLifecycle> provideActivityRetainedLifecycleProvider;

    private ActivityRetainedCImpl(SingletonCImpl singletonCImpl,
        SavedStateHandleHolder savedStateHandleHolderParam) {
      this.singletonCImpl = singletonCImpl;

      initialize(savedStateHandleHolderParam);

    }

    @SuppressWarnings("unchecked")
    private void initialize(final SavedStateHandleHolder savedStateHandleHolderParam) {
      this.provideActivityRetainedLifecycleProvider = DoubleCheck.provider(new SwitchingProvider<ActivityRetainedLifecycle>(singletonCImpl, activityRetainedCImpl, 0));
    }

    @Override
    public ActivityComponentBuilder activityComponentBuilder() {
      return new ActivityCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public ActivityRetainedLifecycle getActivityRetainedLifecycle() {
      return provideActivityRetainedLifecycleProvider.get();
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final ActivityRetainedCImpl activityRetainedCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
          int id) {
        this.singletonCImpl = singletonCImpl;
        this.activityRetainedCImpl = activityRetainedCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // dagger.hilt.android.ActivityRetainedLifecycle 
          return (T) ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory.provideActivityRetainedLifecycle();

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ServiceCImpl extends TimeFlowApplication_HiltComponents.ServiceC {
    private final SingletonCImpl singletonCImpl;

    private final ServiceCImpl serviceCImpl = this;

    private ServiceCImpl(SingletonCImpl singletonCImpl, Service serviceParam) {
      this.singletonCImpl = singletonCImpl;


    }

    @Override
    public void injectAutoBackupService(AutoBackupService autoBackupService) {
      injectAutoBackupService2(autoBackupService);
    }

    @Override
    public void injectFocusTimerService(FocusTimerService focusTimerService) {
      injectFocusTimerService2(focusTimerService);
    }

    @Override
    public void injectNotificationTestService(NotificationTestService notificationTestService) {
      injectNotificationTestService2(notificationTestService);
    }

    @Override
    public void injectTaskPersistentNotificationService(
        TaskPersistentNotificationService taskPersistentNotificationService) {
      injectTaskPersistentNotificationService2(taskPersistentNotificationService);
    }

    @Override
    public void injectTimeTrackingService(TimeTrackingService timeTrackingService) {
      injectTimeTrackingService2(timeTrackingService);
    }

    @CanIgnoreReturnValue
    private AutoBackupService injectAutoBackupService2(AutoBackupService instance) {
      AutoBackupService_MembersInjector.injectBackupManager(instance, singletonCImpl.provideDatabaseBackupManagerProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private FocusTimerService injectFocusTimerService2(FocusTimerService instance) {
      FocusTimerService_MembersInjector.injectSharedPreferences(instance, singletonCImpl.provideSharedPreferencesProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private NotificationTestService injectNotificationTestService2(
        NotificationTestService instance) {
      NotificationTestService_MembersInjector.injectNotificationManager(instance, singletonCImpl.timeFlowNotificationManagerProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private TaskPersistentNotificationService injectTaskPersistentNotificationService2(
        TaskPersistentNotificationService instance) {
      TaskPersistentNotificationService_MembersInjector.injectTaskRepository(instance, singletonCImpl.provideTaskRepositoryProvider.get());
      TaskPersistentNotificationService_MembersInjector.injectNotificationHelper(instance, singletonCImpl.notificationHelperProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private TimeTrackingService injectTimeTrackingService2(TimeTrackingService instance) {
      TimeTrackingService_MembersInjector.injectNotificationHelper(instance, singletonCImpl.notificationHelperProvider.get());
      return instance;
    }
  }

  private static final class SingletonCImpl extends TimeFlowApplication_HiltComponents.SingletonC {
    private final ApplicationContextModule applicationContextModule;

    private final SingletonCImpl singletonCImpl = this;

    private Provider<CrashReporter> provideCrashReporterProvider;

    private Provider<AppExceptionHandler> appExceptionHandlerProvider;

    private Provider<DatabaseBackupManager> provideDatabaseBackupManagerProvider;

    private Provider<AppDatabase> provideAppDatabaseProvider;

    private Provider<KanbanBoardDao> provideKanbanBoardDaoProvider;

    private Provider<KanbanBoardRepositoryImpl> kanbanBoardRepositoryImplProvider;

    private Provider<KanbanColumnDao> provideKanbanColumnDaoProvider;

    private Provider<KanbanColumnRepositoryImpl> kanbanColumnRepositoryImplProvider;

    private Provider<TaskDao> provideTaskDaoProvider;

    private Provider<TaskRepository> provideTaskRepositoryProvider;

    private Provider<NotificationHelper> notificationHelperProvider;

    private Provider<TimeFlowNotificationManager> timeFlowNotificationManagerProvider;

    private Provider<TaskReminderScheduler> taskReminderSchedulerProvider;

    private Provider<Context> provideContextProvider;

    private Provider<NotificationPermissionHelper> notificationPermissionHelperProvider;

    private Provider<TaskReminderUtils> taskReminderUtilsProvider;

    private Provider<TaskRepositoryImpl> taskRepositoryImplProvider;

    private Provider<SampleDataGenerator> sampleDataGeneratorProvider;

    private Provider<GoalTemplateDao> provideGoalTemplateDaoProvider;

    private Provider<DefaultTemplateInitializer> defaultTemplateInitializerProvider;

    private Provider<AppInitializer> appInitializerProvider;

    private Provider<RoomDatabase> provideRoomDatabaseProvider;

    private Provider<ReflectionDao> provideReflectionDaoProvider;

    private Provider<ReflectionRepositoryImpl> reflectionRepositoryImplProvider;

    private Provider<AiSuggestionScheduler> aiSuggestionSchedulerProvider;

    private Provider<TaskPersistentNotificationManager> taskPersistentNotificationManagerProvider;

    private Provider<AutoBackupWorker_AssistedFactory> autoBackupWorker_AssistedFactoryProvider;

    private Provider<RecurrenceCalculator> recurrenceCalculatorProvider;

    private Provider<RecurringTaskManager> recurringTaskManagerProvider;

    private Provider<RecurringTaskWorker_AssistedFactory> recurringTaskWorker_AssistedFactoryProvider;

    private Provider<HabitDao> provideHabitDaoProvider;

    private Provider<GoalDao> provideGoalDaoProvider;

    private Provider<HabitRepositoryImpl> habitRepositoryImplProvider;

    private Provider<DailyReviewScheduler> dailyReviewSchedulerProvider;

    private Provider<TimeSessionDao> provideTimeSessionDaoProvider;

    private Provider<TimeSessionRepository> timeSessionRepositoryProvider;

    private Provider<GoalRepository> provideGoalRepositoryProvider;

    private Provider<UserPreferencesManager> userPreferencesManagerProvider;

    private Provider<DataStore<Preferences>> provideThemeDataStoreProvider;

    private Provider<RenderOptimizer> provideRenderOptimizerProvider;

    private Provider<SystemBarManager> provideSystemBarManagerProvider;

    private Provider<AppUsageDao> provideAppUsageDaoProvider;

    private Provider<TimeAnalyticsRepositoryImpl> timeAnalyticsRepositoryImplProvider;

    private Provider<TimeAnalyticsRepository> bindTimeAnalyticsRepositoryProvider;

    private Provider<AiTaskRepositoryImpl> aiTaskRepositoryImplProvider;

    private Provider<UserPreferenceRepositoryImpl> userPreferenceRepositoryImplProvider;

    private Provider<AnalyticsDataService> analyticsDataServiceProvider;

    private Provider<AnalyticsInsightService> analyticsInsightServiceProvider;

    private Provider<SharedPendingDeletionState> provideSharedPendingDeletionStateProvider;

    private Provider<TaskTimeRepository> provideTaskTimeRepositoryProvider;

    private Provider<TaskTimeUseCase> provideTaskTimeUseCaseProvider;

    private Provider<SharedPreferences> provideSharedPreferencesProvider;

    private Provider<TemplateCache> templateCacheProvider;

    private Provider<GoalTemplateRepositoryImpl> goalTemplateRepositoryImplProvider;

    private Provider<CycleDao> provideCycleDaoProvider;

    private Provider<CycleRepository> provideCycleRepositoryProvider;

    private Provider<NotificationConfigManager> notificationConfigManagerProvider;

    private Provider<DataStore<Preferences>> provideNotificationDataStoreProvider;

    private Provider<MedicationReminderManager> medicationReminderManagerProvider;

    private Provider<DataStore<Preferences>> provideMedicationDataStoreProvider;

    private Provider<MedicationRecordDao> provideMedicationRecordDaoProvider;

    private Provider<MedicationRepository> provideMedicationRepositoryProvider;

    private Provider<EmotionRecordDao> provideEmotionRecordDaoProvider;

    private Provider<EmotionRecordRepository> provideEmotionRecordRepositoryProvider;

    private Provider<SearchSuggestionServiceImpl> searchSuggestionServiceImplProvider;

    private Provider<PaymentManager> paymentManagerProvider;

    private Provider<SimpleRealDataStatisticsService> provideSimpleRealDataStatisticsServiceProvider;

    private Provider<SyncRepositoryImpl> syncRepositoryImplProvider;

    private Provider<WorkManager> provideWorkManagerProvider;

    private Provider<SharedFilterState> provideSharedFilterStateProvider;

    private Provider<FocusTimerManager> focusTimerManagerProvider;

    private Provider<WishDao> provideWishDaoProvider;

    private Provider<WishRepository> provideWishRepositoryProvider;

    private SingletonCImpl(ApplicationContextModule applicationContextModuleParam) {
      this.applicationContextModule = applicationContextModuleParam;
      initialize(applicationContextModuleParam);

    }

    private Map<String, javax.inject.Provider<WorkerAssistedFactory<? extends ListenableWorker>>> mapOfStringAndProviderOfWorkerAssistedFactoryOf(
        ) {
      return MapBuilder.<String, javax.inject.Provider<WorkerAssistedFactory<? extends ListenableWorker>>>newMapBuilder(2).put("com.timeflow.app.worker.AutoBackupWorker", ((Provider) autoBackupWorker_AssistedFactoryProvider)).put("com.timeflow.app.worker.RecurringTaskWorker", ((Provider) recurringTaskWorker_AssistedFactoryProvider)).build();
    }

    @SuppressWarnings("unchecked")
    private void initialize(final ApplicationContextModule applicationContextModuleParam) {
      this.provideCrashReporterProvider = DoubleCheck.provider(new SwitchingProvider<CrashReporter>(singletonCImpl, 1));
      this.appExceptionHandlerProvider = DoubleCheck.provider(new SwitchingProvider<AppExceptionHandler>(singletonCImpl, 0));
      this.provideDatabaseBackupManagerProvider = DoubleCheck.provider(new SwitchingProvider<DatabaseBackupManager>(singletonCImpl, 7));
      this.provideAppDatabaseProvider = DoubleCheck.provider(new SwitchingProvider<AppDatabase>(singletonCImpl, 6));
      this.provideKanbanBoardDaoProvider = DoubleCheck.provider(new SwitchingProvider<KanbanBoardDao>(singletonCImpl, 5));
      this.kanbanBoardRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<KanbanBoardRepositoryImpl>(singletonCImpl, 4));
      this.provideKanbanColumnDaoProvider = DoubleCheck.provider(new SwitchingProvider<KanbanColumnDao>(singletonCImpl, 9));
      this.kanbanColumnRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<KanbanColumnRepositoryImpl>(singletonCImpl, 8));
      this.provideTaskDaoProvider = DoubleCheck.provider(new SwitchingProvider<TaskDao>(singletonCImpl, 12));
      this.provideTaskRepositoryProvider = new DelegateFactory<>();
      this.notificationHelperProvider = DoubleCheck.provider(new SwitchingProvider<NotificationHelper>(singletonCImpl, 15));
      this.timeFlowNotificationManagerProvider = DoubleCheck.provider(new SwitchingProvider<TimeFlowNotificationManager>(singletonCImpl, 14));
      this.taskReminderSchedulerProvider = DoubleCheck.provider(new SwitchingProvider<TaskReminderScheduler>(singletonCImpl, 13));
      this.provideContextProvider = DoubleCheck.provider(new SwitchingProvider<Context>(singletonCImpl, 17));
      this.notificationPermissionHelperProvider = DoubleCheck.provider(new SwitchingProvider<NotificationPermissionHelper>(singletonCImpl, 18));
      this.taskReminderUtilsProvider = DoubleCheck.provider(new SwitchingProvider<TaskReminderUtils>(singletonCImpl, 16));
      this.taskRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<TaskRepositoryImpl>(singletonCImpl, 11));
      DelegateFactory.setDelegate(provideTaskRepositoryProvider, DoubleCheck.provider(new SwitchingProvider<TaskRepository>(singletonCImpl, 10)));
      this.sampleDataGeneratorProvider = DoubleCheck.provider(new SwitchingProvider<SampleDataGenerator>(singletonCImpl, 3));
      this.provideGoalTemplateDaoProvider = DoubleCheck.provider(new SwitchingProvider<GoalTemplateDao>(singletonCImpl, 20));
      this.defaultTemplateInitializerProvider = DoubleCheck.provider(new SwitchingProvider<DefaultTemplateInitializer>(singletonCImpl, 19));
      this.appInitializerProvider = DoubleCheck.provider(new SwitchingProvider<AppInitializer>(singletonCImpl, 2));
      this.provideRoomDatabaseProvider = DoubleCheck.provider(new SwitchingProvider<RoomDatabase>(singletonCImpl, 21));
      this.provideReflectionDaoProvider = DoubleCheck.provider(new SwitchingProvider<ReflectionDao>(singletonCImpl, 23));
      this.reflectionRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<ReflectionRepositoryImpl>(singletonCImpl, 22));
      this.aiSuggestionSchedulerProvider = DoubleCheck.provider(new SwitchingProvider<AiSuggestionScheduler>(singletonCImpl, 24));
      this.taskPersistentNotificationManagerProvider = DoubleCheck.provider(new SwitchingProvider<TaskPersistentNotificationManager>(singletonCImpl, 25));
      this.autoBackupWorker_AssistedFactoryProvider = SingleCheck.provider(new SwitchingProvider<AutoBackupWorker_AssistedFactory>(singletonCImpl, 26));
      this.recurrenceCalculatorProvider = DoubleCheck.provider(new SwitchingProvider<RecurrenceCalculator>(singletonCImpl, 29));
      this.recurringTaskManagerProvider = DoubleCheck.provider(new SwitchingProvider<RecurringTaskManager>(singletonCImpl, 28));
      this.recurringTaskWorker_AssistedFactoryProvider = SingleCheck.provider(new SwitchingProvider<RecurringTaskWorker_AssistedFactory>(singletonCImpl, 27));
      this.provideHabitDaoProvider = DoubleCheck.provider(new SwitchingProvider<HabitDao>(singletonCImpl, 31));
      this.provideGoalDaoProvider = DoubleCheck.provider(new SwitchingProvider<GoalDao>(singletonCImpl, 32));
      this.habitRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<HabitRepositoryImpl>(singletonCImpl, 30));
      this.dailyReviewSchedulerProvider = DoubleCheck.provider(new SwitchingProvider<DailyReviewScheduler>(singletonCImpl, 33));
      this.provideTimeSessionDaoProvider = DoubleCheck.provider(new SwitchingProvider<TimeSessionDao>(singletonCImpl, 35));
      this.timeSessionRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<TimeSessionRepository>(singletonCImpl, 34));
      this.provideGoalRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<GoalRepository>(singletonCImpl, 36));
      this.userPreferencesManagerProvider = DoubleCheck.provider(new SwitchingProvider<UserPreferencesManager>(singletonCImpl, 37));
      this.provideThemeDataStoreProvider = DoubleCheck.provider(new SwitchingProvider<DataStore<Preferences>>(singletonCImpl, 38));
      this.provideRenderOptimizerProvider = DoubleCheck.provider(new SwitchingProvider<RenderOptimizer>(singletonCImpl, 39));
      this.provideSystemBarManagerProvider = DoubleCheck.provider(new SwitchingProvider<SystemBarManager>(singletonCImpl, 40));
      this.provideAppUsageDaoProvider = DoubleCheck.provider(new SwitchingProvider<AppUsageDao>(singletonCImpl, 42));
      this.timeAnalyticsRepositoryImplProvider = new SwitchingProvider<>(singletonCImpl, 41);
      this.bindTimeAnalyticsRepositoryProvider = DoubleCheck.provider((Provider) timeAnalyticsRepositoryImplProvider);
      this.aiTaskRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<AiTaskRepositoryImpl>(singletonCImpl, 43));
      this.userPreferenceRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<UserPreferenceRepositoryImpl>(singletonCImpl, 44));
      this.analyticsDataServiceProvider = DoubleCheck.provider(new SwitchingProvider<AnalyticsDataService>(singletonCImpl, 45));
      this.analyticsInsightServiceProvider = DoubleCheck.provider(new SwitchingProvider<AnalyticsInsightService>(singletonCImpl, 46));
      this.provideSharedPendingDeletionStateProvider = DoubleCheck.provider(new SwitchingProvider<SharedPendingDeletionState>(singletonCImpl, 47));
      this.provideTaskTimeRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<TaskTimeRepository>(singletonCImpl, 49));
      this.provideTaskTimeUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<TaskTimeUseCase>(singletonCImpl, 48));
      this.provideSharedPreferencesProvider = DoubleCheck.provider(new SwitchingProvider<SharedPreferences>(singletonCImpl, 50));
      this.templateCacheProvider = DoubleCheck.provider(new SwitchingProvider<TemplateCache>(singletonCImpl, 52));
      this.goalTemplateRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<GoalTemplateRepositoryImpl>(singletonCImpl, 51));
      this.provideCycleDaoProvider = DoubleCheck.provider(new SwitchingProvider<CycleDao>(singletonCImpl, 54));
      this.provideCycleRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<CycleRepository>(singletonCImpl, 53));
      this.notificationConfigManagerProvider = DoubleCheck.provider(new SwitchingProvider<NotificationConfigManager>(singletonCImpl, 55));
      this.provideNotificationDataStoreProvider = DoubleCheck.provider(new SwitchingProvider<DataStore<Preferences>>(singletonCImpl, 56));
      this.medicationReminderManagerProvider = DoubleCheck.provider(new SwitchingProvider<MedicationReminderManager>(singletonCImpl, 57));
      this.provideMedicationDataStoreProvider = DoubleCheck.provider(new SwitchingProvider<DataStore<Preferences>>(singletonCImpl, 58));
      this.provideMedicationRecordDaoProvider = DoubleCheck.provider(new SwitchingProvider<MedicationRecordDao>(singletonCImpl, 60));
      this.provideMedicationRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<MedicationRepository>(singletonCImpl, 59));
      this.provideEmotionRecordDaoProvider = DoubleCheck.provider(new SwitchingProvider<EmotionRecordDao>(singletonCImpl, 62));
      this.provideEmotionRecordRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<EmotionRecordRepository>(singletonCImpl, 61));
      this.searchSuggestionServiceImplProvider = DoubleCheck.provider(new SwitchingProvider<SearchSuggestionServiceImpl>(singletonCImpl, 63));
      this.paymentManagerProvider = DoubleCheck.provider(new SwitchingProvider<PaymentManager>(singletonCImpl, 64));
      this.provideSimpleRealDataStatisticsServiceProvider = DoubleCheck.provider(new SwitchingProvider<SimpleRealDataStatisticsService>(singletonCImpl, 65));
      this.syncRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<SyncRepositoryImpl>(singletonCImpl, 66));
      this.provideWorkManagerProvider = DoubleCheck.provider(new SwitchingProvider<WorkManager>(singletonCImpl, 67));
      this.provideSharedFilterStateProvider = DoubleCheck.provider(new SwitchingProvider<SharedFilterState>(singletonCImpl, 68));
      this.focusTimerManagerProvider = DoubleCheck.provider(new SwitchingProvider<FocusTimerManager>(singletonCImpl, 69));
      this.provideWishDaoProvider = DoubleCheck.provider(new SwitchingProvider<WishDao>(singletonCImpl, 71));
      this.provideWishRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<WishRepository>(singletonCImpl, 70));
    }

    @Override
    public void injectTimeFlowApplication(TimeFlowApplication timeFlowApplication) {
      injectTimeFlowApplication2(timeFlowApplication);
    }

    @Override
    public HiltWorkerFactory workerFactory() {
      return WorkerFactoryModule_ProvideFactoryFactory.provideFactory(mapOfStringAndProviderOfWorkerAssistedFactoryOf());
    }

    @Override
    public RecurringTaskManager recurringTaskManager() {
      return recurringTaskManagerProvider.get();
    }

    @Override
    public void injectBootCompletedReceiver(BootCompletedReceiver bootCompletedReceiver) {
      injectBootCompletedReceiver2(bootCompletedReceiver);
    }

    @Override
    public void injectDailyReviewAlarmReceiver(DailyReviewAlarmReceiver dailyReviewAlarmReceiver) {
      injectDailyReviewAlarmReceiver2(dailyReviewAlarmReceiver);
    }

    @Override
    public void injectFocusTimerActionReceiver(FocusTimerActionReceiver focusTimerActionReceiver) {
    }

    @Override
    public void injectHabitAlarmReceiver(HabitAlarmReceiver habitAlarmReceiver) {
      injectHabitAlarmReceiver2(habitAlarmReceiver);
    }

    @Override
    public void injectTaskAlarmReceiver(TaskAlarmReceiver taskAlarmReceiver) {
      injectTaskAlarmReceiver2(taskAlarmReceiver);
    }

    @Override
    public void injectTaskPersistentNotificationActionReceiver(
        TaskPersistentNotificationActionReceiver taskPersistentNotificationActionReceiver) {
    }

    @Override
    public UserPreferencesManager userPreferencesManager() {
      return userPreferencesManagerProvider.get();
    }

    @Override
    public TaskReminderScheduler taskReminderScheduler() {
      return taskReminderSchedulerProvider.get();
    }

    @Override
    public Set<Boolean> getDisableFragmentGetContextFix() {
      return Collections.<Boolean>emptySet();
    }

    @Override
    public ActivityRetainedComponentBuilder retainedComponentBuilder() {
      return new ActivityRetainedCBuilder(singletonCImpl);
    }

    @Override
    public ServiceComponentBuilder serviceComponentBuilder() {
      return new ServiceCBuilder(singletonCImpl);
    }

    @CanIgnoreReturnValue
    private TimeFlowApplication injectTimeFlowApplication2(TimeFlowApplication instance) {
      TimeFlowApplication_MembersInjector.injectExceptionHandler(instance, appExceptionHandlerProvider.get());
      TimeFlowApplication_MembersInjector.injectAppInitializer(instance, appInitializerProvider.get());
      TimeFlowApplication_MembersInjector.injectAppDatabase(instance, provideRoomDatabaseProvider.get());
      TimeFlowApplication_MembersInjector.injectReflectionRepository(instance, reflectionRepositoryImplProvider.get());
      TimeFlowApplication_MembersInjector.injectAiSuggestionScheduler(instance, aiSuggestionSchedulerProvider.get());
      TimeFlowApplication_MembersInjector.injectTaskPersistentNotificationManager(instance, taskPersistentNotificationManagerProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private BootCompletedReceiver injectBootCompletedReceiver2(BootCompletedReceiver instance) {
      BootCompletedReceiver_MembersInjector.injectHabitRepository(instance, habitRepositoryImplProvider.get());
      BootCompletedReceiver_MembersInjector.injectTaskRepository(instance, provideTaskRepositoryProvider.get());
      BootCompletedReceiver_MembersInjector.injectTaskReminderScheduler(instance, taskReminderSchedulerProvider.get());
      BootCompletedReceiver_MembersInjector.injectDailyReviewScheduler(instance, dailyReviewSchedulerProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private DailyReviewAlarmReceiver injectDailyReviewAlarmReceiver2(
        DailyReviewAlarmReceiver instance) {
      DailyReviewAlarmReceiver_MembersInjector.injectNotificationManager(instance, timeFlowNotificationManagerProvider.get());
      DailyReviewAlarmReceiver_MembersInjector.injectTaskRepository(instance, provideTaskRepositoryProvider.get());
      DailyReviewAlarmReceiver_MembersInjector.injectHabitRepository(instance, habitRepositoryImplProvider.get());
      DailyReviewAlarmReceiver_MembersInjector.injectTimeSessionRepository(instance, timeSessionRepositoryProvider.get());
      DailyReviewAlarmReceiver_MembersInjector.injectReflectionRepository(instance, reflectionRepositoryImplProvider.get());
      DailyReviewAlarmReceiver_MembersInjector.injectGoalRepository(instance, provideGoalRepositoryProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private HabitAlarmReceiver injectHabitAlarmReceiver2(HabitAlarmReceiver instance) {
      HabitAlarmReceiver_MembersInjector.injectNotificationHelper(instance, notificationHelperProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private TaskAlarmReceiver injectTaskAlarmReceiver2(TaskAlarmReceiver instance) {
      TaskAlarmReceiver_MembersInjector.injectNotificationManager(instance, timeFlowNotificationManagerProvider.get());
      return instance;
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.timeflow.app.utils.AppExceptionHandler 
          return (T) new AppExceptionHandler(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.provideCrashReporterProvider.get());

          case 1: // com.timeflow.app.di.CrashReporter 
          return (T) AnalyticsModule_ProvideCrashReporterFactory.provideCrashReporter(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 2: // com.timeflow.app.di.AppInitializer 
          return (T) new AppInitializer(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.sampleDataGeneratorProvider.get(), singletonCImpl.defaultTemplateInitializerProvider.get());

          case 3: // com.timeflow.app.utils.SampleDataGenerator 
          return (T) new SampleDataGenerator(singletonCImpl.kanbanBoardRepositoryImplProvider.get(), singletonCImpl.kanbanColumnRepositoryImplProvider.get(), singletonCImpl.provideTaskRepositoryProvider);

          case 4: // com.timeflow.app.data.repository.KanbanBoardRepositoryImpl 
          return (T) new KanbanBoardRepositoryImpl(singletonCImpl.provideKanbanBoardDaoProvider.get());

          case 5: // com.timeflow.app.data.dao.KanbanBoardDao 
          return (T) DatabaseModule_ProvideKanbanBoardDaoFactory.provideKanbanBoardDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 6: // com.timeflow.app.data.db.AppDatabase 
          return (T) DatabaseModule_ProvideAppDatabaseFactory.provideAppDatabase(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.provideDatabaseBackupManagerProvider.get());

          case 7: // com.timeflow.app.utils.DatabaseBackupManager 
          return (T) DatabaseModule_ProvideDatabaseBackupManagerFactory.provideDatabaseBackupManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 8: // com.timeflow.app.data.repository.KanbanColumnRepositoryImpl 
          return (T) new KanbanColumnRepositoryImpl(singletonCImpl.provideKanbanColumnDaoProvider.get());

          case 9: // com.timeflow.app.data.dao.KanbanColumnDao 
          return (T) DatabaseModule_ProvideKanbanColumnDaoFactory.provideKanbanColumnDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 10: // com.timeflow.app.data.repository.TaskRepository 
          return (T) RepositoryModule_ProvideTaskRepositoryFactory.provideTaskRepository(singletonCImpl.taskRepositoryImplProvider.get());

          case 11: // com.timeflow.app.data.repository.TaskRepositoryImpl 
          return (T) new TaskRepositoryImpl(singletonCImpl.provideTaskDaoProvider.get(), singletonCImpl.provideAppDatabaseProvider.get(), singletonCImpl.taskReminderSchedulerProvider, singletonCImpl.taskReminderUtilsProvider.get());

          case 12: // com.timeflow.app.data.dao.TaskDao 
          return (T) DatabaseModule_ProvideTaskDaoFactory.provideTaskDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 13: // com.timeflow.app.service.TaskReminderScheduler 
          return (T) new TaskReminderScheduler(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.provideTaskRepositoryProvider.get(), singletonCImpl.timeFlowNotificationManagerProvider.get());

          case 14: // com.timeflow.app.utils.TimeFlowNotificationManager 
          return (T) new TimeFlowNotificationManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.notificationHelperProvider.get());

          case 15: // com.timeflow.app.utils.NotificationHelper 
          return (T) new NotificationHelper(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 16: // com.timeflow.app.utils.TaskReminderUtils 
          return (T) new TaskReminderUtils(singletonCImpl.provideContextProvider.get(), singletonCImpl.notificationPermissionHelperProvider.get());

          case 17: // android.content.Context 
          return (T) RepositoryModule_ProvideContextFactory.provideContext(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 18: // com.timeflow.app.utils.NotificationPermissionHelper 
          return (T) new NotificationPermissionHelper(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 19: // com.timeflow.app.data.repository.DefaultTemplateInitializer 
          return (T) new DefaultTemplateInitializer(singletonCImpl.provideGoalTemplateDaoProvider.get());

          case 20: // com.timeflow.app.data.dao.GoalTemplateDao 
          return (T) DatabaseModule_ProvideGoalTemplateDaoFactory.provideGoalTemplateDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 21: // androidx.room.RoomDatabase 
          return (T) DatabaseModule_ProvideRoomDatabaseFactory.provideRoomDatabase(singletonCImpl.provideAppDatabaseProvider.get());

          case 22: // com.timeflow.app.ui.screen.reflection.data.ReflectionRepositoryImpl 
          return (T) new ReflectionRepositoryImpl(singletonCImpl.provideReflectionDaoProvider.get());

          case 23: // com.timeflow.app.data.dao.ReflectionDao 
          return (T) DatabaseModule_ProvideReflectionDaoFactory.provideReflectionDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 24: // com.timeflow.app.service.AiSuggestionScheduler 
          return (T) new AiSuggestionScheduler(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 25: // com.timeflow.app.service.TaskPersistentNotificationManager 
          return (T) new TaskPersistentNotificationManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 26: // com.timeflow.app.worker.AutoBackupWorker_AssistedFactory 
          return (T) new AutoBackupWorker_AssistedFactory() {
            @Override
            public AutoBackupWorker create(Context context, WorkerParameters workerParams) {
              return new AutoBackupWorker(context, workerParams, singletonCImpl.provideDatabaseBackupManagerProvider.get());
            }
          };

          case 27: // com.timeflow.app.worker.RecurringTaskWorker_AssistedFactory 
          return (T) new RecurringTaskWorker_AssistedFactory() {
            @Override
            public RecurringTaskWorker create(Context context2, WorkerParameters workerParams2) {
              return new RecurringTaskWorker(context2, workerParams2, singletonCImpl.recurringTaskManagerProvider.get());
            }
          };

          case 28: // com.timeflow.app.service.RecurringTaskManager 
          return (T) new RecurringTaskManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.provideTaskRepositoryProvider.get(), singletonCImpl.recurrenceCalculatorProvider.get());

          case 29: // com.timeflow.app.service.RecurrenceCalculator 
          return (T) new RecurrenceCalculator();

          case 30: // com.timeflow.app.data.repository.HabitRepositoryImpl 
          return (T) new HabitRepositoryImpl(singletonCImpl.provideHabitDaoProvider.get(), singletonCImpl.provideGoalDaoProvider.get());

          case 31: // com.timeflow.app.data.dao.HabitDao 
          return (T) DatabaseModule_ProvideHabitDaoFactory.provideHabitDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 32: // com.timeflow.app.data.dao.GoalDao 
          return (T) DatabaseModule_ProvideGoalDaoFactory.provideGoalDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 33: // com.timeflow.app.service.DailyReviewScheduler 
          return (T) new DailyReviewScheduler(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 34: // com.timeflow.app.data.repository.TimeSessionRepository 
          return (T) new TimeSessionRepository(singletonCImpl.provideTimeSessionDaoProvider.get());

          case 35: // com.timeflow.app.data.dao.TimeSessionDao 
          return (T) DatabaseModule_ProvideTimeSessionDaoFactory.provideTimeSessionDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 36: // com.timeflow.app.data.repository.GoalRepository 
          return (T) DatabaseModule_ProvideGoalRepositoryFactory.provideGoalRepository(singletonCImpl.provideGoalDaoProvider.get());

          case 37: // com.timeflow.app.data.preferences.UserPreferencesManager 
          return (T) new UserPreferencesManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 38: // @com.timeflow.app.di.DataStoreModule.ThemeDataStore androidx.datastore.core.DataStore<androidx.datastore.preferences.core.Preferences> 
          return (T) DataStoreModule_ProvideThemeDataStoreFactory.provideThemeDataStore(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 39: // com.timeflow.app.utils.RenderOptimizer 
          return (T) UtilsModule_ProvideRenderOptimizerFactory.provideRenderOptimizer();

          case 40: // com.timeflow.app.utils.SystemBarManager 
          return (T) UtilsModule_ProvideSystemBarManagerFactory.provideSystemBarManager();

          case 41: // com.timeflow.app.data.repository.TimeAnalyticsRepositoryImpl 
          return (T) new TimeAnalyticsRepositoryImpl(singletonCImpl.provideAppUsageDaoProvider.get());

          case 42: // com.timeflow.app.data.dao.AppUsageDao 
          return (T) DatabaseModule_ProvideAppUsageDaoFactory.provideAppUsageDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 43: // com.timeflow.app.data.repository.AiTaskRepositoryImpl 
          return (T) new AiTaskRepositoryImpl(singletonCImpl.provideTaskRepositoryProvider.get(), singletonCImpl.provideContextProvider.get());

          case 44: // com.timeflow.app.data.repository.UserPreferenceRepositoryImpl 
          return (T) new UserPreferenceRepositoryImpl();

          case 45: // com.timeflow.app.ui.screen.analytics.AnalyticsDataService 
          return (T) new AnalyticsDataService(singletonCImpl.provideTaskRepositoryProvider.get(), singletonCImpl.provideGoalRepositoryProvider.get(), singletonCImpl.habitRepositoryImplProvider.get(), singletonCImpl.timeSessionRepositoryProvider.get(), singletonCImpl.bindTimeAnalyticsRepositoryProvider.get());

          case 46: // com.timeflow.app.ui.screen.analytics.AnalyticsInsightService 
          return (T) new AnalyticsInsightService(singletonCImpl.provideTaskRepositoryProvider.get(), singletonCImpl.timeSessionRepositoryProvider.get(), singletonCImpl.provideGoalRepositoryProvider.get(), singletonCImpl.habitRepositoryImplProvider.get());

          case 47: // com.timeflow.app.data.repository.SharedPendingDeletionState 
          return (T) RepositoryModule_ProvideSharedPendingDeletionStateFactory.provideSharedPendingDeletionState(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.provideTaskRepositoryProvider.get());

          case 48: // com.timeflow.app.domain.usecase.TaskTimeUseCase 
          return (T) TaskTimeModule_ProvideTaskTimeUseCaseFactory.provideTaskTimeUseCase(singletonCImpl.provideTaskTimeRepositoryProvider.get());

          case 49: // com.timeflow.app.data.repository.TaskTimeRepository 
          return (T) TaskTimeModule_ProvideTaskTimeRepositoryFactory.provideTaskTimeRepository();

          case 50: // android.content.SharedPreferences 
          return (T) DatabaseModule_ProvideSharedPreferencesFactory.provideSharedPreferences(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 51: // com.timeflow.app.data.repository.GoalTemplateRepositoryImpl 
          return (T) new GoalTemplateRepositoryImpl(singletonCImpl.provideGoalTemplateDaoProvider.get(), singletonCImpl.provideGoalRepositoryProvider.get(), singletonCImpl.templateCacheProvider.get());

          case 52: // com.timeflow.app.utils.TemplateCache 
          return (T) new TemplateCache();

          case 53: // com.timeflow.app.data.repository.CycleRepository 
          return (T) RepositoryModule_ProvideCycleRepositoryFactory.provideCycleRepository(singletonCImpl.provideCycleDaoProvider.get());

          case 54: // com.timeflow.app.data.dao.CycleDao 
          return (T) DatabaseModule_ProvideCycleDaoFactory.provideCycleDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 55: // com.timeflow.app.service.NotificationConfigManager 
          return (T) new NotificationConfigManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 56: // @com.timeflow.app.di.DataStoreModule.NotificationDataStore androidx.datastore.core.DataStore<androidx.datastore.preferences.core.Preferences> 
          return (T) DataStoreModule_ProvideNotificationDataStoreFactory.provideNotificationDataStore(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 57: // com.timeflow.app.service.MedicationReminderManager 
          return (T) new MedicationReminderManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 58: // @com.timeflow.app.di.DataStoreModule.MedicationDataStore androidx.datastore.core.DataStore<androidx.datastore.preferences.core.Preferences> 
          return (T) DataStoreModule_ProvideMedicationDataStoreFactory.provideMedicationDataStore(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 59: // com.timeflow.app.data.repository.MedicationRepository 
          return (T) MedicationModule_ProvideMedicationRepositoryFactory.provideMedicationRepository(singletonCImpl.provideMedicationRecordDaoProvider.get());

          case 60: // com.timeflow.app.data.dao.MedicationRecordDao 
          return (T) MedicationModule_ProvideMedicationRecordDaoFactory.provideMedicationRecordDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 61: // com.timeflow.app.data.repository.EmotionRecordRepository 
          return (T) DatabaseModule_ProvideEmotionRecordRepositoryFactory.provideEmotionRecordRepository(singletonCImpl.provideEmotionRecordDaoProvider.get());

          case 62: // com.timeflow.app.data.dao.EmotionRecordDao 
          return (T) DatabaseModule_ProvideEmotionRecordDaoFactory.provideEmotionRecordDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 63: // com.timeflow.app.ui.screen.reflection.data.SearchSuggestionServiceImpl 
          return (T) new SearchSuggestionServiceImpl(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 64: // com.timeflow.app.service.PaymentManager 
          return (T) new PaymentManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 65: // com.timeflow.app.data.service.SimpleRealDataStatisticsService 
          return (T) StatisticsModule_ProvideSimpleRealDataStatisticsServiceFactory.provideSimpleRealDataStatisticsService(singletonCImpl.provideTaskRepositoryProvider.get(), singletonCImpl.reflectionRepositoryImplProvider.get());

          case 66: // com.timeflow.app.ui.screen.settings.SyncRepositoryImpl 
          return (T) new SyncRepositoryImpl();

          case 67: // androidx.work.WorkManager 
          return (T) WorkManagerModule_ProvideWorkManagerFactory.provideWorkManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 68: // com.timeflow.app.data.repository.SharedFilterState 
          return (T) RepositoryModule_ProvideSharedFilterStateFactory.provideSharedFilterState();

          case 69: // com.timeflow.app.service.FocusTimerManager 
          return (T) new FocusTimerManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 70: // com.timeflow.app.data.repository.WishRepository 
          return (T) WishModule_ProvideWishRepositoryFactory.provideWishRepository(singletonCImpl.provideWishDaoProvider.get());

          case 71: // com.timeflow.app.data.dao.WishDao 
          return (T) WishModule_ProvideWishDaoFactory.provideWishDao(singletonCImpl.provideAppDatabaseProvider.get());

          default: throw new AssertionError(id);
        }
      }
    }
  }
}
