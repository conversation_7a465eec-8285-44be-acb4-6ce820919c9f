package com.timeflow.app.widget.layout

import android.content.Context
import android.util.DisplayMetrics
import android.util.TypedValue
import androidx.annotation.Dimension
import kotlin.math.roundToInt

/**
 * 小组件尺寸管理器
 * 实现响应式布局系统，支持多种尺寸适配
 */
class WidgetSizeManager private constructor(private val context: Context) {

    companion object {
        @Volatile
        private var INSTANCE: WidgetSizeManager? = null

        fun getInstance(context: Context): WidgetSizeManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: WidgetSizeManager(context.applicationContext).also { INSTANCE = it }
            }
        }

        // 标准小组件尺寸（dp）
        private const val CELL_SIZE_DP = 74
        private const val CELL_MARGIN_DP = 8
    }

    /**
     * 支持的小组件尺寸
     */
    enum class WidgetSize(val width: Int, val height: Int, val displayName: String) {
        SMALL(1, 1, "小尺寸 1×1"),           // 1x1
        MEDIUM_HORIZONTAL(2, 1, "中等横向 2×1"), // 2x1
        MEDIUM(2, 2, "中等 2×2"),            // 2x2
        LARGE_HORIZONTAL(4, 2, "大尺寸横向 4×2"), // 4x2
        LARGE(4, 3, "大尺寸 4×3"),           // 4x3
        EXTRA_LARGE(4, 4, "超大尺寸 4×4")      // 4x4
    }

    /**
     * UI元素类型
     */
    enum class UIElement {
        TITLE,              // 标题
        SUBTITLE,           // 副标题
        BODY_TEXT,          // 正文
        CAPTION,            // 说明文字
        BUTTON,             // 按钮
        ICON,               // 图标
        PROGRESS_BAR,       // 进度条
        CHART,              // 图表
        LIST_ITEM,          // 列表项
        DIVIDER,            // 分隔线
        BADGE,              // 徽章
        AVATAR              // 头像
    }

    /**
     * 文本类型
     */
    enum class TextType {
        DISPLAY_LARGE,
        DISPLAY_MEDIUM,
        DISPLAY_SMALL,
        HEADLINE_LARGE,
        HEADLINE_MEDIUM,
        HEADLINE_SMALL,
        TITLE_LARGE,
        TITLE_MEDIUM,
        TITLE_SMALL,
        BODY_LARGE,
        BODY_MEDIUM,
        BODY_SMALL,
        LABEL_LARGE,
        LABEL_MEDIUM,
        LABEL_SMALL
    }

    /**
     * 布局配置
     */
    data class LayoutConfig(
        val size: WidgetSize,
        val widthDp: Int,
        val heightDp: Int,
        val paddingDp: Int,
        val cornerRadiusDp: Int,
        val elevationDp: Int,
        val maxItems: Int,
        val showDetails: Boolean,
        val showIcons: Boolean,
        val showProgress: Boolean,
        val textScale: Float,
        val iconScale: Float,
        val spacingScale: Float
    )

    /**
     * 尺寸规格
     */
    data class SizeSpec(
        val widthPx: Int,
        val heightPx: Int,
        val widthDp: Int,
        val heightDp: Int,
        val density: Float
    )

    private val displayMetrics: DisplayMetrics = context.resources.displayMetrics

    /**
     * 根据像素尺寸确定小组件尺寸
     */
    fun determineWidgetSize(widthPx: Int, heightPx: Int): WidgetSize {
        val widthDp = pxToDp(widthPx)
        val heightDp = pxToDp(heightPx)

        return when {
            // 1x1 小尺寸
            widthDp <= 110 && heightDp <= 110 -> WidgetSize.SMALL
            
            // 2x1 中等横向
            widthDp <= 180 && heightDp <= 110 -> WidgetSize.MEDIUM_HORIZONTAL
            
            // 2x2 中等尺寸
            widthDp <= 180 && heightDp <= 180 -> WidgetSize.MEDIUM
            
            // 4x2 大尺寸横向
            widthDp <= 320 && heightDp <= 180 -> WidgetSize.LARGE_HORIZONTAL
            
            // 4x3 大尺寸
            widthDp <= 320 && heightDp <= 250 -> WidgetSize.LARGE
            
            // 4x4 超大尺寸
            else -> WidgetSize.EXTRA_LARGE
        }
    }

    /**
     * 获取布局配置
     */
    fun getLayoutConfig(size: WidgetSize): LayoutConfig {
        return when (size) {
            WidgetSize.SMALL -> LayoutConfig(
                size = size,
                widthDp = 74,
                heightDp = 74,
                paddingDp = 8,
                cornerRadiusDp = 16,
                elevationDp = 2,
                maxItems = 1,
                showDetails = false,
                showIcons = true,
                showProgress = false,
                textScale = 0.8f,
                iconScale = 0.8f,
                spacingScale = 0.8f
            )
            
            WidgetSize.MEDIUM_HORIZONTAL -> LayoutConfig(
                size = size,
                widthDp = 156,
                heightDp = 74,
                paddingDp = 12,
                cornerRadiusDp = 16,
                elevationDp = 2,
                maxItems = 2,
                showDetails = false,
                showIcons = true,
                showProgress = true,
                textScale = 0.9f,
                iconScale = 0.9f,
                spacingScale = 0.9f
            )
            
            WidgetSize.MEDIUM -> LayoutConfig(
                size = size,
                widthDp = 156,
                heightDp = 156,
                paddingDp = 16,
                cornerRadiusDp = 20,
                elevationDp = 4,
                maxItems = 3,
                showDetails = true,
                showIcons = true,
                showProgress = true,
                textScale = 1.0f,
                iconScale = 1.0f,
                spacingScale = 1.0f
            )
            
            WidgetSize.LARGE_HORIZONTAL -> LayoutConfig(
                size = size,
                widthDp = 320,
                heightDp = 156,
                paddingDp = 16,
                cornerRadiusDp = 20,
                elevationDp = 4,
                maxItems = 4,
                showDetails = true,
                showIcons = true,
                showProgress = true,
                textScale = 1.0f,
                iconScale = 1.0f,
                spacingScale = 1.0f
            )
            
            WidgetSize.LARGE -> LayoutConfig(
                size = size,
                widthDp = 320,
                heightDp = 238,
                paddingDp = 20,
                cornerRadiusDp = 24,
                elevationDp = 6,
                maxItems = 5,
                showDetails = true,
                showIcons = true,
                showProgress = true,
                textScale = 1.1f,
                iconScale = 1.1f,
                spacingScale = 1.1f
            )
            
            WidgetSize.EXTRA_LARGE -> LayoutConfig(
                size = size,
                widthDp = 320,
                heightDp = 320,
                paddingDp = 24,
                cornerRadiusDp = 28,
                elevationDp = 8,
                maxItems = 8,
                showDetails = true,
                showIcons = true,
                showProgress = true,
                textScale = 1.2f,
                iconScale = 1.2f,
                spacingScale = 1.2f
            )
        }
    }

    /**
     * 获取最优文本大小
     */
    fun getOptimalTextSize(size: WidgetSize, textType: TextType): Float {
        val config = getLayoutConfig(size)
        val baseSize = getBaseTextSize(textType)
        return baseSize * config.textScale
    }

    /**
     * 获取基础文本大小
     */
    private fun getBaseTextSize(textType: TextType): Float {
        return when (textType) {
            TextType.DISPLAY_LARGE -> 57f
            TextType.DISPLAY_MEDIUM -> 45f
            TextType.DISPLAY_SMALL -> 36f
            TextType.HEADLINE_LARGE -> 32f
            TextType.HEADLINE_MEDIUM -> 28f
            TextType.HEADLINE_SMALL -> 24f
            TextType.TITLE_LARGE -> 22f
            TextType.TITLE_MEDIUM -> 16f
            TextType.TITLE_SMALL -> 14f
            TextType.BODY_LARGE -> 16f
            TextType.BODY_MEDIUM -> 14f
            TextType.BODY_SMALL -> 12f
            TextType.LABEL_LARGE -> 14f
            TextType.LABEL_MEDIUM -> 12f
            TextType.LABEL_SMALL -> 11f
        }
    }

    /**
     * 判断是否应该显示UI元素
     */
    fun shouldShowElement(element: UIElement, size: WidgetSize): Boolean {
        val config = getLayoutConfig(size)
        
        return when (element) {
            UIElement.TITLE -> true // 标题总是显示
            UIElement.SUBTITLE -> size != WidgetSize.SMALL
            UIElement.BODY_TEXT -> config.showDetails
            UIElement.CAPTION -> config.showDetails && size.width >= 2
            UIElement.BUTTON -> size.width >= 2
            UIElement.ICON -> config.showIcons
            UIElement.PROGRESS_BAR -> config.showProgress
            UIElement.CHART -> size.width >= 2 && size.height >= 2
            UIElement.LIST_ITEM -> size.height >= 2
            UIElement.DIVIDER -> config.showDetails && size.height >= 2
            UIElement.BADGE -> true
            UIElement.AVATAR -> size.width >= 2
        }
    }

    /**
     * 获取图标大小
     */
    fun getIconSize(size: WidgetSize): Int {
        val config = getLayoutConfig(size)
        val baseSize = when (size) {
            WidgetSize.SMALL -> 16
            WidgetSize.MEDIUM_HORIZONTAL -> 20
            WidgetSize.MEDIUM -> 24
            WidgetSize.LARGE_HORIZONTAL -> 24
            WidgetSize.LARGE -> 28
            WidgetSize.EXTRA_LARGE -> 32
        }
        return (baseSize * config.iconScale).roundToInt()
    }

    /**
     * 获取间距大小
     */
    fun getSpacing(size: WidgetSize, spacingType: SpacingType): Int {
        val config = getLayoutConfig(size)
        val baseSpacing = when (spacingType) {
            SpacingType.EXTRA_SMALL -> 4
            SpacingType.SMALL -> 8
            SpacingType.MEDIUM -> 12
            SpacingType.LARGE -> 16
            SpacingType.EXTRA_LARGE -> 24
        }
        return (baseSpacing * config.spacingScale).roundToInt()
    }

    /**
     * 间距类型
     */
    enum class SpacingType {
        EXTRA_SMALL,
        SMALL,
        MEDIUM,
        LARGE,
        EXTRA_LARGE
    }

    /**
     * 获取尺寸规格
     */
    fun getSizeSpec(widthPx: Int, heightPx: Int): SizeSpec {
        return SizeSpec(
            widthPx = widthPx,
            heightPx = heightPx,
            widthDp = pxToDp(widthPx),
            heightDp = pxToDp(heightPx),
            density = displayMetrics.density
        )
    }

    /**
     * 获取推荐的列数
     */
    fun getRecommendedColumns(size: WidgetSize): Int {
        return when (size) {
            WidgetSize.SMALL -> 1
            WidgetSize.MEDIUM_HORIZONTAL -> 2
            WidgetSize.MEDIUM -> 1
            WidgetSize.LARGE_HORIZONTAL -> 2
            WidgetSize.LARGE -> 1
            WidgetSize.EXTRA_LARGE -> 2
        }
    }

    /**
     * 获取推荐的行数
     */
    fun getRecommendedRows(size: WidgetSize): Int {
        return when (size) {
            WidgetSize.SMALL -> 1
            WidgetSize.MEDIUM_HORIZONTAL -> 1
            WidgetSize.MEDIUM -> 3
            WidgetSize.LARGE_HORIZONTAL -> 2
            WidgetSize.LARGE -> 4
            WidgetSize.EXTRA_LARGE -> 6
        }
    }

    /**
     * 计算自适应文本大小
     */
    fun calculateAdaptiveTextSize(
        text: String,
        maxWidthPx: Int,
        maxHeightPx: Int,
        minTextSizeSp: Float = 8f,
        maxTextSizeSp: Float = 24f
    ): Float {
        // 简化的文本大小计算逻辑
        val availableWidth = pxToDp(maxWidthPx)
        val availableHeight = pxToDp(maxHeightPx)
        
        val textLength = text.length
        val estimatedWidth = textLength * 0.6f // 估算字符宽度
        
        val widthBasedSize = (availableWidth / estimatedWidth) * 14f
        val heightBasedSize = availableHeight * 0.4f
        
        val calculatedSize = minOf(widthBasedSize, heightBasedSize)
        
        return calculatedSize.coerceIn(minTextSizeSp, maxTextSizeSp)
    }

    /**
     * 获取响应式边距
     */
    fun getResponsiveMargin(size: WidgetSize, marginType: MarginType): Int {
        val config = getLayoutConfig(size)
        val baseMargin = when (marginType) {
            MarginType.CONTENT -> config.paddingDp
            MarginType.ITEM -> config.paddingDp / 2
            MarginType.SECTION -> config.paddingDp * 2
        }
        return dpToPx(baseMargin)
    }

    /**
     * 边距类型
     */
    enum class MarginType {
        CONTENT,    // 内容边距
        ITEM,       // 项目边距
        SECTION     // 区域边距
    }

    /**
     * 检查是否为紧凑布局
     */
    fun isCompactLayout(size: WidgetSize): Boolean {
        return size == WidgetSize.SMALL || size == WidgetSize.MEDIUM_HORIZONTAL
    }

    /**
     * 检查是否为宽屏布局
     */
    fun isWideLayout(size: WidgetSize): Boolean {
        return size.width >= 4
    }

    /**
     * 检查是否为高屏布局
     */
    fun isTallLayout(size: WidgetSize): Boolean {
        return size.height >= 3
    }

    /**
     * 获取布局方向建议
     */
    fun getLayoutOrientation(size: WidgetSize): LayoutOrientation {
        return when {
            size.width > size.height -> LayoutOrientation.HORIZONTAL
            size.height > size.width -> LayoutOrientation.VERTICAL
            else -> LayoutOrientation.SQUARE
        }
    }

    /**
     * 布局方向
     */
    enum class LayoutOrientation {
        HORIZONTAL,
        VERTICAL,
        SQUARE
    }

    /**
     * 工具方法：dp转px
     */
    fun dpToPx(@Dimension(unit = Dimension.DP) dp: Int): Int {
        return TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP,
            dp.toFloat(),
            displayMetrics
        ).roundToInt()
    }

    /**
     * 工具方法：px转dp
     */
    fun pxToDp(px: Int): Int {
        return (px / displayMetrics.density).roundToInt()
    }

    /**
     * 工具方法：sp转px
     */
    fun spToPx(@Dimension(unit = Dimension.SP) sp: Float): Int {
        return TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_SP,
            sp,
            displayMetrics
        ).roundToInt()
    }

    /**
     * 获取屏幕密度分类
     */
    fun getDensityCategory(): DensityCategory {
        return when (displayMetrics.densityDpi) {
            in 0..120 -> DensityCategory.LDPI
            in 121..160 -> DensityCategory.MDPI
            in 161..240 -> DensityCategory.HDPI
            in 241..320 -> DensityCategory.XHDPI
            in 321..480 -> DensityCategory.XXHDPI
            else -> DensityCategory.XXXHDPI
        }
    }

    /**
     * 屏幕密度分类
     */
    enum class DensityCategory {
        LDPI,    // ~120dpi
        MDPI,    // ~160dpi
        HDPI,    // ~240dpi
        XHDPI,   // ~320dpi
        XXHDPI,  // ~480dpi
        XXXHDPI  // ~640dpi
    }

    /**
     * 获取适配建议
     */
    fun getAdaptationSuggestions(size: WidgetSize): List<String> {
        val suggestions = mutableListOf<String>()
        val config = getLayoutConfig(size)

        when (size) {
            WidgetSize.SMALL -> {
                suggestions.add("使用简洁的图标和数字")
                suggestions.add("避免过多文字")
                suggestions.add("优先显示最重要的信息")
            }
            WidgetSize.MEDIUM_HORIZONTAL -> {
                suggestions.add("采用横向布局")
                suggestions.add("可以显示2-3个关键信息")
                suggestions.add("使用进度条等线性元素")
            }
            WidgetSize.MEDIUM -> {
                suggestions.add("平衡信息密度和可读性")
                suggestions.add("可以包含小型图表")
                suggestions.add("支持基本交互")
            }
            WidgetSize.LARGE_HORIZONTAL -> {
                suggestions.add("充分利用横向空间")
                suggestions.add("可以显示详细统计信息")
                suggestions.add("支持多个操作按钮")
            }
            WidgetSize.LARGE -> {
                suggestions.add("可以显示丰富的内容")
                suggestions.add("支持复杂的数据可视化")
                suggestions.add("提供完整的功能体验")
            }
            WidgetSize.EXTRA_LARGE -> {
                suggestions.add("提供最完整的功能")
                suggestions.add("可以包含多个区域")
                suggestions.add("支持高级交互和动画")
            }
        }

        return suggestions
    }
}