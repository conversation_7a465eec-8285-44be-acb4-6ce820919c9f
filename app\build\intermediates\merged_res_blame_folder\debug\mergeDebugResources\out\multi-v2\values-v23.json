{"logs": [{"outputFile": "com.timeflow.app-mergeDebugResources-85:/values-v23/values-v23.xml", "map": [{"source": "D:\\development\\Codes\\MyApplication\\app\\src\\main\\res\\values-v23\\bools.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "104", "endColumns": "57", "endOffsets": "157"}, "to": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "57", "endOffsets": "108"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\6245402d8a114e85318ed4c000100fb2\\transformed\\cardview-1.0.0\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "4", "endColumns": "12", "endOffsets": "200"}, "to": {"startLines": "53", "startColumns": "4", "startOffsets": "3419", "endLines": "55", "endColumns": "12", "endOffsets": "3564"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\2e72a71af7cea86f1e691bad46c56138\\transformed\\appcompat-1.6.1\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2,3,4,5,6,20,34,35,36,39,43,44,45,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,190,325,400,487,1225,1975,2094,2221,2443,2667,2782,2889,3002", "endLines": "2,3,4,5,19,33,34,35,38,42,43,44,45,49", "endColumns": "134,134,74,86,12,12,118,126,12,12,114,106,112,12", "endOffsets": "185,320,395,482,1220,1970,2089,2216,2438,2662,2777,2884,2997,3227"}, "to": {"startLines": "5,6,7,8,9,23,37,38,39,42,46,47,48,49", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "242,377,512,587,674,1412,2162,2281,2408,2630,2854,2969,3076,3189", "endLines": "5,6,7,8,22,36,37,38,41,45,46,47,48,52", "endColumns": "134,134,74,86,12,12,118,126,12,12,114,106,112,12", "endOffsets": "372,507,582,669,1407,2157,2276,2403,2625,2849,2964,3071,3184,3414"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\8502c7d6e294f2825540546f57fbfcc0\\transformed\\material-1.11.0\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2,5,9,13,16,19,22,25,28,32", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,271,413,574,784,991,1198,1401,1603,1868", "endLines": "4,8,12,15,18,21,24,27,31,35", "endColumns": "10,10,10,10,10,10,10,10,10,10", "endOffsets": "266,408,569,779,986,1193,1396,1598,1863,2136"}, "to": {"startLines": "56,59,63,67,70,73,76,79,82,86", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "3569,3785,3927,4088,4298,4505,4712,4915,5117,5382", "endLines": "58,62,66,69,72,75,78,81,85,89", "endColumns": "10,10,10,10,10,10,10,10,10,10", "endOffsets": "3780,3922,4083,4293,4500,4707,4910,5112,5377,5650"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\75ac900f6bf42e90e2b331a4ef619a90\\transformed\\work-runtime-2.9.0\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,121", "endColumns": "65,62", "endOffsets": "116,179"}, "to": {"startLines": "3,4", "startColumns": "4,4", "startOffsets": "113,179", "endColumns": "65,62", "endOffsets": "174,237"}}]}]}