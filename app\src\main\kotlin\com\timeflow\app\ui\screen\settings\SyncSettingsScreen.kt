package com.timeflow.app.ui.screen.settings

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavController
import com.timeflow.app.R
import com.timeflow.app.ui.theme.*
import com.timeflow.app.utils.SystemBarManager
import android.app.Activity

/**
 * 同步设置页面 - 支持S3等云存储同步
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SyncSettingsScreen(
    navController: NavController,
    viewModel: SyncSettingsViewModel = hiltViewModel()
) {
    val context = LocalContext.current
    val activity = context as? Activity
    
    // 获取同步状态
    val syncSettings by viewModel.syncSettings.collectAsState()
    val isConnecting by viewModel.isConnecting.collectAsState()
    val syncStatus by viewModel.syncStatus.collectAsState()
    val errorMessage by viewModel.errorMessage.collectAsState()
    val isSyncing by viewModel.isSyncing.collectAsState()
    
    // 处理状态栏
    SideEffect {
        activity?.let { 
            SystemBarManager.forceOpaqueStatusBar(it, false)
        }
    }
    
    // 显示错误消息
    errorMessage?.let { message ->
        LaunchedEffect(message) {
            // 这里可以显示Snackbar或Toast
            // 暂时只是清除错误
            viewModel.clearError()
        }
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFF8F8F8))
            .padding(top = SystemBarManager.getFixedStatusBarHeight())
    ) {
        // 顶部标题和返回按钮
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(
                onClick = { navController.navigateUp() },
                modifier = Modifier
                    .size(36.dp)
                    .background(Color.LightGray.copy(alpha = 0.2f), CircleShape)
            ) {
                Icon(
                    imageVector = Icons.Filled.ArrowBack,
                    contentDescription = "返回",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(20.dp)
                )
            }
            
            Text(
                text = "同步设置",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )
            
            Box(modifier = Modifier.size(36.dp))
        }
        
        // 滚动内容
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(horizontal = 16.dp)
        ) {
            Spacer(modifier = Modifier.height(16.dp))
            
            // 同步状态卡片
            SyncStatusCard(
                isEnabled = syncSettings.isEnabled,
                syncStatus = syncStatus,
                lastSyncTime = syncSettings.lastSyncTime,
                onToggleSync = { enabled -> viewModel.toggleSync(enabled) },
                onManualSync = { viewModel.manualSync() },
                isConnecting = isConnecting || isSyncing
            )
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // 云存储配置区域
            Text(
                text = stringResource(R.string.cloud_storage_config),
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.primary,
                modifier = Modifier.padding(bottom = 12.dp)
            )

            CloudStorageConfigCard(
                config = syncSettings.getEffectiveCloudStorageConfig() ?: CloudStorageConfigData(),
                onConfigUpdate = { config ->
                    viewModel.updateCloudStorageConfig(config)
                },
                onTestConnection = { viewModel.testConnection() },
                onSaveConfig = { config ->
                    viewModel.saveCloudStorageConfig(config)
                },
                onScanAndMigrate = { config ->
                    viewModel.scanAndMigrateResources(config)
                },
                isConnecting = isConnecting
            )
            
            Spacer(modifier = Modifier.height(100.dp))
        }
    }
}

/**
 * 同步状态卡片
 */
@Composable
private fun SyncStatusCard(
    isEnabled: Boolean,
    syncStatus: SyncStatus,
    lastSyncTime: String?,
    onToggleSync: (Boolean) -> Unit,
    onManualSync: () -> Unit,
    isConnecting: Boolean
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = stringResource(R.string.data_sync),
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.SemiBold,
                        color = MaterialTheme.colorScheme.primary
                    )
                    Text(
                        text = if (isEnabled) stringResource(R.string.sync_enabled) else stringResource(R.string.sync_disabled),
                        style = MaterialTheme.typography.bodySmall,
                        color = if (isEnabled) DustyLavender else Color.Gray
                    )
                }
                
                Switch(
                    checked = isEnabled,
                    onCheckedChange = { enabled -> onToggleSync(enabled) },
                    colors = SwitchDefaults.colors(
                        checkedThumbColor = Color.White,
                        checkedTrackColor = DustyLavender,
                        uncheckedThumbColor = Color.White,
                        uncheckedTrackColor = Color.Gray
                    )
                )
            }
            
            if (isEnabled) {
                Spacer(modifier = Modifier.height(16.dp))
                
                // 同步状态指示器
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = when (syncStatus) {
                            SyncStatus.CONNECTED -> Icons.Default.CheckCircle
                            SyncStatus.CONNECTING -> Icons.Default.Sync
                            SyncStatus.ERROR -> Icons.Default.Error
                            SyncStatus.DISCONNECTED -> Icons.Default.Schedule
                        },
                        contentDescription = null,
                        tint = when (syncStatus) {
                            SyncStatus.CONNECTED -> DustyLavender
                            SyncStatus.CONNECTING -> Color(0xFF2196F3)
                            SyncStatus.ERROR -> Color(0xFFE57373)
                            SyncStatus.DISCONNECTED -> Color.Gray
                        },
                        modifier = Modifier.size(20.dp)
                    )
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Column {
                        Text(
                            text = when (syncStatus) {
                                SyncStatus.CONNECTED -> stringResource(R.string.connected)
                                SyncStatus.CONNECTING -> stringResource(R.string.connecting)
                                SyncStatus.ERROR -> stringResource(R.string.connection_failed)
                                SyncStatus.DISCONNECTED -> stringResource(R.string.disconnected)
                            },
                            style = MaterialTheme.typography.bodyMedium,
                            fontWeight = FontWeight.Medium,
                            color = MaterialTheme.colorScheme.primary
                        )
                        
                        if (lastSyncTime != null && syncStatus == SyncStatus.CONNECTED) {
                            Text(
                                text = stringResource(R.string.last_sync_time_format, lastSyncTime),
                                style = MaterialTheme.typography.bodySmall,
                                color = Color.Gray
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 手动同步按钮
                Button(
                    onClick = onManualSync,
                    enabled = !isConnecting && syncStatus == SyncStatus.CONNECTED,
                    modifier = Modifier.fillMaxWidth(),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = DustyLavender
                    )
                ) {
                    if (isConnecting) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            color = Color.White,
                            strokeWidth = 2.dp
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(stringResource(R.string.sync_in_progress))
                    } else {
                        Icon(
                            imageVector = Icons.Default.Sync,
                            contentDescription = null,
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(stringResource(R.string.sync_now))
                    }
                }
            }
        }
    }
}

/**
 * 云存储配置卡片
 */
@Composable
private fun CloudStorageConfigCard(
    config: CloudStorageConfigData,
    onConfigUpdate: (CloudStorageConfigData) -> Unit,
    onTestConnection: () -> Unit,
    onSaveConfig: (CloudStorageConfigData) -> Unit,
    onScanAndMigrate: (CloudStorageConfigData) -> Unit,
    isConnecting: Boolean
) {
    var showPassword by remember { mutableStateOf(false) }

    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.background),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            // 云服务提供商显示和选择
            val provider = try {
                CloudProvider.valueOf(config.provider)
            } catch (e: Exception) {
                CloudProvider.AWS_S3
            }

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "${provider.displayName} 配置",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.primary
                )

                // 云服务提供商选择下拉菜单
                var expanded by remember { mutableStateOf(false) }

                Box {
                    OutlinedButton(
                        onClick = { expanded = true },
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = MaterialTheme.colorScheme.primary
                        )
                    ) {
                        Text(text = provider.displayName)
                        Icon(
                            imageVector = Icons.Default.ArrowDropDown,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp)
                        )
                    }

                    DropdownMenu(
                        expanded = expanded,
                        onDismissRequest = { expanded = false }
                    ) {
                        CloudProvider.values().forEach { cloudProvider ->
                            DropdownMenuItem(
                                text = { Text(cloudProvider.displayName) },
                                onClick = {
                                    onConfigUpdate(config.copy(
                                        provider = cloudProvider.name,
                                        region = getDefaultRegion(cloudProvider),
                                        endpoint = getDefaultEndpoint(cloudProvider)
                                    ))
                                    expanded = false
                                }
                            )
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))
            
            // 区域节点 (Endpoint)
            OutlinedTextField(
                value = config.endpoint,
                onValueChange = {
                    onConfigUpdate(config.copy(endpoint = it))
                },
                label = { Text("区域节点(Endpoint)") },
                placeholder = {
                    Text(
                        text = "格式不要以 http 开头，只需要填写对应域名即可，如${provider.endpointExample}",
                        style = MaterialTheme.typography.bodySmall
                    )
                },
                modifier = Modifier.fillMaxWidth(),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = MaterialTheme.colorScheme.primary,
                    focusedLabelColor = MaterialTheme.colorScheme.primary
                )
            )

            Spacer(modifier = Modifier.height(12.dp))

            // 存储桶 (Bucket)
            OutlinedTextField(
                value = config.bucketName,
                onValueChange = {
                    onConfigUpdate(config.copy(bucketName = it))
                },
                label = { Text("存储桶(Bucket)") },
                modifier = Modifier.fillMaxWidth(),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = MaterialTheme.colorScheme.primary,
                    focusedLabelColor = MaterialTheme.colorScheme.primary
                )
            )

            Spacer(modifier = Modifier.height(12.dp))

            // 访问密钥 (accessKeyId)
            OutlinedTextField(
                value = config.accessKeyId,
                onValueChange = {
                    onConfigUpdate(config.copy(accessKeyId = it))
                },
                label = { Text("访问密钥(accessKeyId)") },
                modifier = Modifier.fillMaxWidth(),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = MaterialTheme.colorScheme.primary,
                    focusedLabelColor = MaterialTheme.colorScheme.primary
                )
            )

            Spacer(modifier = Modifier.height(12.dp))

            // 密钥 (secretAccessKey)
            OutlinedTextField(
                value = config.secretAccessKey,
                onValueChange = {
                    onConfigUpdate(config.copy(secretAccessKey = it))
                },
                label = { Text("密钥(secretAccessKey)") },
                modifier = Modifier.fillMaxWidth(),
                visualTransformation = if (showPassword) VisualTransformation.None else PasswordVisualTransformation(),
                trailingIcon = {
                    IconButton(onClick = { showPassword = !showPassword }) {
                        Icon(
                            imageVector = if (showPassword) Icons.Default.VisibilityOff else Icons.Default.Visibility,
                            contentDescription = if (showPassword) "隐藏密码" else "显示密码"
                        )
                    }
                },
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = MaterialTheme.colorScheme.primary,
                    focusedLabelColor = MaterialTheme.colorScheme.primary
                )
            )

            Spacer(modifier = Modifier.height(12.dp))

            // 地区
            OutlinedTextField(
                value = config.region,
                onValueChange = {
                    onConfigUpdate(config.copy(region = it))
                },
                label = { Text("地区") },
                modifier = Modifier.fillMaxWidth(),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = MaterialTheme.colorScheme.primary,
                    focusedLabelColor = MaterialTheme.colorScheme.primary
                )
            )
            
            Spacer(modifier = Modifier.height(24.dp))

            // 配置按钮
            Button(
                onClick = {
                    // 保存当前配置
                    onSaveConfig(config)
                },
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFF2196F3)
                ),
                shape = RoundedCornerShape(8.dp)
            ) {
                Text(
                    text = "保存配置",
                    color = Color.White,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.padding(vertical = 4.dp)
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 测试连接按钮
            OutlinedButton(
                onClick = onTestConnection,
                enabled = !isConnecting &&
                    config.accessKeyId.isNotBlank() &&
                    config.secretAccessKey.isNotBlank() &&
                    config.bucketName.isNotBlank(),
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.outlinedButtonColors(
                    contentColor = Color(0xFF2196F3)
                ),
                shape = RoundedCornerShape(8.dp)
            ) {
                if (isConnecting) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        color = Color(0xFF2196F3),
                        strokeWidth = 2.dp
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("测试中...")
                } else {
                    Icon(
                        imageVector = Icons.Default.CloudSync,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "测试连接",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium,
                        modifier = Modifier.padding(vertical = 4.dp)
                    )
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            // 扫描并迁移资源到S3按钮
            Button(
                onClick = {
                    // 调用资源迁移功能
                    onScanAndMigrate(config)
                },
                enabled = !isConnecting &&
                    config.accessKeyId.isNotBlank() &&
                    config.secretAccessKey.isNotBlank() &&
                    config.bucketName.isNotBlank(),
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFF4CAF50)
                ),
                shape = RoundedCornerShape(8.dp)
            ) {
                if (isConnecting) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        color = Color.White,
                        strokeWidth = 2.dp
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("迁移中...")
                } else {
                    Icon(
                        imageVector = Icons.Default.CloudUpload,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "扫描并迁移资源到S3",
                        color = Color.White,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium,
                        modifier = Modifier.padding(vertical = 4.dp)
                    )
                }
            }
        }
    }
}

/**
 * 获取默认区域
 */
private fun getDefaultRegion(provider: CloudProvider): String {
    return when (provider) {
        CloudProvider.AWS_S3 -> "us-east-1"
        CloudProvider.QINIU_CLOUD -> "cn-east-1"
        CloudProvider.TENCENT_CLOUD -> "ap-beijing"
        CloudProvider.ALIYUN_OSS -> "cn-hangzhou"
    }
}

/**
 * 获取默认端点
 */
private fun getDefaultEndpoint(provider: CloudProvider): String {
    return when (provider) {
        CloudProvider.AWS_S3 -> "s3.amazonaws.com"
        CloudProvider.QINIU_CLOUD -> "s3-cn-east-1.qiniucs.com"
        CloudProvider.TENCENT_CLOUD -> "cos.ap-beijing.myqcloud.com"
        CloudProvider.ALIYUN_OSS -> "oss-cn-hangzhou.aliyuncs.com"
    }
}

// 注意：数据模型定义在SyncSettingsViewModel.kt中