package com.timeflow.app.data.service;

import com.timeflow.app.data.repository.TaskRepository;
import com.timeflow.app.ui.screen.reflection.ReflectionRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SimpleRealDataStatisticsService_Factory implements Factory<SimpleRealDataStatisticsService> {
  private final Provider<TaskRepository> taskRepositoryProvider;

  private final Provider<ReflectionRepository> reflectionRepositoryProvider;

  public SimpleRealDataStatisticsService_Factory(Provider<TaskRepository> taskRepositoryProvider,
      Provider<ReflectionRepository> reflectionRepositoryProvider) {
    this.taskRepositoryProvider = taskRepositoryProvider;
    this.reflectionRepositoryProvider = reflectionRepositoryProvider;
  }

  @Override
  public SimpleRealDataStatisticsService get() {
    return newInstance(taskRepositoryProvider.get(), reflectionRepositoryProvider.get());
  }

  public static SimpleRealDataStatisticsService_Factory create(
      Provider<TaskRepository> taskRepositoryProvider,
      Provider<ReflectionRepository> reflectionRepositoryProvider) {
    return new SimpleRealDataStatisticsService_Factory(taskRepositoryProvider, reflectionRepositoryProvider);
  }

  public static SimpleRealDataStatisticsService newInstance(TaskRepository taskRepository,
      ReflectionRepository reflectionRepository) {
    return new SimpleRealDataStatisticsService(taskRepository, reflectionRepository);
  }
}
