package com.timeflow.app.widget.data

/**
 * 今日任务数据类
 */
data class TodayTasksData(
    val tasks: List<DisplayTask> = emptyList(),
    val totalTasks: Int = 0,
    val totalCompleted: Int = 0,
    val totalIncomplete: Int = 0
) {
    fun getDisplayTasks(): List<DisplayTask> {
        return tasks.filter { !it.isCompleted }.take(5)
    }
    
    fun getCompletionPercentage(): Int {
        return if (totalTasks > 0) {
            (totalCompleted * 100 / totalTasks)
        } else {
            0
        }
    }
}

/**
 * 显示任务数据类
 */
data class DisplayTask(
    val id: String,
    val title: String,
    val isCompleted: Boolean,
    val priority: String,
    val dueTime: String? = null
)

/**
 * 简单任务数据（用于默认显示）
 */
data class SimpleTask(
    val title: String,
    val isCompleted: Boolean
)

/**
 * 带优先级的简单任务数据
 */
data class SimpleTaskWithPriority(
    val id: String? = null,
    val title: String,
    val isCompleted: Boolean,
    val priority: String = "NORMAL"
)