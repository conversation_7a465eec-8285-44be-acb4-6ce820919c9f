/ Header Record For PersistentHashMapValueStorage$ #androidx.activity.ComponentActivity= android.app.Application$androidx.work.Configuration.Provider timber.log.Timber.Tree kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum androidx.room.RoomDatabase3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer java.io.Serializable kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer java.lang.Exception- ,com.timeflow.app.data.model.PaymentException- ,com.timeflow.app.data.model.PaymentException- ,com.timeflow.app.data.model.PaymentException- ,com.timeflow.app.data.model.PaymentException- ,com.timeflow.app.data.model.PaymentException- ,com.timeflow.app.data.model.PaymentException- ,com.timeflow.app.data.model.PaymentException kotlin.Enum kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum kotlin.Enum kotlin.Enum) (com.timeflow.app.data.model.EndCondition) (com.timeflow.app.data.model.EndCondition) (com.timeflow.app.data.model.EndCondition kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum% $com.timeflow.app.data.model.TaskType% $com.timeflow.app.data.model.TaskType% $com.timeflow.app.data.model.TaskType% $com.timeflow.app.data.model.TaskType kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum androidx.work.CoroutineWorker androidx.work.CoroutineWorker androidx.work.CoroutineWorker androidx.work.CoroutineWorker androidx.work.CoroutineWorker androidx.work.CoroutineWorker2 1com.timeflow.app.data.repository.AiTaskRepository9 8com.timeflow.app.data.repository.EmotionRecordRepository1 0com.timeflow.app.data.repository.EventRepository0 /com.timeflow.app.data.repository.GoalRepository8 7com.timeflow.app.data.repository.GoalTemplateRepository1 0com.timeflow.app.data.repository.HabitRepository7 6com.timeflow.app.data.repository.KanbanBoardRepository8 7com.timeflow.app.data.repository.KanbanColumnRepository6 5com.timeflow.app.data.repository.MedicationRepository3 2kotlinx.serialization.internal.GeneratedSerializer` /com.timeflow.app.data.repository.TaskRepository/com.timeflow.app.data.repository.BaseRepository9 8com.timeflow.app.data.repository.TimeAnalyticsRepository: 9com.timeflow.app.data.repository.UserPreferenceRepository0 /com.timeflow.app.data.repository.WishRepository% $com.timeflow.app.di.AnalyticsTracker" !com.timeflow.app.di.CrashReporter kotlin.Annotation kotlin.Annotation kotlin.Annotation kotlin.Annotation androidx.startup.Initializer androidx.startup.Initializer" !android.content.BroadcastReceiver" !android.content.BroadcastReceiver" !android.content.BroadcastReceiver" !android.content.BroadcastReceiver" !android.content.BroadcastReceiver" !android.content.BroadcastReceiver android.app.Service android.app.Service" !android.content.BroadcastReceiver" !android.content.BroadcastReceiver android.app.Service android.app.Service android.os.Binder android.app.Service android.os.Binder$ #androidx.activity.ComponentActivity androidx.lifecycle.ViewModel# "com.timeflow.app.ui.base.ViewState# "com.timeflow.app.ui.base.ViewState# "com.timeflow.app.ui.base.ViewState# "com.timeflow.app.ui.base.ViewState kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum& %com.timeflow.app.ui.navigation.Screen& %com.timeflow.app.ui.navigation.Screen& %com.timeflow.app.ui.navigation.Screen& %com.timeflow.app.ui.navigation.Screen& %com.timeflow.app.ui.navigation.Screen& %com.timeflow.app.ui.navigation.Screen& %com.timeflow.app.ui.navigation.Screen& %com.timeflow.app.ui.navigation.Screen& %com.timeflow.app.ui.navigation.Screen& %com.timeflow.app.ui.navigation.Screen1 0com.timeflow.app.ui.navigation.TimeFlowNavigator kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel kotlin.Enum kotlin.EnumA @androidx.compose.foundation.interaction.MutableInteractionSource androidx.lifecycle.ViewModelF Ecom.timeflow.app.ui.screen.calendar.CalendarViewModel.CalendarUiEventF Ecom.timeflow.app.ui.screen.calendar.CalendarViewModel.CalendarUiEvent androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel0 /com.timeflow.app.data.repository.GoalRepository0 /com.timeflow.app.ui.screen.goal.TemplateUiState0 /com.timeflow.app.ui.screen.goal.TemplateUiState0 /com.timeflow.app.ui.screen.goal.TemplateUiState0 /com.timeflow.app.ui.screen.goal.TemplateUiState kotlin.Enum androidx.lifecycle.ViewModel$ #androidx.lifecycle.AndroidViewModel, +com.timeflow.app.ui.screen.goal.GoalUiState, +com.timeflow.app.ui.screen.goal.GoalUiState, +com.timeflow.app.ui.screen.goal.GoalUiState, +com.timeflow.app.ui.screen.goal.GoalUiState/ .com.timeflow.app.ui.screen.goal.BreakdownState/ .com.timeflow.app.ui.screen.goal.BreakdownState/ .com.timeflow.app.ui.screen.goal.BreakdownState/ .com.timeflow.app.ui.screen.goal.BreakdownState/ .com.timeflow.app.ui.screen.goal.BreakdownState* )com.timeflow.app.ui.screen.goal.GoalState* )com.timeflow.app.ui.screen.goal.GoalState* )com.timeflow.app.ui.screen.goal.GoalState androidx.lifecycle.ViewModel kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel" kotlin.Enumandroid.os.Parcelable android.os.Parcelable androidx.lifecycle.ViewModel kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel; :com.timeflow.app.ui.screen.reflection.ReflectionRepository> =com.timeflow.app.ui.screen.reflection.SearchSuggestionService kotlin.Enum kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel kotlin.Enum androidx.lifecycle.ViewModel kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer7 6com.timeflow.app.ui.screen.settings.CloudStorageClient7 6com.timeflow.app.ui.screen.settings.CloudStorageClient7 6com.timeflow.app.ui.screen.settings.CloudStorageClient7 6com.timeflow.app.ui.screen.settings.CloudStorageClient3 2com.timeflow.app.ui.screen.settings.SyncRepository androidx.lifecycle.ViewModel kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel2 1com.timeflow.app.ui.screen.task.TaskDetailUiState2 1com.timeflow.app.ui.screen.task.TaskDetailUiState2 1com.timeflow.app.ui.screen.task.TaskDetailUiState2 1com.timeflow.app.ui.screen.task.TaskDetailUiStateA @androidx.compose.foundation.interaction.MutableInteractionSource kotlin.Enum kotlin.Enum kotlin.Annotation kotlin.Annotation kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel: 9com.timeflow.app.ui.screen.task.TaskViewModel.TaskUiState: 9com.timeflow.app.ui.screen.task.TaskViewModel.TaskUiState: 9com.timeflow.app.ui.screen.task.TaskViewModel.TaskUiState kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel kotlin.Enum androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel kotlin.Enum kotlin.Enum kotlin.Enum0 /com.timeflow.app.data.repository.TaskRepositoryD Ccom.timeflow.app.ui.task.components.common.event.AppEvent.BaseEventD Ccom.timeflow.app.ui.task.components.common.event.AppEvent.BaseEventD Ccom.timeflow.app.ui.task.components.common.event.AppEvent.BaseEventD Ccom.timeflow.app.ui.task.components.common.event.AppEvent.BaseEventD Ccom.timeflow.app.ui.task.components.common.event.AppEvent.BaseEventD Ccom.timeflow.app.ui.task.components.common.event.AppEvent.BaseEventD Ccom.timeflow.app.ui.task.components.common.event.AppEvent.BaseEventD Ccom.timeflow.app.ui.task.components.common.event.AppEvent.BaseEventD Ccom.timeflow.app.ui.task.components.common.event.AppEvent.BaseEventD Ccom.timeflow.app.ui.task.components.common.event.AppEvent.BaseEventD Ccom.timeflow.app.ui.task.components.common.event.AppEvent.BaseEventD Ccom.timeflow.app.ui.task.components.common.event.AppEvent.BaseEventD Ccom.timeflow.app.ui.task.components.common.event.AppEvent.BaseEventD Ccom.timeflow.app.ui.task.components.common.event.AppEvent.BaseEventD Ccom.timeflow.app.ui.task.components.common.event.AppEvent.BaseEventD Ccom.timeflow.app.ui.task.components.common.event.AppEvent.BaseEventD Ccom.timeflow.app.ui.task.components.common.event.AppEvent.BaseEventD Ccom.timeflow.app.ui.task.components.common.event.AppEvent.BaseEventD Ccom.timeflow.app.ui.task.components.common.event.AppEvent.BaseEventD Ccom.timeflow.app.ui.task.components.common.event.AppEvent.BaseEventD Ccom.timeflow.app.ui.task.components.common.event.AppEvent.BaseEventD Ccom.timeflow.app.ui.task.components.common.event.AppEvent.BaseEventD Ccom.timeflow.app.ui.task.components.common.event.AppEvent.BaseEventD Ccom.timeflow.app.ui.task.components.common.event.AppEvent.BaseEventD Ccom.timeflow.app.ui.task.components.common.event.AppEvent.BaseEventD Ccom.timeflow.app.ui.task.components.common.event.AppEvent.BaseEventD Ccom.timeflow.app.ui.task.components.common.event.AppEvent.BaseEventD Ccom.timeflow.app.ui.task.components.common.event.AppEvent.BaseEventD Ccom.timeflow.app.ui.task.components.common.event.AppEvent.BaseEventD Ccom.timeflow.app.ui.task.components.common.event.AppEvent.BaseEvent; :com.timeflow.app.ui.task.components.common.state.LoadState; :com.timeflow.app.ui.task.components.common.state.LoadState; :com.timeflow.app.ui.task.components.common.state.LoadState; :com.timeflow.app.ui.task.components.common.state.LoadState kotlin.Enum kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel kotlin.Enum kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModelH Gcom.timeflow.app.ui.viewmodel.GoalCreationViewModel.GoalCreationUiStateH Gcom.timeflow.app.ui.viewmodel.GoalCreationViewModel.GoalCreationUiStateH Gcom.timeflow.app.ui.viewmodel.GoalCreationViewModel.GoalCreationUiStateH Gcom.timeflow.app.ui.viewmodel.GoalCreationViewModel.GoalCreationUiStateH Gcom.timeflow.app.ui.viewmodel.GoalCreationViewModel.GoalCreationUiState androidx.lifecycle.ViewModel* )com.timeflow.app.ui.viewmodel.GoalUiState* )com.timeflow.app.ui.viewmodel.GoalUiState* )com.timeflow.app.ui.viewmodel.GoalUiState* )com.timeflow.app.ui.viewmodel.GoalUiState androidx.lifecycle.ViewModel kotlin.Enum androidx.lifecycle.ViewModel/ .com.timeflow.app.ui.viewmodel.ValidationResult/ .com.timeflow.app.ui.viewmodel.ValidationResult/ .com.timeflow.app.ui.viewmodel.ValidationResult2 1com.timeflow.app.ui.viewmodel.ConflictCheckResult2 1com.timeflow.app.ui.viewmodel.ConflictCheckResult2 1com.timeflow.app.ui.viewmodel.ConflictCheckResult. -com.timeflow.app.ui.viewmodel.OperationResult. -com.timeflow.app.ui.viewmodel.OperationResult. -com.timeflow.app.ui.viewmodel.OperationResult. -com.timeflow.app.ui.viewmodel.OperationResult. -com.timeflow.app.ui.viewmodel.OperationResult androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel kotlin.Enum* )java.lang.Thread.UncaughtExceptionHandler kotlin.Enum timber.log.Timber.Tree kotlin.Enum kotlin.Enum kotlin.Enum" !android.content.BroadcastReceiver androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel3 2kotlinx.serialization.internal.GeneratedSerializer$ #android.appwidget.AppWidgetProvider$ #android.appwidget.AppWidgetProvider$ #android.appwidget.AppWidgetProvider$ #android.appwidget.AppWidgetProvider$ #android.appwidget.AppWidgetProvider$ #android.appwidget.AppWidgetProvider androidx.work.CoroutineWorker androidx.work.CoroutineWorker androidx.work.CoroutineWorker androidx.work.CoroutineWorker androidx.work.CoroutineWorker androidx.work.CoroutineWorker androidx.work.CoroutineWorker