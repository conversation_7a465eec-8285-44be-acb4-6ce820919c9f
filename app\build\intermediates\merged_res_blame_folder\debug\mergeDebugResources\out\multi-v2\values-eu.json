{"logs": [{"outputFile": "com.timeflow.app-mergeDebugResources-85:/values-eu/values-eu.xml", "map": [{"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\e8414bc356727da52467a60536d06b50\\transformed\\material3-release\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,202,349,476,621,750,848,963,1103,1222,1367,1451,1556,1652,1752,1871,1992,2102,2245,2389,2524,2715,2840,2962,3086,3208,3305,3402,3530,3665,3763,3866,3972,4119,4270,4378,4478,4554,4650,4745,4832,4920,5030,5110,5195,5290,5393,5484,5583,5672,5780,5880,5986,6104,6184,6288", "endColumns": "146,146,126,144,128,97,114,139,118,144,83,104,95,99,118,120,109,142,143,134,190,124,121,123,121,96,96,127,134,97,102,105,146,150,107,99,75,95,94,86,87,109,79,84,94,102,90,98,88,107,99,105,117,79,103,94", "endOffsets": "197,344,471,616,745,843,958,1098,1217,1362,1446,1551,1647,1747,1866,1987,2097,2240,2384,2519,2710,2835,2957,3081,3203,3300,3397,3525,3660,3758,3861,3967,4114,4265,4373,4473,4549,4645,4740,4827,4915,5025,5105,5190,5285,5388,5479,5578,5667,5775,5875,5981,6099,6179,6283,6378"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5693,5840,5987,6114,6259,6388,6486,6601,6741,6860,7005,7089,7194,7290,7390,7509,7630,7740,7883,8027,8162,8353,8478,8600,8724,8846,8943,9040,9168,9303,9401,9504,9610,9757,9908,10016,10116,10192,10288,10383,10470,10558,10668,10748,10833,10928,11031,11122,11221,11310,11418,11518,11624,11742,11822,11926", "endColumns": "146,146,126,144,128,97,114,139,118,144,83,104,95,99,118,120,109,142,143,134,190,124,121,123,121,96,96,127,134,97,102,105,146,150,107,99,75,95,94,86,87,109,79,84,94,102,90,98,88,107,99,105,117,79,103,94", "endOffsets": "5835,5982,6109,6254,6383,6481,6596,6736,6855,7000,7084,7189,7285,7385,7504,7625,7735,7878,8022,8157,8348,8473,8595,8719,8841,8938,9035,9163,9298,9396,9499,9605,9752,9903,10011,10111,10187,10283,10378,10465,10553,10663,10743,10828,10923,11026,11117,11216,11305,11413,11513,11619,11737,11817,11921,12016"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\71256635af31e1aec955d3a558d276eb\\transformed\\foundation-release\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,88", "endOffsets": "136,225"}, "to": {"startLines": "188,189", "startColumns": "4,4", "startOffsets": "17905,17991", "endColumns": "85,88", "endOffsets": "17986,18075"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\443a6cfc0de72abe8b7a8ef4c7b7a3e3\\transformed\\core-1.12.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,564,667,786", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "148,251,351,454,559,662,781,882"}, "to": {"startLines": "38,39,40,41,42,43,44,183", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3569,3667,3770,3870,3973,4078,4181,17370", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "3662,3765,3865,3968,4073,4176,4295,17466"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\2e72a71af7cea86f1e691bad46c56138\\transformed\\appcompat-1.6.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,312,422,508,614,738,824,905,997,1091,1187,1281,1382,1476,1572,1669,1761,1854,1936,2045,2154,2253,2362,2469,2580,2751,2850", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "209,307,417,503,609,733,819,900,992,1086,1182,1276,1377,1471,1567,1664,1756,1849,1931,2040,2149,2248,2357,2464,2575,2746,2845,2928"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,181", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "325,434,532,642,728,834,958,1044,1125,1217,1311,1407,1501,1602,1696,1792,1889,1981,2074,2156,2265,2374,2473,2582,2689,2800,2971,17213", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "429,527,637,723,829,953,1039,1120,1212,1306,1402,1496,1597,1691,1787,1884,1976,2069,2151,2260,2369,2468,2577,2684,2795,2966,3065,17291"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\6e1bec7b61f7c5e14c57eb6146ea2199\\transformed\\ui-release\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,196,277,378,482,574,650,737,826,910,985,1057,1145,1235,1309,1386,1454", "endColumns": "90,80,100,103,91,75,86,88,83,74,71,87,89,73,76,67,119", "endOffsets": "191,272,373,477,569,645,732,821,905,980,1052,1140,1230,1304,1381,1449,1569"}, "to": {"startLines": "48,49,51,52,53,58,59,172,173,175,176,179,180,182,185,186,187", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4596,4687,4839,4940,5044,5469,5545,16419,16508,16671,16746,17035,17123,17296,17640,17717,17785", "endColumns": "90,80,100,103,91,75,86,88,83,74,71,87,89,73,76,67,119", "endOffsets": "4682,4763,4935,5039,5131,5540,5627,16503,16587,16741,16813,17118,17208,17365,17712,17780,17900"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\8502c7d6e294f2825540546f57fbfcc0\\transformed\\material-1.11.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,275,382,487,567,674,774,872,987,1070,1137,1236,1304,1365,1453,1516,1582,1646,1717,1780,1834,1943,2002,2065,2119,2193,2318,2408,2488,2633,2716,2798,2936,3027,3110,3162,3215,3281,3352,3432,3518,3598,3676,3754,3827,3902,4009,4096,4183,4274,4367,4439,4515,4607,4658,4740,4806,4890,4976,5038,5102,5165,5233,5340,5449,5545,5650,5706,5763", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,106,104,79,106,99,97,114,82,66,98,67,60,87,62,65,63,70,62,53,108,58,62,53,73,124,89,79,144,82,81,137,90,82,51,52,65,70,79,85,79,77,77,72,74,106,86,86,90,92,71,75,91,50,81,65,83,85,61,63,62,67,106,108,95,104,55,56,82", "endOffsets": "270,377,482,562,669,769,867,982,1065,1132,1231,1299,1360,1448,1511,1577,1641,1712,1775,1829,1938,1997,2060,2114,2188,2313,2403,2483,2628,2711,2793,2931,3022,3105,3157,3210,3276,3347,3427,3513,3593,3671,3749,3822,3897,4004,4091,4178,4269,4362,4434,4510,4602,4653,4735,4801,4885,4971,5033,5097,5160,5228,5335,5444,5540,5645,5701,5758,5841"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,54,56,57,60,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,177", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3070,3177,3282,3362,3469,4300,4398,4513,5136,5302,5401,5632,12021,12109,12172,12238,12302,12373,12436,12490,12599,12658,12721,12775,12849,12974,13064,13144,13289,13372,13454,13592,13683,13766,13818,13871,13937,14008,14088,14174,14254,14332,14410,14483,14558,14665,14752,14839,14930,15023,15095,15171,15263,15314,15396,15462,15546,15632,15694,15758,15821,15889,15996,16105,16201,16306,16362,16818", "endLines": "5,33,34,35,36,37,45,46,47,54,56,57,60,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,177", "endColumns": "12,106,104,79,106,99,97,114,82,66,98,67,60,87,62,65,63,70,62,53,108,58,62,53,73,124,89,79,144,82,81,137,90,82,51,52,65,70,79,85,79,77,77,72,74,106,86,86,90,92,71,75,91,50,81,65,83,85,61,63,62,67,106,108,95,104,55,56,82", "endOffsets": "320,3172,3277,3357,3464,3564,4393,4508,4591,5198,5396,5464,5688,12104,12167,12233,12297,12368,12431,12485,12594,12653,12716,12770,12844,12969,13059,13139,13284,13367,13449,13587,13678,13761,13813,13866,13932,14003,14083,14169,14249,14327,14405,14478,14553,14660,14747,14834,14925,15018,15090,15166,15258,15309,15391,15457,15541,15627,15689,15753,15816,15884,15991,16100,16196,16301,16357,16414,16896"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\5619827a7b7a666ff443af1afbf85287\\transformed\\preference-1.2.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,275,354,488,657,747", "endColumns": "70,98,78,133,168,89,83", "endOffsets": "171,270,349,483,652,742,826"}, "to": {"startLines": "50,55,174,178,184,190,191", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4768,5203,16592,16901,17471,18080,18170", "endColumns": "70,98,78,133,168,89,83", "endOffsets": "4834,5297,16666,17030,17635,18165,18249"}}]}]}