package com.timeflow.app.di;

import com.timeflow.app.data.repository.GoalRepository;
import com.timeflow.app.data.repository.HabitRepository;
import com.timeflow.app.data.repository.TaskRepository;
import com.timeflow.app.data.repository.TimeSessionRepository;
import com.timeflow.app.data.service.RealDataStatisticsService;
import com.timeflow.app.ui.screen.reflection.ReflectionRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class StatisticsModule_ProvideRealDataStatisticsServiceFactory implements Factory<RealDataStatisticsService> {
  private final Provider<TaskRepository> taskRepositoryProvider;

  private final Provider<TimeSessionRepository> timeSessionRepositoryProvider;

  private final Provider<GoalRepository> goalRepositoryProvider;

  private final Provider<HabitRepository> habitRepositoryProvider;

  private final Provider<ReflectionRepository> reflectionRepositoryProvider;

  public StatisticsModule_ProvideRealDataStatisticsServiceFactory(
      Provider<TaskRepository> taskRepositoryProvider,
      Provider<TimeSessionRepository> timeSessionRepositoryProvider,
      Provider<GoalRepository> goalRepositoryProvider,
      Provider<HabitRepository> habitRepositoryProvider,
      Provider<ReflectionRepository> reflectionRepositoryProvider) {
    this.taskRepositoryProvider = taskRepositoryProvider;
    this.timeSessionRepositoryProvider = timeSessionRepositoryProvider;
    this.goalRepositoryProvider = goalRepositoryProvider;
    this.habitRepositoryProvider = habitRepositoryProvider;
    this.reflectionRepositoryProvider = reflectionRepositoryProvider;
  }

  @Override
  public RealDataStatisticsService get() {
    return provideRealDataStatisticsService(taskRepositoryProvider.get(), timeSessionRepositoryProvider.get(), goalRepositoryProvider.get(), habitRepositoryProvider.get(), reflectionRepositoryProvider.get());
  }

  public static StatisticsModule_ProvideRealDataStatisticsServiceFactory create(
      Provider<TaskRepository> taskRepositoryProvider,
      Provider<TimeSessionRepository> timeSessionRepositoryProvider,
      Provider<GoalRepository> goalRepositoryProvider,
      Provider<HabitRepository> habitRepositoryProvider,
      Provider<ReflectionRepository> reflectionRepositoryProvider) {
    return new StatisticsModule_ProvideRealDataStatisticsServiceFactory(taskRepositoryProvider, timeSessionRepositoryProvider, goalRepositoryProvider, habitRepositoryProvider, reflectionRepositoryProvider);
  }

  public static RealDataStatisticsService provideRealDataStatisticsService(
      TaskRepository taskRepository, TimeSessionRepository timeSessionRepository,
      GoalRepository goalRepository, HabitRepository habitRepository,
      ReflectionRepository reflectionRepository) {
    return Preconditions.checkNotNullFromProvides(StatisticsModule.INSTANCE.provideRealDataStatisticsService(taskRepository, timeSessionRepository, goalRepository, habitRepository, reflectionRepository));
  }
}
