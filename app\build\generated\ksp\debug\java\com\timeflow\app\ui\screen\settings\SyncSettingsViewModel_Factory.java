package com.timeflow.app.ui.screen.settings;

import android.content.SharedPreferences;
import com.timeflow.app.data.repository.TaskRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SyncSettingsViewModel_Factory implements Factory<SyncSettingsViewModel> {
  private final Provider<SyncRepository> syncRepositoryProvider;

  private final Provider<TaskRepository> taskRepositoryProvider;

  private final Provider<SharedPreferences> sharedPreferencesProvider;

  public SyncSettingsViewModel_Factory(Provider<SyncRepository> syncRepositoryProvider,
      Provider<TaskRepository> taskRepositoryProvider,
      Provider<SharedPreferences> sharedPreferencesProvider) {
    this.syncRepositoryProvider = syncRepositoryProvider;
    this.taskRepositoryProvider = taskRepositoryProvider;
    this.sharedPreferencesProvider = sharedPreferencesProvider;
  }

  @Override
  public SyncSettingsViewModel get() {
    return newInstance(syncRepositoryProvider.get(), taskRepositoryProvider.get(), sharedPreferencesProvider.get());
  }

  public static SyncSettingsViewModel_Factory create(
      Provider<SyncRepository> syncRepositoryProvider,
      Provider<TaskRepository> taskRepositoryProvider,
      Provider<SharedPreferences> sharedPreferencesProvider) {
    return new SyncSettingsViewModel_Factory(syncRepositoryProvider, taskRepositoryProvider, sharedPreferencesProvider);
  }

  public static SyncSettingsViewModel newInstance(SyncRepository syncRepository,
      TaskRepository taskRepository, SharedPreferences sharedPreferences) {
    return new SyncSettingsViewModel(syncRepository, taskRepository, sharedPreferences);
  }
}
