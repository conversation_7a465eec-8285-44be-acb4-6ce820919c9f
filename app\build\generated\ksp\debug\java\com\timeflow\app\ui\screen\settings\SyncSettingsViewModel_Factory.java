package com.timeflow.app.ui.screen.settings;

import android.content.Context;
import android.content.SharedPreferences;
import com.timeflow.app.data.repository.TaskRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SyncSettingsViewModel_Factory implements Factory<SyncSettingsViewModel> {
  private final Provider<SyncRepository> syncRepositoryProvider;

  private final Provider<TaskRepository> taskRepositoryProvider;

  private final Provider<SharedPreferences> sharedPreferencesProvider;

  private final Provider<Context> contextProvider;

  public SyncSettingsViewModel_Factory(Provider<SyncRepository> syncRepositoryProvider,
      Provider<TaskRepository> taskRepositoryProvider,
      Provider<SharedPreferences> sharedPreferencesProvider, Provider<Context> contextProvider) {
    this.syncRepositoryProvider = syncRepositoryProvider;
    this.taskRepositoryProvider = taskRepositoryProvider;
    this.sharedPreferencesProvider = sharedPreferencesProvider;
    this.contextProvider = contextProvider;
  }

  @Override
  public SyncSettingsViewModel get() {
    return newInstance(syncRepositoryProvider.get(), taskRepositoryProvider.get(), sharedPreferencesProvider.get(), contextProvider.get());
  }

  public static SyncSettingsViewModel_Factory create(
      Provider<SyncRepository> syncRepositoryProvider,
      Provider<TaskRepository> taskRepositoryProvider,
      Provider<SharedPreferences> sharedPreferencesProvider, Provider<Context> contextProvider) {
    return new SyncSettingsViewModel_Factory(syncRepositoryProvider, taskRepositoryProvider, sharedPreferencesProvider, contextProvider);
  }

  public static SyncSettingsViewModel newInstance(SyncRepository syncRepository,
      TaskRepository taskRepository, SharedPreferences sharedPreferences, Context context) {
    return new SyncSettingsViewModel(syncRepository, taskRepository, sharedPreferences, context);
  }
}
