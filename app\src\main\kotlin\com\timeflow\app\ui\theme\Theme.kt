package com.timeflow.app.ui.theme

import android.app.Activity
import android.os.Build
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.ColorScheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.dynamicDarkColorScheme
import androidx.compose.material3.dynamicLightColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.core.view.WindowCompat
import android.view.Window
import com.timeflow.app.ui.theme.AppBackground
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import android.content.Context
import android.util.Log
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.intPreferencesKey
import androidx.datastore.preferences.core.longPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import com.timeflow.app.di.PreferenceKeys
import com.timeflow.app.ui.task.components.common.event.AppEvent
import com.timeflow.app.ui.task.components.common.event.EventBus
import com.timeflow.app.ui.settings.ThemeMode
import kotlinx.coroutines.isActive
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.delay
import android.content.res.Configuration
import kotlinx.coroutines.cancelChildren
import androidx.compose.runtime.collectAsState

// 浅色主题 - 融合iOS清爽UI与TickTick专业风格
private val LightColorScheme = lightColorScheme(
    primary = Color(0xFF007AFF),        // iOS蓝 - 主要操作按钮
    onPrimary = Color.White,
    primaryContainer = Color(0xFFE5F1FF),
    onPrimaryContainer = Color(0xFF00438A),
    
    secondary = Color(0xFF5E5CE6),      // iOS紫 - 次要操作按钮
    onSecondary = Color.White,
    secondaryContainer = Color(0xFFEAE9FF),
    onSecondaryContainer = Color(0xFF2F2A85),
    
    tertiary = Color(0xFFFF9500),       // iOS橙 - 强调元素
    onTertiary = Color.White,
    tertiaryContainer = Color(0xFFFFEACC),
    onTertiaryContainer = Color(0xFF803B00),
    
    background = Color(0xFFF9F9F9),     // iOS 背景色
    onBackground = Color(0xFF1C1C1E),   // iOS 文本色
    
    surface = Color.White,
    onSurface = Color(0xFF1C1C1E),
    surfaceVariant = Color(0xFFF2F2F7), // iOS分组表格背景色
    onSurfaceVariant = Color(0xFF747480),
    
    error = Color(0xFFFF3B30),          // iOS红 - 错误提示
    onError = Color.White,
    errorContainer = Color(0xFFFFDAD6),
    onErrorContainer = Color(0xFF410002),
    
    outline = Color(0xFFCED2D9),        // 边框颜色
)

// 深色主题 - 融合iOS暗黑模式与TickTick深色风格
private val DarkColorScheme = darkColorScheme(
    primary = Color(0xFF0A84FF),        // iOS深色模式蓝色
    onPrimary = Color.White,
    primaryContainer = Color(0xFF23395D),
    onPrimaryContainer = Color(0xFFD1E4FF),
    
    secondary = Color(0xFF5E5CE6),      // iOS深色模式紫色
    onSecondary = Color.White,
    secondaryContainer = Color(0xFF2B2E5D),
    onSecondaryContainer = Color(0xFFE0DFFF),
    
    tertiary = Color(0xFFFF9F0A),       // iOS深色模式橙色
    onTertiary = Color.Black,
    tertiaryContainer = Color(0xFF653B00),
    onTertiaryContainer = Color(0xFFFFDCC7),
    
    background = Color(0xFF1C1C1E),     // iOS深色模式背景
    onBackground = Color(0xFFEBEBF5),   // iOS深色模式文本
    
    surface = Color(0xFF2C2C2E),        // iOS深色模式卡片
    onSurface = Color(0xFFEBEBF5),      // iOS深色模式文本
    surfaceVariant = Color(0xFF3A3A3C), // iOS深色模式分组表格背景
    onSurfaceVariant = Color(0xFFAEAEB2),
    
    error = Color(0xFFFF453A),          // iOS深色模式红色
    onError = Color.Black,
    errorContainer = Color(0xFF93000A),
    onErrorContainer = Color(0xFFFFDAD6),
    
    outline = Color(0xFF48484A),        // 深色模式边框
)

// Custom theme data for user customization
data class CustomThemeData(
    val isDark: Boolean = false,
    val useDynamicTheming: Boolean = false,
    val customPrimaryColor: Color? = null,
    val customBackgroundColor: Color? = null,
    val useUnifiedBackground: Boolean = false,
    val homePageColor: Color? = null,
    val calendarPageColor: Color? = null,
    val statisticsPageColor: Color? = null, 
    val profilePageColor: Color? = null
)

// Local composition for custom theme
val LocalCustomThemeData = staticCompositionLocalOf {
    CustomThemeData()
}

/**
 * TimeFlow主题
 * 
 * 应用主题配置，支持动态取色和自定义主题
 */
@Composable
fun TimeFlowTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    customBackgroundColor: Color? = null,
    content: @Composable () -> Unit
) {
    // 获取ThemeManager中的主题偏好设置
    val themePreference by ThemeManager.userThemePreference.collectAsState()
    
    // 根据主题偏好设置创建颜色方案
    val colorScheme = if (darkTheme) {
        darkColorScheme(
            primary = themePreference.primaryColor,
            onPrimary = Color.White,
            primaryContainer = themePreference.primaryColor.copy(alpha = 0.2f),
            onPrimaryContainer = themePreference.primaryColor,
            
            secondary = themePreference.secondaryColor,
            onSecondary = Color.Black,
            secondaryContainer = themePreference.secondaryColor.copy(alpha = 0.2f),
            onSecondaryContainer = themePreference.secondaryColor,
            
            tertiary = Color(0xFFFF9500),       // iOS橙 - 强调元素
            onTertiary = Color.White,
            tertiaryContainer = Color(0xFFFFEACC),
            onTertiaryContainer = Color(0xFF803B00),
            
            background = themePreference.backgroundColor,
            onBackground = Color(0xFFEBEBF5),   // iOS深色模式文本
            
            surface = themePreference.surfaceColor,
            onSurface = Color(0xFFEBEBF5),      // iOS深色模式文本
            surfaceVariant = Color(0xFF3A3A3C), // iOS深色模式分组表格背景
            onSurfaceVariant = Color(0xFFAEAEB2),
            
            error = themePreference.errorColor,
            onError = Color.Black,
            errorContainer = Color(0xFF93000A),
            onErrorContainer = Color(0xFFFFDAD6),
            
            outline = Color(0xFF48484A),        // 深色模式边框
        )
    } else {
        lightColorScheme(
            primary = themePreference.primaryColor,
            onPrimary = Color.White,
            primaryContainer = themePreference.primaryColor.copy(alpha = 0.1f),
            onPrimaryContainer = themePreference.primaryColor,
            
            secondary = themePreference.secondaryColor,
            onSecondary = Color.White,
            secondaryContainer = themePreference.secondaryColor.copy(alpha = 0.1f),
            onSecondaryContainer = themePreference.secondaryColor,
            
            tertiary = Color(0xFFFF9500),       // iOS橙 - 强调元素
            onTertiary = Color.White,
            tertiaryContainer = Color(0xFFFFEACC),
            onTertiaryContainer = Color(0xFF803B00),
            
            background = themePreference.backgroundColor,
            onBackground = Color(0xFF1C1C1E),   // iOS 文本色
            
            surface = themePreference.surfaceColor,
            onSurface = Color(0xFF1C1C1E),
            surfaceVariant = Color(0xFFF2F2F7), // iOS分组表格背景色
            onSurfaceVariant = Color(0xFF747480),
            
            error = themePreference.errorColor,
            onError = Color.White,
            errorContainer = Color(0xFFFFDAD6),
            onErrorContainer = Color(0xFF410002),
            
            outline = Color(0xFFCED2D9),        // 边框颜色
        )
    }

    // 应用状态栏设置
    val view = LocalView.current
    val context = LocalContext.current
    
    if (!view.isInEditMode) {
        SideEffect {
            (context as? Activity)?.let { activity ->
                com.timeflow.app.utils.SystemBarManager.forceOpaqueStatusBar(
                    activity = activity,
                    lightStatusBar = !darkTheme  // 在暗黑模式下使用浅色图标(白色)，在默认模式下使用深色图标(黑色)
                )
            }
        }
    }
    
    // 提供主题数据
    CompositionLocalProvider(
        LocalCustomThemeData provides CustomThemeData(
            isDark = darkTheme,
            customBackgroundColor = customBackgroundColor
        )
    ) {
        // 如果有自定义背景色，创建新的颜色方案
        if (customBackgroundColor != null) {
            val updatedColorScheme = if (darkTheme) {
                colorScheme.copy(background = customBackgroundColor)
            } else {
                colorScheme.copy(background = customBackgroundColor)
            }
            
            MaterialTheme(
                colorScheme = updatedColorScheme,
                typography = createDynamicTypography(),
                content = content
            )
        } else {
            MaterialTheme(
                colorScheme = colorScheme,
                typography = createDynamicTypography(),
                content = content
            )
        }
    }
}

/**
 * 全屏TimeFlow应用主题
 * 增强版本，支持动态主题、自定义主题和全屏显示
 */
@Composable
fun FullscreenTimeFlowTheme(
    isDark: Boolean = isSystemInDarkTheme(),
    useDynamicTheming: Boolean = true,
    customPrimaryColor: Color? = null,
    customBackgroundColor: Color? = null,
    useUnifiedBackground: Boolean = false,
    homePageColor: Color? = null,
    calendarPageColor: Color? = null,
    statisticsPageColor: Color? = null, 
    profilePageColor: Color? = null,
    content: @Composable () -> Unit
) {
    // 设置自定义主题数据
    val customThemeData = remember(
        isDark, useDynamicTheming, customPrimaryColor, customBackgroundColor,
        useUnifiedBackground, homePageColor, calendarPageColor, statisticsPageColor, profilePageColor
    ) {
        CustomThemeData(
            isDark = isDark,
            useDynamicTheming = useDynamicTheming,
            customPrimaryColor = customPrimaryColor,
            customBackgroundColor = customBackgroundColor,
            useUnifiedBackground = useUnifiedBackground,
            homePageColor = homePageColor,
            calendarPageColor = calendarPageColor,
            statisticsPageColor = statisticsPageColor,
            profilePageColor = profilePageColor
        )
    }
    
    // 应用状态栏设置
    val view = LocalView.current
    val context = LocalContext.current
    
    if (!view.isInEditMode) {
        SideEffect {
            (context as? Activity)?.let { activity ->
                com.timeflow.app.utils.SystemBarManager.forceOpaqueStatusBar(
                    activity = activity,
                    lightStatusBar = !isDark  // 在暗黑模式下使用浅色图标(白色)，在默认模式下使用深色图标(黑色)
                )
            }
        }
    }
    
    // 提供自定义主题数据
    CompositionLocalProvider(
        LocalCustomThemeData provides customThemeData
    ) {
        // 使用基础主题
        TimeFlowTheme(
            darkTheme = isDark,
            customBackgroundColor = customBackgroundColor,
            content = content
        )
    }
}

/**
 * 任务优先级颜色
 */
object PriorityColors {
    val urgentColor = Color(0xFFFF3B30)    // 紧急 - iOS红色
    val highColor = Color(0xFFFF9500)      // 高 - iOS橙色
    val mediumColor = Color(0xFFFFCC00)    // 中 - iOS黄色
    val lowColor = Color(0xFF34C759)       // 低 - iOS绿色
    val noneColor = Color(0xFF8E8E93)      // 无 - iOS灰色
    
    /**
     * 根据优先级返回对应颜色
     */
    fun fromPriority(priority: Int): Color {
        return when (priority) {
            3 -> urgentColor
            2 -> highColor
            1 -> mediumColor
            0 -> lowColor
            else -> noneColor
        }
    }
}

/**
 * 完成状态颜色
 */
object CompletionColors {
    val completed = Color(0xFF34C759)      // 完成 - iOS绿色
    val inProgress = Color(0xFF5AC8FA)     // 进行中 - iOS蓝色
    val notStarted = Color(0xFF8E8E93)     // 未开始 - iOS灰色
    val overdue = Color(0xFFFF3B30)        // 逾期 - iOS红色
}

/**
 * 进度指示器颜色
 */
@Composable
fun progressColor(progress: Float): Color {
    return when {
        progress >= 0.8f -> PriorityColors.lowColor
        progress >= 0.5f -> PriorityColors.mediumColor
        progress >= 0.2f -> PriorityColors.highColor
        else -> PriorityColors.urgentColor
    }
}

/**
 * Returns the background color for a specific page based on theme settings
 */
@Composable
fun getPageBackgroundColor(pageName: String): Color {
    val customThemeData = LocalCustomThemeData.current
    val materialTheme = MaterialTheme.colorScheme
    
    // If unified background is enabled, use the custom background color for all pages
    if (customThemeData.useUnifiedBackground) {
        return customThemeData.customBackgroundColor ?: materialTheme.background
    }
    
    // Otherwise, use the specific page color if available
    return when (pageName) {
        "home" -> customThemeData.homePageColor
        "calendar" -> customThemeData.calendarPageColor
        "statistics" -> customThemeData.statisticsPageColor
        "profile" -> customThemeData.profilePageColor
        else -> null
    } ?: customThemeData.customBackgroundColor ?: materialTheme.background
}

/**
 * 管理应用程序的主题设置和全局颜色状态
 */
object ThemeManager {
    private const val TAG = "ThemeManager"
    
    // 默认颜色定义
    private val defaultPrimaryColor = Color(0xFFf5f4f6)  // 紫色
    private val defaultSecondaryColor = Color(0xFF03DAC6) // 青绿色
    private val defaultBackgroundColor = Color(0xFFF5F5F5) // 近白色
    private val defaultSurfaceColor = Color(0xFFFFFFFF)  // 白色
    private val defaultErrorColor = Color(0xFFB00020)    // 红色
    
    // 状态流定义
    private val _userThemePreference = MutableStateFlow(
        UserThemePreference(
            isDarkMode = false,
            primaryColor = defaultPrimaryColor,
            secondaryColor = defaultSecondaryColor,
            backgroundColor = defaultBackgroundColor,
            surfaceColor = defaultSurfaceColor,
            errorColor = defaultErrorColor,
            // 为不同页面设置默认颜色
            homePageColor = defaultPrimaryColor,
            calendarPageColor = defaultSecondaryColor,
            statisticsPageColor = Color(0xFF009688),
            settingsPageColor = Color(0xFF607D8B),
            useSystemDarkMode = true,
            useUnifiedColors = false
        )
    )
    val userThemePreference: StateFlow<UserThemePreference> = _userThemePreference.asStateFlow()
    
    // 内部状态
    private var isInitialized = false
    private var dataStore: DataStore<Preferences>? = null
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    
    /**
     * 安全获取颜色值，处理Integer到Long的类型转换
     * 🔧 修复：增强颜色值验证和错误处理
     */
    private fun safeGetColor(preferences: Preferences, key: Preferences.Key<Long>, defaultValue: Int): Long {
        return try {
            // 尝试直接获取Long值
            val value = preferences[key]
            if (value != null) {
                // 验证颜色值是否有效
                val colorLong = value and 0xFFFFFFFFL
                if (isValidColor(colorLong)) {
                    Log.d(TAG, "✅ 成功获取颜色值: ${key.name}=${colorLong.toString(16)}")
                    return colorLong
                } else {
                    Log.w(TAG, "⚠️ 检测到无效颜色值: ${key.name}=${colorLong.toString(16)}, 使用默认值")
                }
            }

            // 尝试作为旧格式(Integer)读取
            val keyName = key.name
            val oldKey = intPreferencesKey(keyName)
            val intValue = preferences[oldKey]
            if (intValue != null) {
                // 确保将Int转换为无符号Long并验证
                val colorLong = intValue.toLong() and 0xFFFFFFFFL
                if (isValidColor(colorLong)) {
                    Log.d(TAG, "✅ 从旧格式获取颜色值: ${key.name}=${colorLong.toString(16)}")
                    return colorLong
                } else {
                    Log.w(TAG, "⚠️ 旧格式颜色值无效: ${key.name}=${colorLong.toString(16)}, 使用默认值")
                }
            }

            // 如果都没有，返回默认值
            val defaultColorLong = defaultValue.toLong() and 0xFFFFFFFFL
            Log.d(TAG, "🔧 使用默认颜色值: ${key.name}=${defaultColorLong.toString(16)}")
            defaultColorLong
        } catch (e: ClassCastException) {
            // 类型转换错误，尝试获取原始值
            try {
                // 获取偏好设置map中的原始值
                val preferencesMap = preferences.asMap()
                val entry = preferencesMap.entries.find { it.key.name == key.name }
                val rawValue = entry?.value

                // 根据实际类型转换
                val result = when (rawValue) {
                    is Int -> {
                        val colorLong = rawValue.toLong() and 0xFFFFFFFFL
                        if (isValidColor(colorLong)) colorLong else null
                    }
                    is Long -> {
                        val colorLong = rawValue and 0xFFFFFFFFL
                        if (isValidColor(colorLong)) colorLong else null
                    }
                    else -> null
                }

                if (result != null) {
                    Log.d(TAG, "✅ 通过备用方法成功获取颜色值: ${key.name}=${result.toString(16)}")
                    return result
                }
            } catch (e2: Exception) {
                Log.e(TAG, "❌ 备用颜色转换方法失败: ${e2.message}")
            }

            // 记录类型错误，转换为默认值
            Log.e(TAG, "❌ 颜色类型转换失败: ${key.name}, 使用默认值", e)
            defaultValue.toLong() and 0xFFFFFFFFL
        } catch (e: Exception) {
            Log.e(TAG, "❌ 获取颜色失败: ${key.name}, 使用默认值", e)
            defaultValue.toLong() and 0xFFFFFFFFL
        }
    }

    /**
     * 验证颜色值是否有效
     * 🔧 修复：添加颜色值有效性检查
     */
    private fun isValidColor(colorLong: Long): Boolean {
        return try {
            val color = Color(colorLong.toInt())
            // 检查颜色是否不是完全透明的黑色（通常表示无效值）
            !(color.alpha == 0f && color.red == 0f && color.green == 0f && color.blue == 0f)
        } catch (e: Exception) {
            Log.w(TAG, "颜色验证失败: ${colorLong.toString(16)}", e)
            false
        }
    }
    
    /**
     * 初始化主题管理器
     *
     * @param context 上下文
     * @param themeDataStore 用于存储主题设置的DataStore
     */
    fun initialize(context: Context, themeDataStore: DataStore<Preferences>) {
        if (isInitialized) return

        Log.d(TAG, "🔧 初始化主题管理器")
        isInitialized = true
        dataStore = themeDataStore

        // 🔧 添加初始化延迟，避免与其他组件的竞争条件
        scope.launch {
            delay(50) // 50ms延迟，确保其他组件初始化完成

            // 从DataStore加载设置
            try {
                // 确保dataStore非空
                val store = dataStore ?: return@launch
                
                store.data.collectLatest { preferences ->
                    // 检查协程是否仍然活跃
                    if (!isActive) return@collectLatest
                    
                    // 加载主题设置
                    val isDarkMode = preferences[PreferenceKeys.IS_DARK_MODE] ?: false
                    val useSystemDarkMode = preferences[PreferenceKeys.USE_SYSTEM_DARK_MODE] ?: true
                    val useUnifiedColors = preferences[PreferenceKeys.USE_UNIFIED_COLORS] ?: false
                    
                    // 🔧 加载字体设置并立即应用
                    val useCustomFont = preferences[PreferenceKeys.USE_CUSTOM_FONT] ?: false
                    val customFontPath = preferences[PreferenceKeys.CUSTOM_FONT_PATH] ?: ""
                    val customFontName = preferences[PreferenceKeys.CUSTOM_FONT_NAME] ?: ""
                    
                    if (useCustomFont && customFontPath.isNotEmpty()) {
                        try {
                            Log.d(TAG, "应用启动时加载自定义字体: $customFontName, 路径: $customFontPath")
                            val fontFamily = FontManager.loadCustomFont(customFontPath)
                            if (fontFamily != null) {
                                FontManager.setCustomFont(fontFamily, true)
                                Log.d(TAG, "✅ 自定义字体在应用启动时加载成功: $customFontName")
                            } else {
                                Log.e(TAG, "❌ 自定义字体加载失败: $customFontPath")
                                // 字体加载失败时，关闭自定义字体设置
                                FontManager.setCustomFont(null, false)
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "❌ 加载自定义字体时出错: $customFontPath", e)
                            FontManager.setCustomFont(null, false)
                        }
                    } else {
                        // 没有自定义字体设置或路径为空，使用系统默认字体
                        FontManager.setCustomFont(null, false)
                        Log.d(TAG, "使用系统默认字体")
                    }
                    
                    // 加载颜色设置 - 使用安全的类型转换
                    // 先获取任意类型，然后安全转换为Long
                    val primaryColorLong = safeGetColor(preferences, PreferenceKeys.PRIMARY_COLOR, defaultPrimaryColor.toArgb())
                    val secondaryColorLong = safeGetColor(preferences, PreferenceKeys.SECONDARY_COLOR, defaultSecondaryColor.toArgb())
                    val backgroundColorLong = safeGetColor(preferences, PreferenceKeys.BACKGROUND_COLOR, defaultBackgroundColor.toArgb())
                    val surfaceColorLong = safeGetColor(preferences, PreferenceKeys.SURFACE_COLOR, defaultSurfaceColor.toArgb())
                    val errorColorLong = safeGetColor(preferences, PreferenceKeys.ERROR_COLOR, defaultErrorColor.toArgb())
                    
                    // 加载页面颜色设置 - 使用安全的类型转换
                    val homePageColorLong = safeGetColor(preferences, PreferenceKeys.HOME_PAGE_COLOR, defaultPrimaryColor.toArgb())
                    val calendarPageColorLong = safeGetColor(preferences, PreferenceKeys.CALENDAR_PAGE_COLOR, defaultSecondaryColor.toArgb())
                    val statisticsPageColorLong = safeGetColor(preferences, PreferenceKeys.STATISTICS_PAGE_COLOR, Color(0xFF009688).toArgb())
                    val profilePageColorLong = safeGetColor(preferences, PreferenceKeys.PROFILE_PAGE_COLOR, Color(0xFF795548).toArgb()) // 🔧 添加profile页面
                    val settingsPageColorLong = safeGetColor(preferences, PreferenceKeys.SETTINGS_PAGE_COLOR, Color(0xFF607D8B).toArgb())
                    
                    // 转换为Color对象
                    val primaryColor = Color(primaryColorLong.toInt())
                    val secondaryColor = Color(secondaryColorLong.toInt())
                    val backgroundColor = Color(backgroundColorLong.toInt())
                    val surfaceColor = Color(surfaceColorLong.toInt())
                    val errorColor = Color(errorColorLong.toInt())
                    val homePageColor = Color(homePageColorLong.toInt())
                    val calendarPageColor = Color(calendarPageColorLong.toInt())
                    val statisticsPageColor = Color(statisticsPageColorLong.toInt())
                    val profilePageColor = Color(profilePageColorLong.toInt()) // 🔧 添加profile页面
                    val settingsPageColor = Color(settingsPageColorLong.toInt())
                    
                    // 更新主题偏好
                    val updatedPreference = UserThemePreference(
                        isDarkMode = isDarkMode,
                        primaryColor = primaryColor,
                        secondaryColor = secondaryColor,
                        backgroundColor = backgroundColor,
                        surfaceColor = surfaceColor,
                        errorColor = errorColor,
                        homePageColor = homePageColor,
                        calendarPageColor = calendarPageColor,
                        statisticsPageColor = statisticsPageColor,
                        profilePageColor = profilePageColor, // 🔧 添加profile页面
                        settingsPageColor = settingsPageColor,
                        useSystemDarkMode = useSystemDarkMode,
                        useUnifiedColors = useUnifiedColors
                    )
                    
                    // 🔧 修复：检查是否有实际变化，避免不必要的更新和覆盖用户刚设置的值
                    val currentPreference = _userThemePreference.value
                    val hasSignificantChange = currentPreference.primaryColor != updatedPreference.primaryColor ||
                            currentPreference.backgroundColor != updatedPreference.backgroundColor ||
                            currentPreference.isDarkMode != updatedPreference.isDarkMode ||
                            currentPreference.useSystemDarkMode != updatedPreference.useSystemDarkMode

                    if (hasSignificantChange) {
                        _userThemePreference.value = updatedPreference
                        Log.d(TAG, "✅ 从DataStore加载主题设置成功: primaryColor=${updatedPreference.primaryColor.toArgb().toString(16)}")

                        // 通过EventBus广播主题改变事件
                        broadcastThemeSettings()
                    } else {
                        // 🔧 即使没有变化，也应该广播一次设置，确保所有页面都能接收到主题设置
                        Log.d(TAG, "主题设置无显著变化，但仍然广播确保所有页面同步")
                        broadcastThemeSettings()
                    }

                    // 🔧 修复：只在应用启动时强制检查预设主题匹配，避免覆盖用户自定义
                    PresetThemeManager.checkAndUpdateCurrentPresetTheme(forceCheck = true)
                }
            } catch (e: Exception) {
                // 忽略取消异常，只记录其他异常
                if (e !is CancellationException) {
                    Log.e(TAG, "加载主题设置失败: ${e.message}", e)
                } else {
                    Log.d(TAG, "主题设置加载被取消，这是正常现象")
                }
            }
        }
        
        // 监控系统暗色模式变化
        val configuration = context.resources.configuration
        val currentNightMode = configuration.uiMode and Configuration.UI_MODE_NIGHT_MASK
        
        // 初始化时应用系统深色模式设置（如果启用）
        if (_userThemePreference.value.useSystemDarkMode) {
            val systemIsDark = currentNightMode == Configuration.UI_MODE_NIGHT_YES
            if (_userThemePreference.value.isDarkMode != systemIsDark) {
                scope.launch {
                    toggleDarkMode(systemIsDark)
                }
            }
        }
        
        Log.d(TAG, "主题管理器初始化完成")
    }
    
    /**
     * 切换暗色模式
     * @param isDark 是否使用暗色模式
     */
    suspend fun toggleDarkMode(isDark: Boolean) {
        try {
            Log.d(TAG, "切换暗色模式: $isDark")
            val store = dataStore ?: return
            
            // 更新DataStore
            store.edit { preferences ->
                preferences[PreferenceKeys.IS_DARK_MODE] = isDark
            }
            
            // 更新内存中的状态
            _userThemePreference.update { current ->
                current.copy(isDarkMode = isDark)
            }
            
            // 广播变更
            broadcastThemeSettings()
            
            Log.d(TAG, "暗色模式已切换为: $isDark")
        } catch (e: Exception) {
            if (e !is CancellationException) {
                Log.e(TAG, "切换暗色模式失败: ${e.message}", e)
            }
        }
    }
    
    /**
     * 广播主题设置变更
     * 通过EventBus发送ThemeChanged事件和页面背景色事件
     */
    private fun broadcastThemeSettings() {
        scope.launch {
            try {
                if (!isActive) return@launch
                
                val preference = _userThemePreference.value
                
                // 1. 广播基础主题变更事件
                EventBus.tryEmit(AppEvent.ThemeChanged(preference.isDarkMode))
                Log.d(TAG, "已广播主题变更事件")
                
                // 2. 广播所有页面的背景色设置（修复背景色持久化问题）
                val pageColors = mapOf(
                    "home" to preference.homePageColor,
                    "calendar" to preference.calendarPageColor,
                    "statistics" to preference.statisticsPageColor,
                    "profile" to preference.profilePageColor, // 🔧 添加profile页面
                    "settings" to preference.settingsPageColor
                )
                
                pageColors.forEach { (pageName, color) ->
                    val colorArgb = color.toArgb().toLong() and 0xFFFFFFFFL
                    EventBus.tryEmit(AppEvent.PageBackgroundChanged(
                        pageName = pageName,
                        colorArgb = colorArgb
                    ))
                    Log.d(TAG, "已广播${pageName}页面背景色事件: ${colorArgb.toString(16)}")
                }
                
                // 3. 🔧 如果启用了统一背景色，也广播统一背景色事件
                if (preference.useUnifiedColors) {
                    EventBus.tryEmit(AppEvent.ThemeSettingsChanged(
                        colorArgb = preference.backgroundColor.toArgb().toLong(),
                        useUnifiedBackground = true
                    ))
                    Log.d(TAG, "已广播统一背景色事件: ${preference.backgroundColor.toArgb().toString(16)}")
                }
                
                Log.d(TAG, "✅ 所有主题设置已广播完成")
                
            } catch (e: Exception) {
                if (e !is CancellationException) {
                    Log.e(TAG, "广播主题变更事件失败: ${e.message}", e)
                }
            }
        }
    }
    
    /**
     * 清理资源方法，应在应用退出时调用
     */
    fun cleanup() {
        scope.coroutineContext.cancelChildren()
        Log.d(TAG, "ThemeManager资源已清理")
    }

    /**
     * 广播页面背景色变更
     * @param pageName 页面名称
     * @param color 颜色值
     */
    fun broadcastPageBackgroundColor(pageName: String, color: Color) {
        scope.launch {
            try {
                if (!isActive) return@launch
                
                // 将Color转换为ARGB Long值
                val colorArgb = color.toArgb().toLong() and 0xFFFFFFFFL
                
                // 发送页面背景色变更事件
                EventBus.tryEmit(AppEvent.PageBackgroundChanged(
                    pageName = pageName,
                    colorArgb = colorArgb
                ))
                
                Log.d(TAG, "已广播页面背景色变更事件: 页面=$pageName, 颜色=${colorArgb.toString(16)}")
            } catch (e: Exception) {
                if (e !is CancellationException) {
                    Log.e(TAG, "广播页面背景色变更事件失败: ${e.message}", e)
                }
            }
        }
    }

    /**
     * 更新是否跟随系统暗色模式设置
     * @param useSystemDarkMode 是否跟随系统暗色模式
     */
    suspend fun updateSystemDarkMode(useSystemDarkMode: Boolean) {
        try {
            Log.d(TAG, "更新跟随系统暗色模式设置: $useSystemDarkMode")
            val store = dataStore ?: return
            
            // 更新DataStore
            store.edit { preferences ->
                preferences[PreferenceKeys.USE_SYSTEM_DARK_MODE] = useSystemDarkMode
            }
            
            // 更新内存中的状态
            _userThemePreference.update { current ->
                current.copy(useSystemDarkMode = useSystemDarkMode)
            }
            
            // 广播变更
            broadcastThemeSettings()
            
            Log.d(TAG, "跟随系统暗色模式设置已更新为: $useSystemDarkMode")
        } catch (e: Exception) {
            if (e !is CancellationException) {
                Log.e(TAG, "更新跟随系统暗色模式设置失败: ${e.message}", e)
            }
        }
    }

    /**
     * 更新主题偏好设置
     * @param updateBlock 更新函数，接收当前偏好设置并返回新的偏好设置
     * @param clearPresetId 是否清除当前预设主题ID，默认为true（用户自定义时）
     */
    suspend fun updateThemePreference(
        updateBlock: (UserThemePreference) -> UserThemePreference,
        clearPresetId: Boolean = true
    ) {
        try {
            Log.d(TAG, "🔧 更新主题偏好设置")
            val store = dataStore ?: return

            // 🔧 修复：先获取当前状态，避免并发修改
            val currentPreference = _userThemePreference.value
            val updatedPreference = updateBlock(currentPreference)

            // 🔧 修复：检查是否有实际变化，避免不必要的写入
            if (currentPreference == updatedPreference) {
                Log.d(TAG, "主题偏好设置无变化，跳过更新")
                return
            }

            // 更新内存中的状态
            _userThemePreference.value = updatedPreference
            Log.d(TAG, "✅ 主题偏好设置已更新: primaryColor=${updatedPreference.primaryColor.toArgb().toString(16)}")
            
            // 🔧 修复：将更新后的设置保存到DataStore，确保颜色值正确转换
            store.edit { preferences ->
                // 🔧 修复：使用更安全的颜色值转换，确保32位无符号整数
                val safeColorConvert = { color: Color ->
                    val argb = color.toArgb()
                    // 确保转换为32位无符号Long值
                    argb.toLong() and 0xFFFFFFFFL
                }

                // 保存所有颜色设置
                preferences[PreferenceKeys.PRIMARY_COLOR] = safeColorConvert(updatedPreference.primaryColor)
                preferences[PreferenceKeys.SECONDARY_COLOR] = safeColorConvert(updatedPreference.secondaryColor)
                preferences[PreferenceKeys.BACKGROUND_COLOR] = safeColorConvert(updatedPreference.backgroundColor)
                preferences[PreferenceKeys.SURFACE_COLOR] = safeColorConvert(updatedPreference.surfaceColor)
                preferences[PreferenceKeys.ERROR_COLOR] = safeColorConvert(updatedPreference.errorColor)

                // 保存页面背景色设置
                preferences[PreferenceKeys.HOME_PAGE_COLOR] = safeColorConvert(updatedPreference.homePageColor)
                preferences[PreferenceKeys.CALENDAR_PAGE_COLOR] = safeColorConvert(updatedPreference.calendarPageColor)
                preferences[PreferenceKeys.STATISTICS_PAGE_COLOR] = safeColorConvert(updatedPreference.statisticsPageColor)
                preferences[PreferenceKeys.PROFILE_PAGE_COLOR] = safeColorConvert(updatedPreference.profilePageColor)
                preferences[PreferenceKeys.SETTINGS_PAGE_COLOR] = safeColorConvert(updatedPreference.settingsPageColor)

                // 保存其他设置
                preferences[PreferenceKeys.IS_DARK_MODE] = updatedPreference.isDarkMode
                preferences[PreferenceKeys.USE_SYSTEM_DARK_MODE] = updatedPreference.useSystemDarkMode
                preferences[PreferenceKeys.USE_UNIFIED_COLORS] = updatedPreference.useUnifiedColors

                Log.d(TAG, "✅ 主题偏好设置已保存到DataStore，主色调: ${safeColorConvert(updatedPreference.primaryColor).toString(16)}")
            }
            
            // 广播变更
            broadcastThemeSettings()

            // 🔧 修复：当用户自定义主题时，清除当前预设主题ID
            // 因为此时用户已经不再使用预设主题了
            if (clearPresetId) {
                PresetThemeManager.clearCurrentPresetId()
            }

            Log.d(TAG, "主题偏好设置更新完成")
        } catch (e: Exception) {
            if (e !is CancellationException) {
                Log.e(TAG, "更新主题偏好设置失败: ${e.message}", e)
                // 🔧 重新抛出异常，让调用方知道保存失败
                throw e
            }
        }
    }
}

/**
 * 预设主题管理器
 * 管理用户的预设主题，包括内置主题和自定义主题
 */
object PresetThemeManager {
    private const val TAG = "PresetThemeManager"
    
    private val _presetThemeState = MutableStateFlow(
        PresetThemeState(
            presets = BuiltInPresets.getAllBuiltInThemes(),
            currentPresetId = null,
            isLoading = false,
            error = null
        )
    )
    val presetThemeState: StateFlow<PresetThemeState> = _presetThemeState.asStateFlow()
    
    private var dataStore: DataStore<Preferences>? = null
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    /**
     * 初始化预设主题管理器
     */
    fun initialize(themeDataStore: DataStore<Preferences>) {
        dataStore = themeDataStore
        loadUserPresets()
        loadCurrentPresetId()
    }
    
    /**
     * 保存当前主题为预设
     */
    suspend fun saveCurrentThemeAsPreset(
        name: String,
        description: String = ""
    ): Result<PresetTheme> {
        return try {
            val currentPreference = ThemeManager.userThemePreference.value
            val presetId = "custom_${System.currentTimeMillis()}"
            
            val newPreset = PresetTheme(
                id = presetId,
                name = name,
                description = description,
                previewColor = currentPreference.primaryColor,
                themePreference = currentPreference,
                isBuiltIn = false,
                createdAt = System.currentTimeMillis()
            )
            
            // 保存到DataStore
            savePresetToDataStore(newPreset)
            
            // 更新状态
            _presetThemeState.update { currentState ->
                currentState.copy(
                    presets = currentState.presets + newPreset
                )
            }
            
            Log.d(TAG, "✅ 成功保存预设主题: $name")
            Result.success(newPreset)
        } catch (e: Exception) {
            Log.e(TAG, "❌ 保存预设主题失败: ${e.message}", e)
            Result.failure(e)
        }
    }
    
    /**
     * 应用预设主题
     */
    suspend fun applyPresetTheme(presetId: String): Result<Unit> {
        return try {
            val preset = _presetThemeState.value.presets.find { it.id == presetId }
            if (preset == null) {
                Log.e(TAG, "❌ 未找到预设主题: $presetId")
                return Result.failure(Exception("未找到预设主题"))
            }
            
            // 应用主题偏好设置，不清除预设主题ID
            ThemeManager.updateThemePreference(
                updateBlock = { preset.themePreference },
                clearPresetId = false
            )
            
            // 更新当前预设ID并保存到DataStore
            _presetThemeState.update { currentState ->
                currentState.copy(currentPresetId = presetId)
            }

            // 保存当前预设ID到DataStore
            saveCurrentPresetId(presetId)

            Log.d(TAG, "✅ 成功应用预设主题: ${preset.name}")
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "❌ 应用预设主题失败: ${e.message}", e)
            Result.failure(e)
        }
    }
    
    /**
     * 删除预设主题
     */
    suspend fun deletePresetTheme(presetId: String): Result<Unit> {
        return try {
            val preset = _presetThemeState.value.presets.find { it.id == presetId }
            if (preset == null) {
                Log.e(TAG, "❌ 未找到预设主题: $presetId")
                return Result.failure(Exception("未找到预设主题"))
            }
            
            if (preset.isBuiltIn) {
                Log.e(TAG, "❌ 不能删除内置主题: $presetId")
                return Result.failure(Exception("不能删除内置主题"))
            }
            
            // 从DataStore删除
            deletePresetFromDataStore(presetId)
            
            // 更新状态
            val newCurrentPresetId = if (_presetThemeState.value.currentPresetId == presetId) null else _presetThemeState.value.currentPresetId
            _presetThemeState.update { currentState ->
                currentState.copy(
                    presets = currentState.presets.filterNot { it.id == presetId },
                    currentPresetId = newCurrentPresetId
                )
            }

            // 如果删除的是当前使用的预设主题，清除DataStore中的当前预设ID
            if (newCurrentPresetId == null) {
                saveCurrentPresetId(null)
            }

            Log.d(TAG, "✅ 成功删除预设主题: ${preset.name}")
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "❌ 删除预设主题失败: ${e.message}", e)
            Result.failure(e)
        }
    }
    
    /**
     * 更新预设主题
     */
    suspend fun updatePresetTheme(
        presetId: String,
        name: String? = null,
        description: String? = null
    ): Result<Unit> {
        return try {
            val presetIndex = _presetThemeState.value.presets.indexOfFirst { it.id == presetId }
            if (presetIndex == -1) {
                Log.e(TAG, "❌ 未找到预设主题: $presetId")
                return Result.failure(Exception("未找到预设主题"))
            }
            
            val currentPreset = _presetThemeState.value.presets[presetIndex]
            if (currentPreset.isBuiltIn) {
                Log.e(TAG, "❌ 不能修改内置主题: $presetId")
                return Result.failure(Exception("不能修改内置主题"))
            }
            
            val updatedPreset = currentPreset.copy(
                name = name ?: currentPreset.name,
                description = description ?: currentPreset.description
            )
            
            // 保存到DataStore
            savePresetToDataStore(updatedPreset)
            
            // 更新状态
            _presetThemeState.update { currentState ->
                val updatedPresets = currentState.presets.toMutableList()
                updatedPresets[presetIndex] = updatedPreset
                currentState.copy(presets = updatedPresets)
            }
            
            Log.d(TAG, "✅ 成功更新预设主题: ${updatedPreset.name}")
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "❌ 更新预设主题失败: ${e.message}", e)
            Result.failure(e)
        }
    }
    
    /**
     * 获取当前应用的预设主题
     */
    fun getCurrentPresetTheme(): PresetTheme? {
        val currentPresetId = _presetThemeState.value.currentPresetId
        return if (currentPresetId != null) {
            _presetThemeState.value.presets.find { it.id == currentPresetId }
        } else {
            null
        }
    }

    /**
     * 清除当前预设主题ID（当用户自定义主题时调用）
     */
    fun clearCurrentPresetId() {
        scope.launch {
            try {
                _presetThemeState.update { currentState ->
                    currentState.copy(currentPresetId = null)
                }

                saveCurrentPresetId(null)

                Log.d(TAG, "✅ 已清除当前预设主题ID（用户自定义主题）")
            } catch (e: Exception) {
                Log.e(TAG, "清除当前预设主题ID失败: ${e.message}", e)
            }
        }
    }

    /**
     * 检查当前主题是否与某个预设主题匹配
     * 🔧 修复：只在应用启动时检查，不在用户自定义时自动覆盖
     */
    fun checkAndUpdateCurrentPresetTheme(forceCheck: Boolean = false) {
        scope.launch {
            try {
                // 🔧 关键修复：如果当前没有预设ID且不是强制检查，则跳过
                // 这避免了用户自定义主题时被自动覆盖
                val currentPresetId = _presetThemeState.value.currentPresetId
                if (currentPresetId == null && !forceCheck) {
                    Log.d(TAG, "跳过自动匹配检查，保持用户自定义主题")
                    return@launch
                }

                val currentTheme = ThemeManager.userThemePreference.value
                val matchingPreset = _presetThemeState.value.presets.find { preset ->
                    preset.themePreference.primaryColor == currentTheme.primaryColor &&
                    preset.themePreference.secondaryColor == currentTheme.secondaryColor &&
                    preset.themePreference.backgroundColor == currentTheme.backgroundColor &&
                    preset.themePreference.homePageColor == currentTheme.homePageColor &&
                    preset.themePreference.calendarPageColor == currentTheme.calendarPageColor &&
                    preset.themePreference.statisticsPageColor == currentTheme.statisticsPageColor &&
                    preset.themePreference.profilePageColor == currentTheme.profilePageColor &&
                    preset.themePreference.settingsPageColor == currentTheme.settingsPageColor
                }

                if (matchingPreset != null && currentPresetId != matchingPreset.id) {
                    _presetThemeState.update { currentState ->
                        currentState.copy(currentPresetId = matchingPreset.id)
                    }

                    saveCurrentPresetId(matchingPreset.id)

                    Log.d(TAG, "✅ 自动识别并设置当前预设主题: ${matchingPreset.name}")
                } else if (matchingPreset == null && currentPresetId != null) {
                    // 🔧 如果当前主题不匹配任何预设，清除预设ID
                    _presetThemeState.update { currentState ->
                        currentState.copy(currentPresetId = null)
                    }
                    clearCurrentPresetId()
                    Log.d(TAG, "✅ 当前主题不匹配任何预设，已清除预设ID")
                }
            } catch (e: Exception) {
                Log.e(TAG, "检查当前预设主题失败: ${e.message}", e)
            }
        }
    }
    
    /**
     * 从DataStore加载用户预设主题
     */
    private fun loadUserPresets() {
        scope.launch {
            try {
                val store = dataStore ?: return@launch
                
                store.data.collectLatest { preferences ->
                    if (!isActive) return@collectLatest
                    
                    val userPresets = mutableListOf<PresetTheme>()
                    
                    // 查找所有用户预设主题
                    val presetKeys = preferences.asMap().keys
                        .filter { it.name.startsWith("preset_") }
                        .map { it.name }
                        .toSet()
                    
                    // 按预设ID分组
                    val presetIds = presetKeys.map { keyName ->
                        keyName.substringAfter("preset_").substringBefore("_")
                    }.toSet()
                    
                    presetIds.forEach { presetId ->
                        try {
                            val preset = loadPresetFromDataStore(preferences, presetId)
                            if (preset != null) {
                                userPresets.add(preset)
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "加载预设主题失败: $presetId", e)
                        }
                    }
                    
                    // 更新状态
                    _presetThemeState.update { currentState ->
                        currentState.copy(
                            presets = BuiltInPresets.getAllBuiltInThemes() + userPresets.sortedByDescending { it.createdAt }
                        )
                    }
                    
                    Log.d(TAG, "✅ 成功加载${userPresets.size}个用户预设主题")
                }
            } catch (e: Exception) {
                if (e !is CancellationException) {
                    Log.e(TAG, "加载用户预设主题失败: ${e.message}", e)
                }
            }
        }
    }
    
    /**
     * 保存预设主题到DataStore
     */
    private suspend fun savePresetToDataStore(preset: PresetTheme) {
        val store = dataStore ?: return
        
        store.edit { preferences ->
            val prefix = "preset_${preset.id}"
            preferences[stringPreferencesKey("${prefix}_name")] = preset.name
            preferences[stringPreferencesKey("${prefix}_description")] = preset.description
            preferences[longPreferencesKey("${prefix}_preview_color")] = preset.previewColor.toArgb().toLong()
            preferences[longPreferencesKey("${prefix}_created_at")] = preset.createdAt
            
            // 保存主题偏好设置
            val themePrefix = "${prefix}_theme"
            preferences[booleanPreferencesKey("${themePrefix}_dark_mode")] = preset.themePreference.isDarkMode
            preferences[longPreferencesKey("${themePrefix}_primary_color")] = preset.themePreference.primaryColor.toArgb().toLong()
            preferences[longPreferencesKey("${themePrefix}_secondary_color")] = preset.themePreference.secondaryColor.toArgb().toLong()
            preferences[longPreferencesKey("${themePrefix}_background_color")] = preset.themePreference.backgroundColor.toArgb().toLong()
            preferences[longPreferencesKey("${themePrefix}_surface_color")] = preset.themePreference.surfaceColor.toArgb().toLong()
            preferences[longPreferencesKey("${themePrefix}_error_color")] = preset.themePreference.errorColor.toArgb().toLong()
            preferences[longPreferencesKey("${themePrefix}_home_page_color")] = preset.themePreference.homePageColor.toArgb().toLong()
            preferences[longPreferencesKey("${themePrefix}_calendar_page_color")] = preset.themePreference.calendarPageColor.toArgb().toLong()
            preferences[longPreferencesKey("${themePrefix}_statistics_page_color")] = preset.themePreference.statisticsPageColor.toArgb().toLong()
            preferences[longPreferencesKey("${themePrefix}_profile_page_color")] = preset.themePreference.profilePageColor.toArgb().toLong()
            preferences[longPreferencesKey("${themePrefix}_settings_page_color")] = preset.themePreference.settingsPageColor.toArgb().toLong()
            preferences[booleanPreferencesKey("${themePrefix}_use_system_dark_mode")] = preset.themePreference.useSystemDarkMode
            preferences[booleanPreferencesKey("${themePrefix}_use_unified_colors")] = preset.themePreference.useUnifiedColors
        }
    }
    
    /**
     * 从DataStore加载预设主题
     */
    private fun loadPresetFromDataStore(preferences: Preferences, presetId: String): PresetTheme? {
        return try {
            val prefix = "preset_$presetId"
            val name = preferences[stringPreferencesKey("${prefix}_name")] ?: return null
            val description = preferences[stringPreferencesKey("${prefix}_description")] ?: ""
            val previewColorLong = preferences[longPreferencesKey("${prefix}_preview_color")] ?: return null
            val createdAt = preferences[longPreferencesKey("${prefix}_created_at")] ?: System.currentTimeMillis()
            
            // 加载主题偏好设置
            val themePrefix = "${prefix}_theme"
            val isDarkMode = preferences[booleanPreferencesKey("${themePrefix}_dark_mode")] ?: false
            val primaryColorLong = preferences[longPreferencesKey("${themePrefix}_primary_color")] ?: return null
            val secondaryColorLong = preferences[longPreferencesKey("${themePrefix}_secondary_color")] ?: return null
            val backgroundColorLong = preferences[longPreferencesKey("${themePrefix}_background_color")] ?: return null
            val surfaceColorLong = preferences[longPreferencesKey("${themePrefix}_surface_color")] ?: return null
            val errorColorLong = preferences[longPreferencesKey("${themePrefix}_error_color")] ?: return null
            val homePageColorLong = preferences[longPreferencesKey("${themePrefix}_home_page_color")] ?: return null
            val calendarPageColorLong = preferences[longPreferencesKey("${themePrefix}_calendar_page_color")] ?: return null
            val statisticsPageColorLong = preferences[longPreferencesKey("${themePrefix}_statistics_page_color")] ?: return null
            val profilePageColorLong = preferences[longPreferencesKey("${themePrefix}_profile_page_color")] ?: return null
            val settingsPageColorLong = preferences[longPreferencesKey("${themePrefix}_settings_page_color")] ?: return null
            val useSystemDarkMode = preferences[booleanPreferencesKey("${themePrefix}_use_system_dark_mode")] ?: true
            val useUnifiedColors = preferences[booleanPreferencesKey("${themePrefix}_use_unified_colors")] ?: false
            
            val themePreference = UserThemePreference(
                isDarkMode = isDarkMode,
                primaryColor = Color(primaryColorLong.toInt()),
                secondaryColor = Color(secondaryColorLong.toInt()),
                backgroundColor = Color(backgroundColorLong.toInt()),
                surfaceColor = Color(surfaceColorLong.toInt()),
                errorColor = Color(errorColorLong.toInt()),
                homePageColor = Color(homePageColorLong.toInt()),
                calendarPageColor = Color(calendarPageColorLong.toInt()),
                statisticsPageColor = Color(statisticsPageColorLong.toInt()),
                profilePageColor = Color(profilePageColorLong.toInt()),
                settingsPageColor = Color(settingsPageColorLong.toInt()),
                useSystemDarkMode = useSystemDarkMode,
                useUnifiedColors = useUnifiedColors
            )
            
            PresetTheme(
                id = presetId,
                name = name,
                description = description,
                previewColor = Color(previewColorLong.toInt()),
                themePreference = themePreference,
                isBuiltIn = false,
                createdAt = createdAt
            )
        } catch (e: Exception) {
            Log.e(TAG, "解析预设主题失败: $presetId", e)
            null
        }
    }
    
    /**
     * 从DataStore删除预设主题
     */
    private suspend fun deletePresetFromDataStore(presetId: String) {
        val store = dataStore ?: return
        
        store.edit { preferences ->
            val prefix = "preset_$presetId"
            val themePrefix = "${prefix}_theme"
            
            // 删除预设主题相关的所有键
            val keysToRemove = preferences.asMap().keys.filter { 
                it.name.startsWith(prefix) 
            }
            
            keysToRemove.forEach { key ->
                preferences.remove(key)
            }
        }
    }
    
    /**
     * 保存当前预设主题ID到DataStore
     */
    private suspend fun saveCurrentPresetId(presetId: String?) {
        val store = dataStore ?: return

        try {
            store.edit { preferences ->
                if (presetId != null) {
                    preferences[PreferenceKeys.CURRENT_PRESET_ID] = presetId
                } else {
                    preferences.remove(PreferenceKeys.CURRENT_PRESET_ID)
                }
            }

            // 确保状态更新
            _presetThemeState.update { currentState ->
                currentState.copy(currentPresetId = presetId)
            }

            Log.d(TAG, "✅ 已保存当前预设主题ID: $presetId")
        } catch (e: Exception) {
            Log.e(TAG, "❌ 保存当前预设主题ID失败: ${e.message}", e)
            throw e // 重新抛出异常，让调用方知道保存失败
        }
    }

    /**
     * 从DataStore加载当前预设主题ID
     */
    private fun loadCurrentPresetId() {
        scope.launch {
            try {
                val store = dataStore ?: return@launch

                store.data.collectLatest { preferences ->
                    if (!isActive) return@collectLatest

                    val currentPresetId = preferences[PreferenceKeys.CURRENT_PRESET_ID]

                    // 更新状态
                    _presetThemeState.update { currentState ->
                        currentState.copy(currentPresetId = currentPresetId)
                    }

                    Log.d(TAG, "✅ 已加载当前预设主题ID: $currentPresetId")
                }
            } catch (e: Exception) {
                if (e !is CancellationException) {
                    Log.e(TAG, "加载当前预设主题ID失败: ${e.message}", e)
                }
            }
        }
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        scope.coroutineContext.cancelChildren()
        Log.d(TAG, "PresetThemeManager资源已清理")
    }
} 