plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'com.google.devtools.ksp'
    id 'com.google.dagger.hilt.android'
    id 'kotlin-parcelize'
    id 'org.jetbrains.kotlin.plugin.serialization'
}

android {
    namespace 'com.timeflow.app'
    compileSdk 34

    defaultConfig {
        applicationId "com.timeflow.app"
        minSdk 26
        targetSdk 34
        versionCode rootProject.ext.app_version_code
        versionName rootProject.ext.app_version_name

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary true
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    
    buildFeatures {
        compose true
        buildConfig true
    }

    composeOptions {
        kotlinCompilerExtensionVersion rootProject.ext.compose_compiler_version
    }

    packaging {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
        }
    }
    
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    
    kotlinOptions {
        jvmTarget = '17'
        freeCompilerArgs += [
            "-opt-in=androidx.compose.foundation.ExperimentalFoundationApi",
            "-opt-in=androidx.compose.material.ExperimentalMaterialApi",
            "-opt-in=androidx.compose.material3.ExperimentalMaterial3Api",
            "-opt-in=kotlinx.coroutines.ExperimentalCoroutinesApi"
        ]
    }
}

dependencies {
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation platform("org.jetbrains.kotlin:kotlin-bom:${rootProject.ext.kotlin_version}")
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.7.0'
    implementation 'androidx.activity:activity-compose:1.8.2'
    
    // Compose
    implementation platform('androidx.compose:compose-bom:2024.02.01')
    implementation 'androidx.compose.ui:ui'
    implementation 'androidx.compose.ui:ui-graphics'
    implementation 'androidx.compose.ui:ui-tooling-preview'
    implementation "androidx.compose.material3:material3:1.2.0"
    implementation "androidx.compose.material:material:${rootProject.ext.compose_version}"
    implementation "androidx.compose.material:material-icons-extended:${rootProject.ext.compose_version}"
    implementation 'androidx.compose.runtime:runtime-livedata'
    implementation 'androidx.lifecycle:lifecycle-runtime-compose:2.7.0'
    implementation 'androidx.compose.animation:animation:1.6.1'
    implementation 'androidx.compose.animation:animation-core:1.6.1'
    implementation 'androidx.compose.animation:animation-graphics:1.6.1'
    implementation 'androidx.compose.ui:ui-text:1.6.1'
    
    implementation 'com.google.android.material:material:1.11.0'
    implementation 'androidx.navigation:navigation-compose:2.7.7'
    implementation 'androidx.hilt:hilt-navigation-compose:1.1.0'
    implementation 'com.google.dagger:hilt-android:2.50'
    ksp 'com.google.dagger:hilt-android-compiler:2.50'
    implementation 'androidx.room:room-runtime:2.6.1'
    implementation 'androidx.room:room-ktx:2.6.1'
    ksp 'androidx.room:room-compiler:2.6.1'
    implementation 'com.jakewharton.timber:timber:5.0.1'
    
    // Performance monitoring
    implementation 'androidx.metrics:metrics-performance:1.0.0-beta01'
    
    // WorkManager for background processing
    implementation "androidx.work:work-runtime-ktx:2.9.0"
    implementation 'androidx.hilt:hilt-work:1.1.0'
    ksp 'androidx.hilt:hilt-compiler:1.1.0'
    
    // Gson for JSON serialization/deserialization
    implementation 'com.google.code.gson:gson:2.10.1'
    
    // DataStore for preferences
    implementation 'androidx.datastore:datastore-preferences:1.0.0'
    
    // Preference library
    implementation 'androidx.preference:preference-ktx:1.2.1'
    
    // App Startup
    implementation 'androidx.startup:startup-runtime:1.1.1'
    
    // Accompanist libraries
    implementation 'com.google.accompanist:accompanist-systemuicontroller:0.32.0'
    
    // 添加ApplicationContext注解所需依赖
    implementation 'javax.inject:javax.inject:1'
    
    // Excel 文件处理库
    implementation 'org.apache.poi:poi:5.2.3'
    implementation 'org.apache.poi:poi-ooxml:5.2.3'
    
    // Coil图片加载库
    implementation "io.coil-kt:coil-compose:2.4.0"
    
    // 添加Kotlin序列化库
    implementation "org.jetbrains.kotlinx:kotlinx-serialization-json:1.6.0"
    
    // AWS SDK for S3 同步功能
    implementation 'com.amazonaws:aws-android-sdk-core:2.76.0'
    implementation 'com.amazonaws:aws-android-sdk-s3:2.76.0'
    implementation 'com.amazonaws:aws-android-sdk-auth-core:2.76.0'

    // 七牛云 SDK (暂时注释，使用模拟实现)
    // implementation 'com.qiniu:qiniu-android-sdk:8.7.0'

    // 腾讯云 COS SDK (暂时注释，避免构建失败)
    // implementation 'com.tencent.qcloud:cosxml:5.9.15'

    // 阿里云 OSS SDK (暂时注释，避免构建失败)
    // implementation 'com.aliyun.dpa:oss-android-sdk:2.9.13'
    
    // 网络请求库
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'
    
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    androidTestImplementation platform('androidx.compose:compose-bom:2024.02.01')
    androidTestImplementation 'androidx.compose.ui:ui-test-junit4'
    debugImplementation 'androidx.compose.ui:ui-tooling'
    debugImplementation 'androidx.compose.ui:ui-test-manifest'
}

// 添加Hilt聚合任务配置，解决FileAlreadyExistsException问题
hilt {
    enableAggregatingTask = true
} 