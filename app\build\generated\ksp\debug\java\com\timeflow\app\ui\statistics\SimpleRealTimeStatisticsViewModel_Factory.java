package com.timeflow.app.ui.statistics;

import com.timeflow.app.data.service.SimpleRealDataStatisticsService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SimpleRealTimeStatisticsViewModel_Factory implements Factory<SimpleRealTimeStatisticsViewModel> {
  private final Provider<SimpleRealDataStatisticsService> statisticsServiceProvider;

  public SimpleRealTimeStatisticsViewModel_Factory(
      Provider<SimpleRealDataStatisticsService> statisticsServiceProvider) {
    this.statisticsServiceProvider = statisticsServiceProvider;
  }

  @Override
  public SimpleRealTimeStatisticsViewModel get() {
    return newInstance(statisticsServiceProvider.get());
  }

  public static SimpleRealTimeStatisticsViewModel_Factory create(
      Provider<SimpleRealDataStatisticsService> statisticsServiceProvider) {
    return new SimpleRealTimeStatisticsViewModel_Factory(statisticsServiceProvider);
  }

  public static SimpleRealTimeStatisticsViewModel newInstance(
      SimpleRealDataStatisticsService statisticsService) {
    return new SimpleRealTimeStatisticsViewModel(statisticsService);
  }
}
