D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\dao\EmotionRecordDao.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\service\NotificationTestService.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\utils\SampleDataGenerator.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\milestone\GridView.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\repository\SharedPendingDeletionState.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\goal\GoalReviewScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\goal\GoalTemplateScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\timetracking\screens\TimeTrackingScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\worker\OverdueTaskCheckWorkerEntryPoint.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\repository\TaskRepositoryImpl.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\model\Converters.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\language\LanguageSettingsViewModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\health\HabitDetailScreenOptimized.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\repository\EmotionRecordRepository.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\health\SymptomWidget.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\viewmodel\GoalViewModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\di\RepositoryModule.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\di\ImageModule.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\goal\AddGoalScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\repository\KanbanBoardRepository.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\utils\RenderUtils.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\entity\KanbanColumn.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\component\goal\ModernProgressComponents.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\utils\StatusBarUtils.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\model\AppUsageData.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\splash\SplashScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\task\components\TaskFilterChips.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\TimeFlowNavHost.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\timetracking\TimerUIComponents.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\task\components\TaskGroupHeader.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\di\ViewModelKey.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\theme\ThemeManager.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\timetracking\components\TaskTimeStatisticsSection.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\health\AddHabitScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\account\AccountScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\utils\ComposeScreenshotUtils.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\di\ReflectionModule.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\task\components\common\PriorityIndicator.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\dao\GoalDao.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\service\RecurringTaskManager.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\viewmodel\MenstrualCycleViewModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\settings\SyncSettingsScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\viewmodel\GlobalTimerViewModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\utils\PerformanceMonitor.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\wishlist\EnhancedAddWishDialog.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\goal\GoalTemplateViewModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\di\ViewModelModule.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\task\KanbanViewModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\components\goal\GoalAssistantButtons.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\viewmodel\BackupSettingsViewModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\statistics\TimeStatisticsScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\repository\TimeAnalyticsRepository.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\model\NotificationConfig.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\ai\AiAssistantScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\widget\WidgetUpdateManager.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\di\AnalyticsModule.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\navigation\AppDestinations.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\repository\AiTaskRepositoryImpl.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\di\MedicationModule.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\entity\AppUsageEntity.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\service\TaskReminderScheduler.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\profile\ProfileScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\utils\TimeFlowNotificationManager.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\task\components\common\event\EventBus.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\utils\ImageUtils.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\milestone\MilestoneViewModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\service\FocusTimerManager.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\home\UnifiedHomeScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\navigation\NavAnimations.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\task\model\SortOption.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\entity\Priority.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\di\UtilsModule.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\discover\DiscoverViewModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\profile\EmotionRecordReviewScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\task\SubTask.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\health\SymptomsDetailScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\timetracking\screens\TimeAnalyticsDetailScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\wishlist\WishListScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\goal\GoalTemplateEditScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\utils\TaskReminderUtils.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\navigation\NavigationGraph.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\task\components\AiSuggestionArea.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\task\TaskViewModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\components\goal\SmartTemplateList.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\goal\SaveGoalAsTemplateScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\utils\ActivityContextProvider.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\components\WishToGoalAnimation.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\goal\GoalDetailScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\domain\usecase\goal\QuickGoalCreationUseCase.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\task\model\FeedbackData.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\ai\model\AiSettings.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\MainActivity.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\entity\Wish.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\goal\SmartCategoryTestScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\viewmodel\WishStatisticsViewModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ai\GoalCategoryClassifier.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\model\GoalType.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\dao\WishDao.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\widget\QuickTimerWidget.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\converter\Converters.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\utils\RippleOptimizer.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\model\GoalTemplate.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\milestone\MilestoneType.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\entity\SymptomRecord.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\model\Goal.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\converter\LocalDateConverter.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\dao\ReflectionDao.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\di\WorkManagerInitializer.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\widget\WeeklyStatsWidget.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\viewmodel\GoalCreationViewModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\model\Task.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\dao\KanbanBoardDao.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\task\components\common\TaskCardTheme.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\util\MarkdownFormatter.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\utils\TaskPersistentNotificationTestHelper.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\navigation\ScreenWrappers.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\preferences\UserPreferencesManager.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\task\components\common\state\ManagedState.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\settings\SyncRepositoryImpl.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\domain\usecase\TaskTimeUseCase.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\repository\HabitRepository.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\repository\WishRepository.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\milestone\MilestoneDetailDialog.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\service\MedicationReminderManager.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\milestone\EditMilestoneScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\viewmodel\BackupRestoreViewModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\navigation\BottomNavItem.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\theme\AnalyticsTheme.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\calendar\CalendarScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\converter\StringListConverter.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\service\FocusTimerService.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\goal\GoalViewModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\service\TaskPersistentNotificationService.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\service\SimpleRealDataStatisticsService.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\util\EventListener.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\health\AddHabitViewModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\repository\GoalRepository.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\settings\ColorType.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\MainActivity.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\task\model\TasksStatistics.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\goal\GoalTemplateImportScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\dao\MedicationDao.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\optimization\OptimizedTaskListComponents.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\ai\model\AiConversation.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\model\HabitModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\repository\TimeSessionRepository.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\di\DataStoreModule.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\settings\DataManagementScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\model\AiConfig.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\TimeFlowApplication.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\profile\DetailedEmotionRecordScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\di\AppInitializer.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\di\AppModule.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\goal\GoalBreakdownScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\components\LoadingContent.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\health\PeriodAnalyticsScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\wishlist\WishStatisticsScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\viewmodel\DataManagementViewModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\debug\PriorityUpdateTest.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\analytics\AnalyticsViewModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\reflection\ReflectionScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\reflection\ReflectionModels.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\dao\AppUsageDao.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\goal\components\RecurringSettingsPanel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\viewmodel\HabitViewModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\model\RecurringSettings.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\model\TimeSlotInfo.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\di\DatabaseModule.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\ai\model\AiTaskDecomposition.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\ai\model\SentStatus.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\settings\SettingsScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\navigation\TimeFlowNavHost.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\notification\GoalNotificationScheduler.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\utils\SafeParcelHelper.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\milestone\MilestoneInsights.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\reflection\data\ReflectionRepositoryImpl.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\task\components\TaskGroupList.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\task\AddTaskScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\repository\AiTaskRepository.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\analytics\PeriodAnalytics.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\service\AiSuggestionScheduler.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\language\LanguageManager.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\service\NotificationConfigManager.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\dao\CycleDao.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\di\PreferenceKeys.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\util\DataConsistencyFixer.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\model\WorkManager.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\settings\SyncSettingsViewModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\model\TaskGroup.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\ai\AIReviewViewModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\components\MiniTimerBar.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\model\ViewTimeSlot.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\worker\DailyReviewWorker.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\task\components\VoiceInputDialog.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\milestone\MilestoneScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\utils\LogConfig.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\profile\ProfileViewModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\entity\TaskTag.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\task\components\AiSuggestionCard.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\model\Reflection.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\milestone\Milestone.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\receiver\TaskAlarmReceiver.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\utils\PerformanceOptimizer.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\dao\MedicationRecordDao.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\service\TaskPersistentNotificationManager.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\reflection\components\ShareableReflectionCard.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\profile\EmotionRecordDetailScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\repository\HabitRepositoryImpl.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\settings\DataRecoveryScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\settings\BackupSettingsScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\statistics\TimeStatisticsViewModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\theme\Color.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\di\SingletonModules.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\navigation\NavigationRoute.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\model\GoalWizardModels.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\theme\Theme.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\reflection\ReflectionViewModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\task\TaskTimeSync.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\analytics\AnalyticsComponents.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\repository\TaskTimeRepository.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\health\FrequencyType.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\dao\GoalTemplateDao.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\utils\DateFormatUtils.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\model\AiApiResult.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\timetracking\TaskTimeStatisticsViewModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\model\GoalCategory.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\repository\TaskRepository.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\task\model\ViewMode.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\entity\GoalTemplate.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\entity\EmotionRecordEntity.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\service\DailyReviewDataService.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\converter\DateTimeConverter.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\utils\AppExceptionHandler.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\calendar\components\FloatingTasksSection.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\worker\RecurringTaskWorker.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\goal\GoalTemplateListScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\service\TimeTrackingService.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\service\RecurrenceCalculator.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\task\TaskFeedbackDialog.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\repository\impl\MedicationRepositoryImpl.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\navigation\LocalNavController.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\components\WishCloudSection.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\goal\EditGoalScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\repository\TimeAnalyticsRepositoryImpl.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\di\StatisticsModule.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\settings\SettingsViewModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\domain\usecase\goal\SmartTemplateUseCase.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\task\TaskDetailViewModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\milestone\ViewMode.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\components\StatsCard.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\util\DataConsistencyHelper.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\language\LanguageSettingsScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\reflection\data\SearchSuggestionServiceImpl.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\repository\KanbanRepository.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\task\TaskQuickDetailSheet.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\milestone\TimelineView.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\SharedFilterState.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\utils\SystemBarManager.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\theme\Type.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\utils\RestrictedReflection.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\calendar\DimensionUtils.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\timetracking\TaskTimeStatisticsScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\di\WishModule.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\goal\components\ReminderSettingsPanel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\repository\WishRepositoryImpl.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\widget\TodayTasksWidget.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\utils\DateTimeUtils.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\receiver\FocusTimerActionReceiver.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\settings\AiSettingsScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\milestone\YearlyView.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\dao\TaskDao.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\task\components\GoalSelectionComponent.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\timetracking\TimeTrackingViewModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\widget\FocusTimerWidget.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\profile\EmotionStatisticsScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\ai\model\AiTimeEstimation.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\task\model\TaskDataExtensions.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\task\components\common\cache\SmartCacheManager.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\components\payment\PaymentDialog.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\reflection\components\ShareOptionsDialog.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\task\TaskDetailScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\health\ProfessionalMedicationScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\task\model\TaskStatus.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\worker\HabitReminderWorker.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\model\RecurrenceModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\statistics\SimpleRealTimeStatisticsViewModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\utils\SafeImageLoader.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\repository\MedicationRepository.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\model\GoalSubTask.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\reflection\model\TimeViewModels.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\components\BottomNavBar.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\task\TaskContent.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\milestone\MilestoneTopBar.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\TimeFlowApp.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\receiver\BootCompletedReceiver.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\entity\MedicationRecord.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\task\components\PriorityPicker.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\task\model\TaskModels.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\timetracking\components\TimeTrackingComponents.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\utils\ViewExtensions.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\algorithm\PeriodPredictionAlgorithm.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\entity\ReflectionEntity.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\theme\GlassPanel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\model\HistoryPeriodModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\utils\DatabaseBackupManager.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\model\TimeSession.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\task\TaskTimeSyncTest.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\utils\ImageLoader.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\task\TaskDetailBottomSheet.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\widget\TimeInsightWidget.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\util\HabitGoalManager.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\settings\CloudStorageConfigScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\navigation\ScreenWrappersFix.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\repository\GoalTemplateRepository.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\model\Medication.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\repository\UserPreferenceRepository.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\components\WishLinkedGoalCard.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\utils\HwcLutsErrorFixer.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\goal\GoalManagementScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\calendar\CalendarViewModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\model\TaskTime.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\components\GlassmorphicCard.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\navigation\BottomNavAnimationManager.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\utils\PreferenceKeys.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\dao\HabitDao.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\receiver\TaskPersistentNotificationActionReceiver.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\repository\KanbanBoardRepositoryImpl.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\converter\TaskConverter.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\utils\ActivityExtensions.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\home\UnifiedHomeScreenOptimized.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\base\SafeViewModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\viewmodel\WishListViewModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\components\ai\AiPromptInput.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\utils\SafeNavigation.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\widget\TimerWidgetUpdater.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\navigation\AnimationConfigurator.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\model\RecurringPeriod.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\discover\DiscoverScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\utils\BinderTransactionHelper.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\worker\AiSuggestionWorker.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\utils\TemplateCache.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\model\Priority.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\goal\components\AiTaskBreakdownScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\health\EnhancedHealthComponents.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\milestone\YearTimelineView.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\service\DailyReviewNotificationGenerator.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\goal\GoalDashboardScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\task\components\common\BaseTaskCard.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\goal\GoalSetupWizardScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\home\HomeReflectionSection.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\navigation\AppDestinations.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\task\TaskListFullScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\health\PeriodEditDialogs.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\utils\ColorUtils.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\model\DataLoadingState.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\model\MoodType.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\reflection\model\ReflectionListState.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\settings\NotificationSettingsViewModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\goal\GoalAnalysisTest.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\components\LoadingIndicator.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\reflection\ReflectionComponents.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\navigation\Screen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\navigation\NavGraph.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\repository\HistoryPeriodRepository.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\utils\CycleUtils.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\ai\AIReviewScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\task\components\common\util\RetryUtil.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\task\components\SwipeableTaskCard.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\test\NotificationSettingsTest.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\components\ErrorState.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\milestone\MilestoneCategory.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\utils\ReflectionShareManager.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\ai\model\AiTaskInsight.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\model\StatisticsModels.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\goal\components\AiBreakdownProcessScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\task\components\OptimizedTaskListView.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\receiver\DailyReviewAlarmReceiver.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\utils\SafetyGuard.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\model\Event.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\model\WishModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\util\TaskUtils.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\worker\TaskReminderWorker.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\components\CalendarGrid.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\goal\components\AiBreakdownResultScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\model\AiConfigModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\widget\GoalProgressWidget.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\task\components\DraggableKanbanBoard.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\timetracking\components\CalendarInfo.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\mapper\ReflectionMapper.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\model\TaskTag.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\settings\ThemeSettingsViewModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\dao\TimeSessionDao.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\service\AutoBackupService.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\entity\Task.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\entity\CycleRecord.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\task\components\common\EnhancedTaskCard.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\components\EmptyState.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\utils\NotificationHelper.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\settings\PresetThemeScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\model\PaymentInfo.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\components\goal\SmartCategoryRecommendation.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\settings\AboutScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\analytics\AnalyticsInsightService.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\theme\ThemeModels.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\repository\BaseRepository.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\analytics\AnalyticsDataService.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\analytics\AnalyticsScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\health\HabitTrackerScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\service\DailyReviewScheduler.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\reflection\ReflectionDetailScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\db\AppDatabase.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\health\PeriodHistoryScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\task\components\common\cache\ChangeTracker.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\repository\KanbanColumnRepositoryImpl.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\task\TaskAdapters.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\di\TaskTimeModule.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\components\LoadingState.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\notification\GoalNotificationManager.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\worker\AutoBackupWorker.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\viewmodel\AiConfigViewModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\navigation\TimeFlowNavHost.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\viewmodel\AiSettingsViewModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\repository\CycleRepository.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\task\components\TaskFilters.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\task\SharedFilterState.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\milestone\MilestoneRoutes.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\analytics\AnalyticsContent.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\calendar\components\ModernDayView.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\components\ChartCard.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\utils\EventBus.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\viewmodel\TaskTimeViewModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\task\TaskTimeManager.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\repository\DefaultTemplateInitializer.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\utils\TimeZoneUtils.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\worker\OverdueTaskCheckWorker.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\task\components\TaskListTopBar.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\settings\ThemeSettingsScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\task\components\SmartTaskInput.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\wishlist\WishRealizationAnimationEffect.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\health\MenstrualCycleScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\widget\TodayTasksDataProvider.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\initializer\RecurringTaskInitializer.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\utils\SettingsManager.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\utils\RenderOptimizer.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\repository\EventRepository.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\task\components\HierarchicalTaskList.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\task\model\TaskData.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\task\model\TaskStatusChange.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\service\PaymentManager.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\repository\SharedFilterState.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\entity\TaskClosure.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\settings\AiModelSettingsScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\navigation\LocalNavigationDestination.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\navigation\TimeFlowNavigator.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\optimization\ComposeRecompositionOptimizer.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\task\components\SegmentedControl.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\milestone\RichTextEditor.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\task\temp.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\ai\model\AiConfig.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\task\TaskModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\entity\Goal.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\receiver\HabitAlarmReceiver.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\utils\NavigationOptimizer.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\settings\NotificationSettingsScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\task\components\common\cache\TaskRepositoryCache.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\health\HabitDetailScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\converter\ListStringConverter.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\theme\DynamicThemeHelper.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\health\ProfessionalMedicationViewModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\viewmodel\AiAssistantViewModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\viewmodel\TimeFlowViewModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\util\NotificationCenter.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\task\TaskEditScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\task\TaskListOptimizer.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\task\TaskViewModel.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\entity\KanbanBoard.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\repository\TransactionHelper.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\theme\Shape.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\model\ReminderType.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\di\SyncModule.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\components\goal\GoalCategorySelector.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\goal\GoalCategoryManagementScreen.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\model\KanbanColumn.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\entity\Habit.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\utils\NotificationPermissionHelper.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\calendar\components\CreateFloatingTaskDialog.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\repository\KanbanColumnRepository.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\data\dao\KanbanColumnDao.kt
D:\development\Codes\MyApplication\app\src\main\kotlin\com\timeflow\app\ui\screen\goal\GoalCompletionAnalysisScreen.kt