package com.timeflow.app.di

import com.timeflow.app.data.service.SimpleRealDataStatisticsService
import com.timeflow.app.data.repository.TaskRepository
import com.timeflow.app.ui.screen.reflection.ReflectionRepository
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 统计服务依赖注入模块
 */
@Module
@InstallIn(SingletonComponent::class)
object StatisticsModule {

    @Provides
    @Singleton
    fun provideSimpleRealDataStatisticsService(
        taskRepository: TaskRepository,
        reflectionRepository: ReflectionRepository
    ): SimpleRealDataStatisticsService {
        return SimpleRealDataStatisticsService(
            taskRepository = taskRepository,
            reflectionRepository = reflectionRepository
        )
    }
}
