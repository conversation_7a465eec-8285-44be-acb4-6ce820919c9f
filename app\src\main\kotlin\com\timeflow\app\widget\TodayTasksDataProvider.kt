package com.timeflow.app.widget

import android.content.Context
import android.util.Log
import com.timeflow.app.widget.data.TodayTasksData
import com.timeflow.app.widget.data.DisplayTask
import com.timeflow.app.widget.data.SimpleTask
import com.timeflow.app.widget.data.SimpleTaskWithPriority

/**
 * 今日待办小组件数据提供者
 * 负责获取和处理今日任务数据
 */
class TodayTasksDataProvider(
    private val context: Context
) {

    companion object {
        private const val TAG = "TodayTasksDataProvider"
        private const val MAX_TASKS_DISPLAY = 4
    }

    /**
     * 获取今日待办任务数据
     * 暂时返回默认数据，后续可以集成真实的任务数据
     */
    fun getTodayTasksData(): TodayTasksData {
        return try {
            // TODO: 集成真实的任务数据获取
            // 目前返回模拟数据
            Log.d(TAG, "获取今日任务数据（使用模拟数据）")
            getDefaultTasksData()
        } catch (e: Exception) {
            Log.e(TAG, "获取今日任务数据失败", e)
            // 返回默认数据
            getDefaultTasksData()
        }
    }
    

    
    /**
     * 获取默认任务数据（当数据获取失败时使用）
     */
    private fun getDefaultTasksData(): TodayTasksData {
        val defaultTasks = listOf(
            SimpleTaskWithPriority("1", "小组件设计", false, "HIGH"),
            SimpleTaskWithPriority("2", "《哈利波特》", false, "MEDIUM"),
            SimpleTaskWithPriority("3", "每天回答3道面", false, "LOW"),
            SimpleTaskWithPriority("4", "一周锻炼5天", true, "MEDIUM")
        )

        val incompleteTasks = defaultTasks.filter { !it.isCompleted }
        val completedTasks = defaultTasks.filter { it.isCompleted }
        
        val allDisplayTasks = defaultTasks.map { task ->
            DisplayTask(
                id = task.id ?: "unknown",
                title = task.title,
                isCompleted = task.isCompleted,
                priority = task.priority
            )
        }

        return TodayTasksData(
            tasks = allDisplayTasks,
            totalTasks = defaultTasks.size,
            totalCompleted = completedTasks.size,
            totalIncomplete = incompleteTasks.size
        )
    }
}




