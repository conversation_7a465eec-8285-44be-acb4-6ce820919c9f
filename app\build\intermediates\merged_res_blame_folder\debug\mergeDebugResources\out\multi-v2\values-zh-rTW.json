{"logs": [{"outputFile": "com.timeflow.app-mergeDebugResources-85:/values-zh-rTW/values-zh-rTW.xml", "map": [{"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\443a6cfc0de72abe8b7a8ef4c7b7a3e3\\transformed\\core-1.12.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "96,97,98,99,100,101,102,531", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "6502,6594,6693,6787,6881,6974,7067,34661", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "6589,6688,6782,6876,6969,7062,7158,34757"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\6e1bec7b61f7c5e14c57eb6146ea2199\\transformed\\ui-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,182,258,345,436,514,588,665,743,818,883,948,1021,1096,1164,1238,1306", "endColumns": "76,75,86,90,77,73,76,77,74,64,64,72,74,67,73,67,115", "endOffsets": "177,253,340,431,509,583,660,738,813,878,943,1016,1091,1159,1233,1301,1417"}, "to": {"startLines": "112,113,138,139,158,243,245,391,404,418,420,437,438,518,535,539,545", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7709,7786,9140,9227,10303,15319,15445,26669,27341,28294,28393,29330,29403,33968,35007,35228,35571", "endColumns": "76,75,86,90,77,73,76,77,74,64,64,72,74,67,73,67,115", "endOffsets": "7781,7857,9222,9313,10376,15388,15517,26742,27411,28354,28453,29398,29473,34031,35076,35291,35682"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\5619827a7b7a666ff443af1afbf85287\\transformed\\preference-1.2.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,5,6,7,8", "startColumns": "4,4,4,4,4,4", "startOffsets": "105,171,322,442,610,689", "endColumns": "65,80,119,167,78,75", "endOffsets": "166,247,437,605,684,760"}, "to": {"startLines": "123,175,427,533,566,567", "startColumns": "4,4,4,4,4,4", "startOffsets": "8297,11381,28782,34801,36747,36826", "endColumns": "65,80,119,167,78,75", "endOffsets": "8358,11457,28897,34964,36821,36897"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\8502c7d6e294f2825540546f57fbfcc0\\transformed\\material-1.11.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,311,373,440,510,587,681,788,861,923,1001,1061,1121,1199,1260,1318,1374,1434,1492,1546,1631,1687,1745,1799,1864,1956,2030,2107,2227,2290,2353,2452,2529,2603,2653,2704,2770,2833,2901,2979,3050,3111,3182,3249,3311,3398,3477,3542,3625,3710,3784,3848,3924,3972,4045,4109,4185,4263,4325,4389,4452,4518,4598,4676,4752,4831,4885,4940", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,63,61,66,69,76,93,106,72,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,76,119,62,62,98,76,73,49,50,65,62,67,77,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68", "endOffsets": "242,306,368,435,505,582,676,783,856,918,996,1056,1116,1194,1255,1313,1369,1429,1487,1541,1626,1682,1740,1794,1859,1951,2025,2102,2222,2285,2348,2447,2524,2598,2648,2699,2765,2828,2896,2974,3045,3106,3177,3244,3306,3393,3472,3537,3620,3705,3779,3843,3919,3967,4040,4104,4180,4258,4320,4384,4447,4513,4593,4671,4747,4826,4880,4935,5004"}, "to": {"startLines": "2,87,88,89,90,91,106,107,109,174,179,238,248,317,318,319,320,321,322,323,324,325,326,327,328,329,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,422", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,5994,6058,6120,6187,6257,7301,7395,7539,11319,11608,15069,15637,21806,21884,21945,22003,22059,22119,22177,22231,22316,22372,22430,22484,22549,23596,23670,23747,23867,23930,23993,24092,24169,24243,24293,24344,24410,24473,24541,24619,24690,24751,24822,24889,24951,25038,25117,25182,25265,25350,25424,25488,25564,25612,25685,25749,25825,25903,25965,26029,26092,26158,26238,26316,26392,26471,26525,28509", "endLines": "5,87,88,89,90,91,106,107,109,174,179,238,248,317,318,319,320,321,322,323,324,325,326,327,328,329,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,422", "endColumns": "12,63,61,66,69,76,93,106,72,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,76,119,62,62,98,76,73,49,50,65,62,67,77,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68", "endOffsets": "292,6053,6115,6182,6252,6329,7390,7497,7607,11376,11681,15124,15692,21879,21940,21998,22054,22114,22172,22226,22311,22367,22425,22479,22544,22636,23665,23742,23862,23925,23988,24087,24164,24238,24288,24339,24405,24468,24536,24614,24685,24746,24817,24884,24946,25033,25112,25177,25260,25345,25419,25483,25559,25607,25680,25744,25820,25898,25960,26024,26087,26153,26233,26311,26387,26466,26520,26575,28573"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\71256635af31e1aec955d3a558d276eb\\transformed\\foundation-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,138", "endColumns": "82,78", "endOffsets": "133,212"}, "to": {"startLines": "558,559", "startColumns": "4,4", "startOffsets": "36293,36376", "endColumns": "82,78", "endOffsets": "36371,36450"}}, {"source": "D:\\development\\Codes\\MyApplication\\app\\src\\main\\res\\values-zh-rTW\\strings.xml", "from": {"startLines": "70,285,426,393,391,390,392,389,81,415,418,302,14,127,128,330,15,283,284,242,243,241,244,236,235,238,237,234,233,334,402,7,366,53,54,2,67,85,470,464,261,382,87,45,46,47,468,288,367,369,483,465,387,388,432,368,10,26,73,259,257,83,263,86,36,37,380,333,74,357,358,150,151,305,376,474,436,38,28,25,370,409,377,281,282,356,27,295,80,364,365,427,116,396,115,117,118,359,8,457,458,459,395,78,425,79,275,276,273,272,274,155,157,156,154,158,159,58,129,92,475,476,477,383,384,306,307,314,315,312,313,316,317,318,319,320,321,310,311,308,309,100,98,99,132,101,103,105,107,104,106,21,90,174,175,346,347,348,349,440,441,55,110,112,109,111,113,139,66,406,419,420,342,343,344,345,414,442,443,324,3,479,480,290,481,381,95,325,60,59,52,252,466,360,266,91,262,385,386,429,33,35,34,450,446,447,448,449,444,445,18,19,135,399,143,372,267,271,424,88,77,473,484,467,374,405,404,417,416,335,301,296,162,163,166,167,168,169,164,165,451,452,68,253,75,373,22,24,433,20,89,39,40,69,254,9,29,398,401,336,147,268,293,294,82,258,84,62,61,289,472,462,463,375,471,339,140,72,205,226,230,214,212,216,178,179,181,180,203,227,222,229,219,196,198,197,199,208,202,215,220,225,213,209,224,192,193,195,194,228,217,206,223,221,187,188,189,191,190,204,210,211,218,182,183,184,185,186,207,247,41,42,328,482,329,353,403,439,126,350,352,351,65,23,56,453,454,469,478,93,141,361,279,280,172,340,341,4,5,43,44,32,251,138,6,120,121,394,173,123,122,57,136,421,48,49,260,71,455,456,300,94,137,144,371,299,11,428,397,400,76,248", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3030,13306,19662,18329,18220,18164,18278,18106,3430,19170,19315,13899,474,5346,5399,15344,528,13205,13250,11483,11558,11407,11640,11034,10969,11288,11220,10782,10718,15487,18723,247,16965,2258,2304,55,2872,3577,21816,21517,12325,17705,3651,1904,1953,2012,21724,13375,17007,17090,22445,21575,17992,18043,19910,17053,367,984,3142,12231,12129,3503,12433,3614,1432,1478,17603,15439,3180,16593,16635,6106,6161,13962,17425,22002,20025,1534,1074,940,17140,19028,17480,13091,13140,16550,1031,13626,3392,16846,16893,19719,4853,18480,4787,4909,4975,16681,288,21213,21260,21323,18430,3320,19598,3356,12863,12913,12766,12718,12815,6322,6492,6405,6236,6578,6662,2511,5460,3846,22065,22110,22166,17763,17815,14009,14061,14526,14581,14397,14454,14654,14710,14782,14837,14909,14968,14259,14320,14129,14186,4125,4018,4071,5542,4181,4232,4339,4450,4287,4390,743,3765,7481,7533,16080,16129,16190,16246,20167,20230,2359,4557,4669,4501,4619,4728,5791,2822,18925,19372,19435,15847,15896,15957,16013,19122,20303,20354,15070,96,22257,22302,13452,22355,17653,3961,15122,2606,2555,2207,11923,21619,16726,12512,3803,12363,17884,17931,19831,1251,1365,1305,20807,20539,20600,20669,20734,20417,20471,596,645,5633,18608,5908,17215,12561,12676,19551,3687,3287,21951,22493,21674,17305,18871,18821,19272,19213,15528,13837,13683,6768,6839,7065,7137,7217,7288,6922,6988,20877,20931,2925,11983,3219,17254,790,889,19954,693,3724,1590,1636,2980,12048,328,1122,18569,18682,15574,6045,12609,13520,13567,3465,12180,3540,2714,2669,13413,21906,21396,21445,17361,21861,15655,5829,3106,9212,10408,10640,9723,9605,9834,7605,7663,7801,7730,9097,10467,10174,10577,10004,8744,8877,8804,8946,9383,9036,9779,10060,10348,9665,9440,10288,8490,8547,8680,8612,10522,9888,9272,10231,10115,8180,8235,8299,8428,8362,9154,9497,9551,9944,7866,7920,7985,8050,8117,9329,11729,1692,1741,15220,22400,15284,16486,18763,20109,5287,16315,16431,16374,2782,841,2406,20995,21044,21770,22211,3883,5869,16785,12986,13033,7387,15700,15764,132,169,1799,1847,1203,11864,5751,211,5041,5095,18382,7436,5206,5151,2458,5671,19497,2065,2117,12280,3067,21100,21153,13788,3922,5710,5964,17177,13744,407,19784,18534,18644,3253,11781", "endColumns": "36,46,56,52,57,55,50,57,34,42,56,41,53,52,60,75,42,44,55,74,81,75,69,185,64,97,67,186,63,40,39,40,41,45,54,40,52,36,44,57,37,57,35,48,58,52,45,37,45,49,47,43,50,62,43,36,39,46,37,48,50,36,59,36,45,55,49,47,38,41,45,54,52,46,54,62,64,55,47,43,36,57,103,48,64,42,42,56,37,46,71,64,55,53,65,65,64,44,39,46,62,53,49,35,63,35,49,51,48,47,47,82,85,86,85,83,86,43,60,36,44,55,44,51,68,51,67,54,72,56,71,55,71,54,71,58,80,60,76,56,72,55,52,53,69,49,54,50,49,51,59,46,37,51,50,48,60,55,68,62,72,46,61,58,55,49,57,37,49,83,62,61,48,60,55,66,47,50,62,51,35,44,52,46,44,51,35,78,62,50,50,59,54,58,48,42,69,46,60,59,53,66,59,69,60,68,64,72,53,67,48,47,37,35,55,38,47,41,46,36,32,50,52,49,55,53,49,42,58,45,61,39,70,82,71,79,70,79,65,76,53,63,54,64,33,50,50,50,51,49,40,45,55,49,59,38,55,38,40,61,41,45,46,58,37,50,36,46,44,38,44,48,71,63,44,44,39,35,59,58,56,55,59,53,57,66,64,70,56,54,56,62,55,59,68,72,68,56,60,54,54,59,57,56,59,56,64,63,67,54,55,56,56,58,54,63,62,61,65,57,53,53,59,53,64,64,66,62,53,51,48,57,63,44,59,44,57,57,58,58,54,56,39,47,51,48,55,45,45,38,37,41,46,57,48,63,82,36,41,47,56,47,58,39,35,53,55,47,44,59,54,52,38,35,51,66,44,38,52,59,48,38,40,61,37,43,41,46,34,37,33,58", "endOffsets": "3062,13348,19714,18377,18273,18215,18324,18159,3460,19208,19367,13936,523,5394,5455,15415,566,13245,13301,11553,11635,11478,11705,11215,11029,11381,11283,10964,10777,15523,18758,283,17002,2299,2354,91,2920,3609,21856,21570,12358,17758,3682,1948,2007,2060,21765,13408,17048,17135,22488,21614,18038,18101,19949,17085,402,1026,3175,12275,12175,3535,12488,3646,1473,1529,17648,15482,3214,16630,16676,6156,6209,14004,17475,22060,20085,1585,1117,979,17172,19081,17579,13135,13200,16588,1069,13678,3425,16888,16960,19779,4904,18529,4848,4970,5035,16721,323,21255,21318,21372,18475,3351,19657,3387,12908,12960,12810,12761,12858,6400,6573,6487,6317,6657,6744,2550,5516,3878,22105,22161,22206,17810,17879,14056,14124,14576,14649,14449,14521,14705,14777,14832,14904,14963,15044,14315,14392,14181,14254,4176,4066,4120,5607,4226,4282,4385,4495,4334,4445,785,3798,7528,7579,16124,16185,16241,16310,20225,20298,2401,4614,4723,4552,4664,4781,5824,2867,19004,19430,19492,15891,15952,16008,16075,19165,20349,20412,15117,127,22297,22350,13494,22395,17700,3992,15196,2664,2601,2253,11978,21669,16780,12556,3841,12428,17926,17987,19886,1300,1427,1360,20872,20595,20664,20729,20802,20466,20534,640,688,5666,18639,5959,17249,12604,12713,19593,3719,3315,21997,22541,21719,17356,18920,18866,19310,19267,15569,13894,13718,6834,6917,7132,7212,7283,7363,6983,7060,20926,20990,2975,12043,3248,17300,836,935,20001,738,3760,1631,1687,3025,12103,362,1173,18603,18718,15631,6082,12650,13562,13621,3498,12226,3572,2756,2709,13447,21946,21440,21512,17420,21901,15695,5864,3137,9267,10462,10692,9774,9660,9883,7658,7725,7861,7796,9149,10517,10226,10635,10055,8799,8941,8872,9010,9435,9092,9829,10110,10403,9718,9492,10343,8542,8607,8739,8675,10572,9939,9324,10283,10169,8230,8294,8357,8485,8423,9207,9546,9600,9999,7915,7980,8045,8112,8175,9378,11776,1736,1794,15279,22440,15339,16526,18816,20162,5341,16369,16481,16426,2817,884,2453,21039,21095,21811,22252,3917,5902,16822,13028,13086,7431,15759,15842,164,206,1842,1899,1246,11918,5786,242,5090,5146,18425,7476,5261,5201,2506,5705,19528,2112,2179,12320,3101,21148,21208,13832,3956,5746,6021,17210,13783,444,19826,18564,18677,3282,11835"}, "to": {"startLines": "33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,92,93,94,95,103,104,105,108,110,111,114,115,116,117,118,119,120,121,122,124,125,126,127,128,129,130,131,132,133,134,135,136,137,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,176,177,178,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,239,240,241,242,244,246,247,249,250,251,252,253,254,255,256,257,258,259,316,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,389,390,392,393,394,395,396,397,398,399,400,401,402,403,405,406,407,408,409,410,411,412,413,414,415,416,417,419,421,423,424,425,426,428,429,430,431,432,433,434,435,436,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,519,520,521,522,523,524,525,526,527,528,529,530,532,534,536,537,538,540,541,542,543,544,546,547,548,549,550,551,552,553,554,555,556,557,560,561,562,563,564,565,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2863,2900,2947,3004,3057,3115,3171,3222,3280,3315,3358,3415,3457,3511,3564,3625,3701,3744,3789,3845,3920,4002,4078,4148,4334,4399,4497,4565,4752,4816,4857,4897,4938,4980,5026,5081,5122,5175,5212,5257,5315,5353,5411,5447,5496,5555,5608,5654,5692,5738,5788,5836,5880,5931,6334,6378,6415,6455,7163,7201,7250,7502,7612,7672,7862,7908,7964,8014,8062,8101,8143,8189,8244,8363,8410,8465,8528,8593,8649,8697,8741,8778,8836,8940,8989,9054,9097,9318,9375,9413,9460,9532,9597,9653,9707,9773,9839,9904,9949,9989,10036,10099,10153,10203,10239,10381,10417,10467,10519,10568,10616,10664,10747,10833,10920,11006,11090,11177,11221,11282,11462,11507,11563,11686,11738,11807,11859,11927,11982,12055,12112,12184,12240,12312,12367,12439,12498,12579,12640,12717,12774,12847,12903,12956,13010,13080,13130,13185,13236,13286,13338,13398,13445,13483,13535,13586,13635,13696,13752,13821,13884,13957,14004,14066,14125,14181,14231,14289,14327,14377,14461,14524,14586,14635,14696,14752,14819,14867,14918,14981,15033,15129,15174,15227,15274,15393,15522,15558,15697,15760,15811,15862,15922,15977,16036,16085,16128,16198,16245,21746,22641,22695,22762,22822,22892,22953,23022,23087,23160,23214,23282,23331,23379,23417,23453,23509,23548,26580,26622,26747,26784,26817,26868,26921,26971,27027,27081,27131,27174,27233,27279,27416,27456,27527,27610,27682,27762,27833,27913,27979,28056,28110,28174,28229,28359,28458,28578,28629,28680,28732,28902,28943,28989,29045,29095,29155,29194,29250,29289,29478,29540,29582,29628,29675,29734,29772,29823,29860,29907,29952,29991,30036,30085,30157,30221,30266,30311,30351,30466,30526,30585,30642,30698,30758,30812,30870,30937,31002,31073,31130,31185,31242,31305,31361,31421,31490,31563,31632,31689,31750,31805,31860,31920,31978,32035,32095,32152,32217,32281,32349,32404,32460,32517,32574,32633,32688,32752,32815,32877,32943,33001,33055,33109,33169,33223,33288,33353,33420,33483,33537,33589,33638,33696,33760,33805,33865,33910,34036,34094,34153,34212,34267,34324,34364,34412,34464,34513,34569,34615,34762,34969,35081,35123,35170,35296,35345,35409,35492,35529,35687,35735,35792,35840,35899,35939,35975,36029,36085,36133,36178,36238,36455,36508,36547,36583,36635,36702,36902,36941,36994,37054,37103,37142,37183,37245,37283,37327,37369,37416,37451,37489,37523", "endColumns": "36,46,56,52,57,55,50,57,34,42,56,41,53,52,60,75,42,44,55,74,81,75,69,185,64,97,67,186,63,40,39,40,41,45,54,40,52,36,44,57,37,57,35,48,58,52,45,37,45,49,47,43,50,62,43,36,39,46,37,48,50,36,59,36,45,55,49,47,38,41,45,54,52,46,54,62,64,55,47,43,36,57,103,48,64,42,42,56,37,46,71,64,55,53,65,65,64,44,39,46,62,53,49,35,63,35,49,51,48,47,47,82,85,86,85,83,86,43,60,36,44,55,44,51,68,51,67,54,72,56,71,55,71,54,71,58,80,60,76,56,72,55,52,53,69,49,54,50,49,51,59,46,37,51,50,48,60,55,68,62,72,46,61,58,55,49,57,37,49,83,62,61,48,60,55,66,47,50,62,51,35,44,52,46,44,51,35,78,62,50,50,59,54,58,48,42,69,46,60,59,53,66,59,69,60,68,64,72,53,67,48,47,37,35,55,38,47,41,46,36,32,50,52,49,55,53,49,42,58,45,61,39,70,82,71,79,70,79,65,76,53,63,54,64,33,50,50,50,51,49,40,45,55,49,59,38,55,38,40,61,41,45,46,58,37,50,36,46,44,38,44,48,71,63,44,44,39,35,59,58,56,55,59,53,57,66,64,70,56,54,56,62,55,59,68,72,68,56,60,54,54,59,57,56,59,56,64,63,67,54,55,56,56,58,54,63,62,61,65,57,53,53,59,53,64,64,66,62,53,51,48,57,63,44,59,44,57,57,58,58,54,56,39,47,51,48,55,45,45,38,37,41,46,57,48,63,82,36,41,47,56,47,58,39,35,53,55,47,44,59,54,52,38,35,51,66,44,38,52,59,48,38,40,61,37,43,41,46,34,37,33,58", "endOffsets": "2895,2942,2999,3052,3110,3166,3217,3275,3310,3353,3410,3452,3506,3559,3620,3696,3739,3784,3840,3915,3997,4073,4143,4329,4394,4492,4560,4747,4811,4852,4892,4933,4975,5021,5076,5117,5170,5207,5252,5310,5348,5406,5442,5491,5550,5603,5649,5687,5733,5783,5831,5875,5926,5989,6373,6410,6450,6497,7196,7245,7296,7534,7667,7704,7903,7959,8009,8057,8096,8138,8184,8239,8292,8405,8460,8523,8588,8644,8692,8736,8773,8831,8935,8984,9049,9092,9135,9370,9408,9455,9527,9592,9648,9702,9768,9834,9899,9944,9984,10031,10094,10148,10198,10234,10298,10412,10462,10514,10563,10611,10659,10742,10828,10915,11001,11085,11172,11216,11277,11314,11502,11558,11603,11733,11802,11854,11922,11977,12050,12107,12179,12235,12307,12362,12434,12493,12574,12635,12712,12769,12842,12898,12951,13005,13075,13125,13180,13231,13281,13333,13393,13440,13478,13530,13581,13630,13691,13747,13816,13879,13952,13999,14061,14120,14176,14226,14284,14322,14372,14456,14519,14581,14630,14691,14747,14814,14862,14913,14976,15028,15064,15169,15222,15269,15314,15440,15553,15632,15755,15806,15857,15917,15972,16031,16080,16123,16193,16240,16301,21801,22690,22757,22817,22887,22948,23017,23082,23155,23209,23277,23326,23374,23412,23448,23504,23543,23591,26617,26664,26779,26812,26863,26916,26966,27022,27076,27126,27169,27228,27274,27336,27451,27522,27605,27677,27757,27828,27908,27974,28051,28105,28169,28224,28289,28388,28504,28624,28675,28727,28777,28938,28984,29040,29090,29150,29189,29245,29284,29325,29535,29577,29623,29670,29729,29767,29818,29855,29902,29947,29986,30031,30080,30152,30216,30261,30306,30346,30382,30521,30580,30637,30693,30753,30807,30865,30932,30997,31068,31125,31180,31237,31300,31356,31416,31485,31558,31627,31684,31745,31800,31855,31915,31973,32030,32090,32147,32212,32276,32344,32399,32455,32512,32569,32628,32683,32747,32810,32872,32938,32996,33050,33104,33164,33218,33283,33348,33415,33478,33532,33584,33633,33691,33755,33800,33860,33905,33963,34089,34148,34207,34262,34319,34359,34407,34459,34508,34564,34610,34656,34796,35002,35118,35165,35223,35340,35404,35487,35524,35566,35730,35787,35835,35894,35934,35970,36024,36080,36128,36173,36233,36288,36503,36542,36578,36630,36697,36742,36936,36989,37049,37098,37137,37178,37240,37278,37322,37364,37411,37446,37484,37518,37577"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\e8414bc356727da52467a60536d06b50\\transformed\\material3-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,262,366,468,560,648,752,857,962,1078,1160,1256,1340,1428,1533,1646,1747,1856,1963,2071,2188,2293,2394,2498,2603,2688,2783,2888,2997,3087,3187,3285,3396,3512,3612,3703,3777,3867,3956,4039,4121,4210,4290,4372,4469,4563,4656,4749,4833,4930,5026,5121,5229,5309,5403", "endColumns": "103,102,103,101,91,87,103,104,104,115,81,95,83,87,104,112,100,108,106,107,116,104,100,103,104,84,94,104,108,89,99,97,110,115,99,90,73,89,88,82,81,88,79,81,96,93,92,92,83,96,95,94,107,79,93,91", "endOffsets": "154,257,361,463,555,643,747,852,957,1073,1155,1251,1335,1423,1528,1641,1742,1851,1958,2066,2183,2288,2389,2493,2598,2683,2778,2883,2992,3082,3182,3280,3391,3507,3607,3698,3772,3862,3951,4034,4116,4205,4285,4367,4464,4558,4651,4744,4828,4925,5021,5116,5224,5304,5398,5490"}, "to": {"startLines": "260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16306,16410,16513,16617,16719,16811,16899,17003,17108,17213,17329,17411,17507,17591,17679,17784,17897,17998,18107,18214,18322,18439,18544,18645,18749,18854,18939,19034,19139,19248,19338,19438,19536,19647,19763,19863,19954,20028,20118,20207,20290,20372,20461,20541,20623,20720,20814,20907,21000,21084,21181,21277,21372,21480,21560,21654", "endColumns": "103,102,103,101,91,87,103,104,104,115,81,95,83,87,104,112,100,108,106,107,116,104,100,103,104,84,94,104,108,89,99,97,110,115,99,90,73,89,88,82,81,88,79,81,96,93,92,92,83,96,95,94,107,79,93,91", "endOffsets": "16405,16508,16612,16714,16806,16894,16998,17103,17208,17324,17406,17502,17586,17674,17779,17892,17993,18102,18209,18317,18434,18539,18640,18744,18849,18934,19029,19134,19243,19333,19433,19531,19642,19758,19858,19949,20023,20113,20202,20285,20367,20456,20536,20618,20715,20809,20902,20995,21079,21176,21272,21367,21475,21555,21649,21741"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\2e72a71af7cea86f1e691bad46c56138\\transformed\\appcompat-1.6.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1115,1211,1306,1400,1496,1588,1680,1772,1850,1946,2041,2136,2233,2329,2427,2577,2671", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1110,1206,1301,1395,1491,1583,1675,1767,1845,1941,2036,2131,2228,2324,2422,2572,2666,2745"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,458", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,485,585,667,764,872,949,1024,1116,1210,1307,1403,1498,1592,1688,1780,1872,1964,2042,2138,2233,2328,2425,2521,2619,2769,30387", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "387,480,580,662,759,867,944,1019,1111,1205,1302,1398,1493,1587,1683,1775,1867,1959,2037,2133,2228,2323,2420,2516,2614,2764,2858,30461"}}]}]}