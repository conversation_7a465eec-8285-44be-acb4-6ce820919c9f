<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.timeflow.app"
    android:versionCode="8"
    android:versionName="0.5.3" >

    <uses-sdk
        android:minSdkVersion="26"
        android:targetSdkVersion="34" />

    <!-- Calendar permissions -->
    <uses-permission android:name="android.permission.READ_CALENDAR" />
    <uses-permission android:name="android.permission.WRITE_CALENDAR" />

    <!-- Network permissions -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <!-- Notification permissions -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.VIBRATE" />

    <!-- Background work permissions -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />

    <!-- 🔧 新增：前台服务权限 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />

    <!-- Alarm permissions for medication reminders -->
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />

    <!-- Storage permissions -->
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="29" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />

    <permission
        android:name="com.timeflow.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.timeflow.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:name="com.timeflow.app.TimeFlowApplication"
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:debuggable="true"
        android:enableOnBackInvokedCallback="true"
        android:extractNativeLibs="false"
        android:fullBackupContent="@xml/backup_rules"
        android:hardwareAccelerated="@bool/enable_hardware_acceleration"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.TimeFlow.OptimizedAnimation" >

        <!-- 主活动 - 默认启用硬件加速，但会在运行时根据设备能力进行调整 -->
        <activity
            android:name="com.timeflow.app.ui.MainActivity"
            android:exported="true"
            android:hardwareAccelerated="true"
            android:launchMode="singleTop"
            android:theme="@style/Theme.TimeFlow.OptimizedAnimation"
            android:windowSoftInputMode="adjustResize" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <!-- 添加深度链接支持任务页面直接访问 -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="tasks"
                    android:scheme="timeflow" />
            </intent-filter>
        </activity>

        <!-- 习惯提醒广播接收器 -->
        <receiver
            android:name="com.timeflow.app.receiver.HabitAlarmReceiver"
            android:exported="false" />

        <!-- 任务提醒广播接收器 -->
        <receiver
            android:name="com.timeflow.app.receiver.TaskAlarmReceiver"
            android:exported="false" />

        <!-- 每日回顾闹钟接收器 -->
        <receiver
            android:name="com.timeflow.app.receiver.DailyReviewAlarmReceiver"
            android:exported="false" />

        <!-- 用药提醒广播接收器 -->
        <receiver
            android:name="com.timeflow.app.service.MedicationReminderReceiver"
            android:exported="false" />

        <!-- 用药动作广播接收器 -->
        <receiver
            android:name="com.timeflow.app.service.MedicationActionReceiver"
            android:exported="false" >
            <intent-filter>
                <action android:name="MEDICATION_TAKEN" />
                <action android:name="MEDICATION_SNOOZE" />
            </intent-filter>
        </receiver>

        <!-- 在应用启动时接收启动完成广播，重新设置闹钟 -->
        <receiver
            android:name="com.timeflow.app.receiver.BootCompletedReceiver"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>

        <!-- 通知操作接收器 -->
        <receiver
            android:name="com.timeflow.app.utils.NotificationActionReceiver"
            android:exported="false" >
            <intent-filter>
                <action android:name="COMPLETE_TASK" />
                <action android:name="POSTPONE_TASK" />
                <action android:name="COMPLETE_HABIT" />
                <action android:name="REMIND_HABIT_LATER" />
            </intent-filter>
        </receiver>

        <!-- 🔧 新增：专注计时操作接收器 -->
        <receiver
            android:name="com.timeflow.app.receiver.FocusTimerActionReceiver"
            android:exported="false" >
            <intent-filter>
                <action android:name="START_TIMER" />
                <action android:name="PAUSE_TIMER" />
                <action android:name="RESUME_TIMER" />
                <action android:name="STOP_TIMER" />
                <action android:name="SHOW_APP" />
            </intent-filter>
        </receiver>

        <!-- 🔧 新增：任务常驻通知操作接收器 -->
        <receiver
            android:name="com.timeflow.app.receiver.TaskPersistentNotificationActionReceiver"
            android:exported="false" >
            <intent-filter>
                <action android:name="START_PERSISTENT" />
                <action android:name="STOP_PERSISTENT" />
                <action android:name="COMPLETE_TASK" />
                <action android:name="SHOW_TASKS" />
                <action android:name="REFRESH_TASKS" />
            </intent-filter>
        </receiver>

        <!-- 通知测试服务 -->
        <service
            android:name="com.timeflow.app.service.NotificationTestService"
            android:exported="false" />

        <!-- 🔧 新增：专注计时前台服务 -->
        <service
            android:name="com.timeflow.app.service.FocusTimerService"
            android:exported="false"
            android:foregroundServiceType="specialUse" />

        <!-- 🔧 新增：任务常驻通知前台服务 -->
        <service
            android:name="com.timeflow.app.service.TaskPersistentNotificationService"
            android:exported="false"
            android:foregroundServiceType="specialUse" />

        <!-- WorkManager initialization -->
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.timeflow.app.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="com.timeflow.app.di.WorkManagerInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <!-- Notification channels for OPPO devices -->
        <meta-data
            android:name="android.max_aspect"
            android:value="2.4" />

        <!-- Automatic backup settings -->
        <meta-data
            android:name="com.google.android.backup.api_key"
            android:value="AEdPqrEAAAAIqYVTTbHXb52YeDRQIqNiSUnJtpQEJCOLy7Qo0A" />

        <!-- 添加 FileProvider -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.timeflow.app.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <!-- 小组件 -->
        <!-- 时间洞察小组件 -->
        <receiver
            android:name="com.timeflow.app.widget.TimeInsightWidget"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/time_insight_widget_info" />
        </receiver>

        <!-- 今日待办小组件 -->
        <receiver
            android:name="com.timeflow.app.widget.TodayTasksWidget"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/today_tasks_widget_info" />
        </receiver>

        <!-- 快速计时小组件 -->
        <receiver
            android:name="com.timeflow.app.widget.QuickTimerWidget"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
                <action android:name="com.timeflow.app.widget.START_TIMER" />
                <action android:name="com.timeflow.app.widget.STOP_TIMER" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/quick_timer_widget_info" />
        </receiver>

        <!-- 专注计时器小组件 -->
        <receiver
            android:name="com.timeflow.app.widget.FocusTimerWidget"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
                <action android:name="com.timeflow.app.widget.PLAY_PAUSE" />
                <action android:name="com.timeflow.app.widget.STOP" />
                <action android:name="com.timeflow.app.widget.REFRESH" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/focus_timer_widget_info" />
        </receiver>

        <!-- 周统计小组件 -->
        <receiver
            android:name="com.timeflow.app.widget.WeeklyStatsWidget"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/weekly_stats_widget_info" />
        </receiver>

        <!-- 目标进度小组件 -->
        <receiver
            android:name="com.timeflow.app.widget.GoalProgressWidget"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/goal_progress_widget_info" />
        </receiver>

        <!-- 🔧 循环任务初始化器 -->
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.timeflow.app.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="com.timeflow.app.initializer.RecurringTaskInitializer"
                android:value="androidx.startup" />
        </provider>

        <service
            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_alarm_service_default"
            android:exported="false" />
        <service
            android:name="androidx.work.impl.background.systemjob.SystemJobService"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_job_service_default"
            android:exported="true"
            android:permission="android.permission.BIND_JOB_SERVICE" />
        <service
            android:name="androidx.work.impl.foreground.SystemForegroundService"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_foreground_service_default"
            android:exported="false" />

        <receiver
            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="false" />
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.BATTERY_OKAY" />
                <action android:name="android.intent.action.BATTERY_LOW" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.TIME_SET" />
                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_alarm_service_default"
            android:exported="false" >
            <intent-filter>
                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
            </intent-filter>
        </receiver>

        <activity
            android:name="androidx.compose.ui.tooling.PreviewActivity"
            android:exported="true" />
        <activity
            android:name="androidx.activity.ComponentActivity"
            android:exported="true" />

        <uses-library
            android:name="androidx.window.extensions"
            android:required="false" />
        <uses-library
            android:name="androidx.window.sidecar"
            android:required="false" />

        <service
            android:name="androidx.room.MultiInstanceInvalidationService"
            android:directBootAware="true"
            android:exported="false" />

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>

        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />
    </application>

</manifest>