<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 小组件文本样式 - 简化版 -->
    <style name="Widget.TimeFlow.Text" parent="@android:style/TextAppearance">
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textColor">@color/widget_today_text_primary_light</item>
    </style>

    <style name="Widget.TimeFlow.Text.DisplayLarge" parent="Widget.TimeFlow.Text">
        <item name="android:textSize">57sp</item>
        <item name="android:fontWeight">400</item>
    </style>

    <style name="Widget.TimeFlow.Text.DisplayMedium" parent="Widget.TimeFlow.Text">
        <item name="android:textSize">45sp</item>
        <item name="android:fontWeight">400</item>
    </style>

    <style name="Widget.TimeFlow.Text.DisplaySmall" parent="Widget.TimeFlow.Text">
        <item name="android:textSize">36sp</item>
        <item name="android:fontWeight">400</item>
    </style>

    <style name="Widget.TimeFlow.Text.HeadlineLarge" parent="Widget.TimeFlow.Text">
        <item name="android:textSize">32sp</item>
        <item name="android:fontWeight">400</item>
    </style>

    <style name="Widget.TimeFlow.Text.HeadlineMedium" parent="Widget.TimeFlow.Text">
        <item name="android:textSize">28sp</item>
        <item name="android:fontWeight">400</item>
    </style>

    <style name="Widget.TimeFlow.Text.HeadlineSmall" parent="Widget.TimeFlow.Text">
        <item name="android:textSize">24sp</item>
        <item name="android:fontWeight">400</item>
    </style>

    <style name="Widget.TimeFlow.Text.TitleLarge" parent="Widget.TimeFlow.Text">
        <item name="android:textSize">22sp</item>
        <item name="android:fontWeight">400</item>
    </style>

    <style name="Widget.TimeFlow.Text.TitleMedium" parent="Widget.TimeFlow.Text">
        <item name="android:textSize">16sp</item>
        <item name="android:fontWeight">500</item>
    </style>

    <style name="Widget.TimeFlow.Text.TitleSmall" parent="Widget.TimeFlow.Text">
        <item name="android:textSize">14sp</item>
        <item name="android:fontWeight">500</item>
    </style>

    <style name="Widget.TimeFlow.Text.BodyLarge" parent="Widget.TimeFlow.Text">
        <item name="android:textSize">16sp</item>
        <item name="android:fontWeight">400</item>
    </style>

    <style name="Widget.TimeFlow.Text.BodyMedium" parent="Widget.TimeFlow.Text">
        <item name="android:textSize">14sp</item>
        <item name="android:fontWeight">400</item>
    </style>

    <style name="Widget.TimeFlow.Text.BodySmall" parent="Widget.TimeFlow.Text">
        <item name="android:textSize">12sp</item>
        <item name="android:fontWeight">400</item>
    </style>

    <style name="Widget.TimeFlow.Text.LabelLarge" parent="Widget.TimeFlow.Text">
        <item name="android:textSize">14sp</item>
        <item name="android:fontWeight">500</item>
    </style>

    <style name="Widget.TimeFlow.Text.LabelMedium" parent="Widget.TimeFlow.Text">
        <item name="android:textSize">12sp</item>
        <item name="android:fontWeight">500</item>
    </style>

    <style name="Widget.TimeFlow.Text.LabelSmall" parent="Widget.TimeFlow.Text">
        <item name="android:textSize">11sp</item>
        <item name="android:fontWeight">500</item>
    </style>

    <!-- 小组件按钮样式 -->
    <style name="Widget.TimeFlow.Button" parent="@android:style/Widget.Button">
        <item name="android:background">@drawable/widget_button_background</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:textSize">14sp</item>
        <item name="android:paddingHorizontal">24dp</item>
        <item name="android:paddingVertical">10dp</item>
        <item name="android:minHeight">40dp</item>
    </style>

    <style name="Widget.TimeFlow.Button.Outlined" parent="Widget.TimeFlow.Button">
        <item name="android:background">@drawable/widget_button_outlined_background</item>
        <item name="android:textColor">@color/widget_today_accent_light</item>
    </style>

    <style name="Widget.TimeFlow.Button.Text" parent="Widget.TimeFlow.Button">
        <item name="android:background">@drawable/widget_button_text_background</item>
        <item name="android:textColor">@color/widget_today_accent_light</item>
    </style>

    <!-- 小组件卡片样式 -->
    <style name="Widget.TimeFlow.Card" parent="@android:style/Widget">
        <item name="android:background">@drawable/widget_card_background</item>
        <item name="android:padding">16dp</item>
    </style>

    <style name="Widget.TimeFlow.Card.Outlined" parent="Widget.TimeFlow.Card">
        <item name="android:background">@drawable/widget_card_outlined_background</item>
    </style>
</resources>