package com.timeflow.app.di;

import com.timeflow.app.data.repository.TaskRepository;
import com.timeflow.app.data.service.SimpleRealDataStatisticsService;
import com.timeflow.app.ui.screen.reflection.ReflectionRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class StatisticsModule_ProvideSimpleRealDataStatisticsServiceFactory implements Factory<SimpleRealDataStatisticsService> {
  private final Provider<TaskRepository> taskRepositoryProvider;

  private final Provider<ReflectionRepository> reflectionRepositoryProvider;

  public StatisticsModule_ProvideSimpleRealDataStatisticsServiceFactory(
      Provider<TaskRepository> taskRepositoryProvider,
      Provider<ReflectionRepository> reflectionRepositoryProvider) {
    this.taskRepositoryProvider = taskRepositoryProvider;
    this.reflectionRepositoryProvider = reflectionRepositoryProvider;
  }

  @Override
  public SimpleRealDataStatisticsService get() {
    return provideSimpleRealDataStatisticsService(taskRepositoryProvider.get(), reflectionRepositoryProvider.get());
  }

  public static StatisticsModule_ProvideSimpleRealDataStatisticsServiceFactory create(
      Provider<TaskRepository> taskRepositoryProvider,
      Provider<ReflectionRepository> reflectionRepositoryProvider) {
    return new StatisticsModule_ProvideSimpleRealDataStatisticsServiceFactory(taskRepositoryProvider, reflectionRepositoryProvider);
  }

  public static SimpleRealDataStatisticsService provideSimpleRealDataStatisticsService(
      TaskRepository taskRepository, ReflectionRepository reflectionRepository) {
    return Preconditions.checkNotNullFromProvides(StatisticsModule.INSTANCE.provideSimpleRealDataStatisticsService(taskRepository, reflectionRepository));
  }
}
