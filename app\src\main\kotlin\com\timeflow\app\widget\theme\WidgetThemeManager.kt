package com.timeflow.app.widget.theme

import android.content.Context
import android.content.res.Configuration
import android.graphics.Color
import androidx.annotation.ColorInt
import androidx.annotation.ColorRes
import androidx.core.content.ContextCompat
import com.timeflow.app.R

/**
 * 小组件主题管理器
 * 基于Material Design 3规范，提供统一的主题系统
 */
class WidgetThemeManager private constructor(private val context: Context) {

    companion object {
        @Volatile
        private var INSTANCE: WidgetThemeManager? = null

        fun getInstance(context: Context): WidgetThemeManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: WidgetThemeManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }

    /**
     * 支持的主题类型
     */
    enum class ThemeType {
        LIGHT,           // 浅色主题
        DARK,            // 深色主题
        AUTO,            // 跟随系统
        CUSTOM           // 自定义主题
    }

    /**
     * Material Design 3 颜色系统
     */
    data class WidgetColorScheme(
        @ColorInt val primary: Int,
        @ColorInt val onPrimary: Int,
        @ColorInt val primaryContainer: Int,
        @ColorInt val onPrimaryContainer: Int,
        @ColorInt val secondary: Int,
        @ColorInt val onSecondary: Int,
        @ColorInt val secondaryContainer: Int,
        @ColorInt val onSecondaryContainer: Int,
        @ColorInt val tertiary: Int,
        @ColorInt val onTertiary: Int,
        @ColorInt val tertiaryContainer: Int,
        @ColorInt val onTertiaryContainer: Int,
        @ColorInt val error: Int,
        @ColorInt val onError: Int,
        @ColorInt val errorContainer: Int,
        @ColorInt val onErrorContainer: Int,
        @ColorInt val background: Int,
        @ColorInt val onBackground: Int,
        @ColorInt val surface: Int,
        @ColorInt val onSurface: Int,
        @ColorInt val surfaceVariant: Int,
        @ColorInt val onSurfaceVariant: Int,
        @ColorInt val outline: Int,
        @ColorInt val outlineVariant: Int,
        @ColorInt val scrim: Int,
        @ColorInt val inverseSurface: Int,
        @ColorInt val inverseOnSurface: Int,
        @ColorInt val inversePrimary: Int
    )

    /**
     * 字体系统
     */
    data class WidgetTypography(
        val displayLarge: TextStyle,
        val displayMedium: TextStyle,
        val displaySmall: TextStyle,
        val headlineLarge: TextStyle,
        val headlineMedium: TextStyle,
        val headlineSmall: TextStyle,
        val titleLarge: TextStyle,
        val titleMedium: TextStyle,
        val titleSmall: TextStyle,
        val bodyLarge: TextStyle,
        val bodyMedium: TextStyle,
        val bodySmall: TextStyle,
        val labelLarge: TextStyle,
        val labelMedium: TextStyle,
        val labelSmall: TextStyle
    )

    /**
     * 文本样式
     */
    data class TextStyle(
        val fontSize: Float,
        val lineHeight: Float,
        val fontWeight: FontWeight,
        val letterSpacing: Float = 0f
    )

    /**
     * 字体粗细
     */
    enum class FontWeight(val value: Int) {
        THIN(100),
        EXTRA_LIGHT(200),
        LIGHT(300),
        NORMAL(400),
        MEDIUM(500),
        SEMI_BOLD(600),
        BOLD(700),
        EXTRA_BOLD(800),
        BLACK(900)
    }

    /**
     * 获取当前主题类型
     */
    fun getCurrentThemeType(): ThemeType {
        return when {
            isSystemInDarkMode() -> ThemeType.DARK
            else -> ThemeType.LIGHT
        }
    }

    /**
     * 检测系统是否处于深色模式
     */
    fun isSystemInDarkMode(): Boolean {
        val nightModeFlags = context.resources.configuration.uiMode and Configuration.UI_MODE_NIGHT_MASK
        return nightModeFlags == Configuration.UI_MODE_NIGHT_YES
    }

    /**
     * 获取浅色主题颜色方案
     */
    fun getLightColorScheme(): WidgetColorScheme {
        return WidgetColorScheme(
            primary = getColor(R.color.widget_accent_blue),
            onPrimary = Color.WHITE,
            primaryContainer = getColor(R.color.widget_today_surface_light),
            onPrimaryContainer = getColor(R.color.widget_today_text_primary_light),
            secondary = getColor(R.color.widget_accent_teal),
            onSecondary = Color.WHITE,
            secondaryContainer = getColor(R.color.widget_background_overlay),
            onSecondaryContainer = getColor(R.color.widget_text_primary),
            tertiary = getColor(R.color.widget_accent_purple),
            onTertiary = Color.WHITE,
            tertiaryContainer = getColor(R.color.widget_gradient_start),
            onTertiaryContainer = getColor(R.color.widget_text_primary),
            error = getColor(R.color.widget_priority_high),
            onError = Color.WHITE,
            errorContainer = Color.parseColor("#FFEBEE"),
            onErrorContainer = getColor(R.color.widget_priority_high),
            background = getColor(R.color.widget_today_background_light),
            onBackground = getColor(R.color.widget_today_text_primary_light),
            surface = getColor(R.color.widget_today_surface_light),
            onSurface = getColor(R.color.widget_today_text_primary_light),
            surfaceVariant = getColor(R.color.widget_background_overlay),
            onSurfaceVariant = getColor(R.color.widget_today_text_secondary_light),
            outline = getColor(R.color.widget_today_border_light),
            outlineVariant = Color.parseColor("#E5E5EA"),
            scrim = Color.parseColor("#80000000"),
            inverseSurface = getColor(R.color.widget_today_background_dark),
            inverseOnSurface = getColor(R.color.widget_today_text_primary_dark),
            inversePrimary = getColor(R.color.widget_today_accent_dark)
        )
    }

    /**
     * 获取深色主题颜色方案
     */
    fun getDarkColorScheme(): WidgetColorScheme {
        return WidgetColorScheme(
            primary = getColor(R.color.widget_today_accent_dark),
            onPrimary = getColor(R.color.widget_today_background_dark),
            primaryContainer = getColor(R.color.widget_today_surface_dark),
            onPrimaryContainer = getColor(R.color.widget_today_text_primary_dark),
            secondary = getColor(R.color.widget_accent_teal),
            onSecondary = getColor(R.color.widget_today_background_dark),
            secondaryContainer = Color.parseColor("#2C2C2E"),
            onSecondaryContainer = getColor(R.color.widget_today_text_primary_dark),
            tertiary = getColor(R.color.widget_accent_purple),
            onTertiary = getColor(R.color.widget_today_background_dark),
            tertiaryContainer = Color.parseColor("#3C3C3E"),
            onTertiaryContainer = getColor(R.color.widget_today_text_primary_dark),
            error = getColor(R.color.widget_priority_high),
            onError = getColor(R.color.widget_today_background_dark),
            errorContainer = Color.parseColor("#5F2120"),
            onErrorContainer = Color.parseColor("#FFBAB1"),
            background = getColor(R.color.widget_today_background_dark),
            onBackground = getColor(R.color.widget_today_text_primary_dark),
            surface = getColor(R.color.widget_today_surface_dark),
            onSurface = getColor(R.color.widget_today_text_primary_dark),
            surfaceVariant = Color.parseColor("#48484A"),
            onSurfaceVariant = getColor(R.color.widget_today_text_secondary_dark),
            outline = getColor(R.color.widget_today_border_dark),
            outlineVariant = Color.parseColor("#48484A"),
            scrim = Color.parseColor("#80000000"),
            inverseSurface = getColor(R.color.widget_today_background_light),
            inverseOnSurface = getColor(R.color.widget_today_text_primary_light),
            inversePrimary = getColor(R.color.widget_today_accent_light)
        )
    }

    /**
     * 获取当前颜色方案
     */
    fun getCurrentColorScheme(): WidgetColorScheme {
        return when (getCurrentThemeType()) {
            ThemeType.DARK -> getDarkColorScheme()
            else -> getLightColorScheme()
        }
    }

    /**
     * 获取Material Design 3字体系统
     */
    fun getTypography(): WidgetTypography {
        return WidgetTypography(
            displayLarge = TextStyle(
                fontSize = 57f,
                lineHeight = 64f,
                fontWeight = FontWeight.NORMAL,
                letterSpacing = -0.25f
            ),
            displayMedium = TextStyle(
                fontSize = 45f,
                lineHeight = 52f,
                fontWeight = FontWeight.NORMAL
            ),
            displaySmall = TextStyle(
                fontSize = 36f,
                lineHeight = 44f,
                fontWeight = FontWeight.NORMAL
            ),
            headlineLarge = TextStyle(
                fontSize = 32f,
                lineHeight = 40f,
                fontWeight = FontWeight.NORMAL
            ),
            headlineMedium = TextStyle(
                fontSize = 28f,
                lineHeight = 36f,
                fontWeight = FontWeight.NORMAL
            ),
            headlineSmall = TextStyle(
                fontSize = 24f,
                lineHeight = 32f,
                fontWeight = FontWeight.NORMAL
            ),
            titleLarge = TextStyle(
                fontSize = 22f,
                lineHeight = 28f,
                fontWeight = FontWeight.NORMAL
            ),
            titleMedium = TextStyle(
                fontSize = 16f,
                lineHeight = 24f,
                fontWeight = FontWeight.MEDIUM,
                letterSpacing = 0.15f
            ),
            titleSmall = TextStyle(
                fontSize = 14f,
                lineHeight = 20f,
                fontWeight = FontWeight.MEDIUM,
                letterSpacing = 0.1f
            ),
            bodyLarge = TextStyle(
                fontSize = 16f,
                lineHeight = 24f,
                fontWeight = FontWeight.NORMAL,
                letterSpacing = 0.5f
            ),
            bodyMedium = TextStyle(
                fontSize = 14f,
                lineHeight = 20f,
                fontWeight = FontWeight.NORMAL,
                letterSpacing = 0.25f
            ),
            bodySmall = TextStyle(
                fontSize = 12f,
                lineHeight = 16f,
                fontWeight = FontWeight.NORMAL,
                letterSpacing = 0.4f
            ),
            labelLarge = TextStyle(
                fontSize = 14f,
                lineHeight = 20f,
                fontWeight = FontWeight.MEDIUM,
                letterSpacing = 0.1f
            ),
            labelMedium = TextStyle(
                fontSize = 12f,
                lineHeight = 16f,
                fontWeight = FontWeight.MEDIUM,
                letterSpacing = 0.5f
            ),
            labelSmall = TextStyle(
                fontSize = 11f,
                lineHeight = 16f,
                fontWeight = FontWeight.MEDIUM,
                letterSpacing = 0.5f
            )
        )
    }

    /**
     * 获取优先级颜色
     */
    fun getPriorityColor(priority: String): Int {
        return when (priority.uppercase()) {
            "HIGH", "URGENT" -> getColor(R.color.widget_priority_high)
            "MEDIUM", "NORMAL" -> getColor(R.color.widget_priority_medium)
            "LOW" -> getColor(R.color.widget_priority_low)
            else -> getCurrentColorScheme().primary
        }
    }

    /**
     * 获取状态颜色
     */
    fun getStatusColor(status: String): Int {
        return when (status.uppercase()) {
            "COMPLETED" -> getColor(R.color.widget_task_completed)
            "PENDING" -> getColor(R.color.widget_task_pending)
            "OVERDUE" -> getColor(R.color.widget_task_overdue)
            "RUNNING" -> getColor(R.color.widget_accent_green)
            "PAUSED" -> getColor(R.color.widget_accent_orange)
            "STOPPED" -> getColor(R.color.widget_text_tertiary)
            else -> getCurrentColorScheme().onSurfaceVariant
        }
    }

    /**
     * 获取图表颜色
     */
    fun getChartColors(): List<Int> {
        return listOf(
            getColor(R.color.widget_chart_work),
            getColor(R.color.widget_chart_focus),
            getColor(R.color.widget_chart_health),
            getColor(R.color.widget_chart_entertainment),
            getColor(R.color.widget_chart_reading),
            getColor(R.color.widget_chart_sleep)
        )
    }

    /**
     * 获取渐变颜色对
     */
    fun getGradientColors(type: String): Pair<Int, Int> {
        return when (type.uppercase()) {
            "BLUE" -> Pair(
                getColor(R.color.widget_gradient_blue_start),
                getColor(R.color.widget_gradient_blue_end)
            )
            "GREEN" -> Pair(
                getColor(R.color.widget_gradient_green_start),
                getColor(R.color.widget_gradient_green_end)
            )
            "ORANGE" -> Pair(
                getColor(R.color.widget_gradient_orange_start),
                getColor(R.color.widget_gradient_orange_end)
            )
            "PURPLE" -> Pair(
                getColor(R.color.widget_gradient_purple_start),
                getColor(R.color.widget_gradient_purple_end)
            )
            "TIMETRACKING" -> Pair(
                getColor(R.color.widget_timetracking_background_start),
                getColor(R.color.widget_timetracking_background_end)
            )
            else -> Pair(
                getColor(R.color.widget_gradient_start),
                getColor(R.color.widget_gradient_end)
            )
        }
    }

    /**
     * 应用透明度
     */
    fun applyAlpha(@ColorInt color: Int, alpha: Float): Int {
        val alphaInt = (alpha * 255).toInt().coerceIn(0, 255)
        return Color.argb(alphaInt, Color.red(color), Color.green(color), Color.blue(color))
    }

    /**
     * 获取对比色
     */
    fun getContrastColor(@ColorInt backgroundColor: Int): Int {
        val luminance = (0.299 * Color.red(backgroundColor) + 
                        0.587 * Color.green(backgroundColor) + 
                        0.114 * Color.blue(backgroundColor)) / 255
        return if (luminance > 0.5) Color.BLACK else Color.WHITE
    }

    /**
     * 辅助方法：获取颜色资源
     */
    @ColorInt
    private fun getColor(@ColorRes colorRes: Int): Int {
        return ContextCompat.getColor(context, colorRes)
    }

    /**
     * 创建自定义颜色方案
     */
    fun createCustomColorScheme(
        primaryColor: Int,
        secondaryColor: Int? = null,
        backgroundColor: Int? = null
    ): WidgetColorScheme {
        val baseScheme = getCurrentColorScheme()
        val secondary = secondaryColor ?: generateSecondaryColor(primaryColor)
        val background = backgroundColor ?: baseScheme.background

        return baseScheme.copy(
            primary = primaryColor,
            onPrimary = getContrastColor(primaryColor),
            secondary = secondary,
            onSecondary = getContrastColor(secondary),
            background = background,
            onBackground = getContrastColor(background)
        )
    }

    /**
     * 生成次要颜色
     */
    private fun generateSecondaryColor(primaryColor: Int): Int {
        val hsv = FloatArray(3)
        Color.colorToHSV(primaryColor, hsv)
        hsv[0] = (hsv[0] + 30) % 360 // 色相偏移30度
        hsv[1] = hsv[1] * 0.8f // 降低饱和度
        return Color.HSVToColor(hsv)
    }
}